
"use client"

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { PageLayout } from '@/components/layouts/PageLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'





import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  AlertCircle,
  Plus,
  Star,
  Clock,
  AlertCircle
} from 'lucide-react'

// أنواع البيانات
interface SupportTicket {
  id: string
  subject: string
  description: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  category: 'order' | 'payment' | 'technical' | 'general'
  createdAt: string
  updatedAt: string
  responses: SupportResponse[]
}

interface SupportResponse {
  id: string
  message: string
  isFromSupport: boolean
  timestamp: string
  attachments?: string[]
}

interface FAQ {
  id: string
  question: string
  answer: string
  category: string
  helpful: number
  notHelpful: number
}

export default function SupportPage() {
  const { user, profile } = useAuth()
  const [activeTab, setActiveTab] = useState('contact')
  const [searchTerm, setSearchTerm] = useState('')
  const [newTicket, setNewTicket] = useState({
    subject: '',
    description: '',
    category: 'general' as const,
    priority: 'medium' as const
  })

  // بيانات وهمية
  const supportTickets: SupportTicket[] = [
    {
      id: 'TKT-001',
      subject: 'مشكلة في تتبع الطلب',
      description: 'لا أستطيع تتبع طلبي رقم GT-240120-001',
      status: 'in_progress',
      priority: 'medium',
      category: 'order',
      createdAt: '2024-01-20T10:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z',
      responses: [
        {
          id: '1',
          message: 'شكراً لتواصلك معنا. سنقوم بالتحقق من حالة طلبك.',
          isFromSupport: true,
          timestamp: '2024-01-20T11:00:00Z'
        },
        {
          id: '2',
          message: 'تم العثور على طلبك وهو قيد الشحن. رقم التتبع: TRK-123456',
          isFromSupport: true,
          timestamp: '2024-01-20T14:30:00Z'
        }
      ]
    },
    {
      id: 'TKT-002',
      subject: 'استفسار عن المقاسات',
      description: 'أريد معرفة جدول المقاسات لأزياء التخرج',
      status: 'resolved',
      priority: 'low',
      category: 'general',
      createdAt: '2024-01-19T15:00:00Z',
      updatedAt: '2024-01-19T16:00:00Z',
      responses: [
        {
          id: '3',
          message: 'يمكنك العثور على جدول المقاسات في صفحة المنتج أو في دليل المقاسات.',
          isFromSupport: true,
          timestamp: '2024-01-19T16:00:00Z'
        }
      ]
    }
  ]

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'كم يستغرق تسليم الطلب؟',
      answer: 'عادة ما يستغرق التسليم من 3-5 أيام عمل للتوصيل العادي، و1-2 أيام للتوصيل السريع.',
      category: 'shipping',
      helpful: 45,
      notHelpful: 3
    },
    {
      id: '2',
      question: 'هل يمكنني تغيير أو إلغاء طلبي؟',
      answer: 'يمكنك تغيير أو إلغاء طلبك خلال 24 ساعة من تأكيد الطلب. بعد ذلك، يصبح الطلب قيد المعالجة.',
      category: 'orders',
      helpful: 32,
      notHelpful: 5
    },
    {
      id: '3',
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نقبل بطاقات الائتمان والخصم، والدفع عند الاستلام، والتحويل البنكي.',
      category: 'payment',
      helpful: 28,
      notHelpful: 2
    },
    {
      id: '4',
      question: 'كيف يمكنني تتبع طلبي؟',
      answer: 'يمكنك تتبع طلبك من خلال صفحة "تتبع الطلب" باستخدام رقم الطلب أو رقم التتبع.',
      category: 'orders',
      helpful: 67,
      notHelpful: 1
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open': return 'مفتوح'
      case 'in_progress': return 'قيد المعالجة'
      case 'resolved': return 'تم الحل'
      case 'closed': return 'مغلق'
      default: return 'غير معروف'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleSubmitTicket = () => {
    // محاكاة إرسال تذكرة جديدة
    console.log('New ticket:', newTicket)
    alert('تم إرسال طلب الدعم بنجاح! سنتواصل معك قريباً.')
    setNewTicket({
      subject: '',
      description: '',
      category: 'general',
      priority: 'medium'
    })
  }

  const filteredFAQs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            الدعم الفني 🎧
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            نحن هنا لمساعدتك في أي وقت
          </p>
        </div>

        {/* Quick Contact Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="text-center">
            <CardContent className="p-6">
              <Phone className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2 arabic-text">اتصل بنا</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text">
                متاح 24/7 لخدمتك
              </p>
              <p className="font-mono text-lg">+971 4 123 4567</p>
              <Button className="mt-3 arabic-text">
                <Phone className="h-4 w-4 mr-2" />
                اتصل الآن
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <Mail className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2 arabic-text">راسلنا</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text">
                سنرد خلال 24 ساعة
              </p>
              <p className="text-sm"><EMAIL></p>
              <Button variant="outline" className="mt-3 arabic-text">
                <Mail className="h-4 w-4 mr-2" />
                إرسال بريد
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <MessageSquare className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2 arabic-text">دردشة مباشرة</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text">
                تحدث مع فريق الدعم
              </p>
              <Badge className="bg-green-100 text-green-800 mb-3">متاح الآن</Badge>
              <br />
              <Button variant="outline" className="arabic-text">
                <MessageSquare className="h-4 w-4 mr-2" />
                بدء المحادثة
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="contact" className="arabic-text">
              <MessageSquare className="h-4 w-4 mr-2" />
              تواصل معنا
            </TabsTrigger>
            <TabsTrigger value="tickets" className="arabic-text">
              <AlertCircle className="h-4 w-4 mr-2" />
              طلبات الدعم
            </TabsTrigger>
            <TabsTrigger value="faq" className="arabic-text">
              <Search className="h-4 w-4 mr-2" />
              الأسئلة الشائعة
            </TabsTrigger>
            <TabsTrigger value="guides" className="arabic-text">
              <ExternalLink className="h-4 w-4 mr-2" />
              أدلة المساعدة
            </TabsTrigger>
          </TabsList>

          {/* Contact Form */}
          <TabsContent value="contact" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إرسال طلب دعم جديد</CardTitle>
                <CardDescription className="arabic-text">
                  املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category" className="arabic-text">فئة المشكلة</Label>
                    <select
                      id="category"
                      value={newTicket.category}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, category: e.target.value as any }))}
                      className="w-full p-2 border rounded-md arabic-text"
                    >
                      <option value="general">عام</option>
                      <option value="order">الطلبات</option>
                      <option value="payment">الدفع</option>
                      <option value="technical">مشكلة تقنية</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="priority" className="arabic-text">الأولوية</Label>
                    <select
                      id="priority"
                      value={newTicket.priority}
                      onChange={(e) => setNewTicket(prev => ({ ...prev, priority: e.target.value as any }))}
                      className="w-full p-2 border rounded-md arabic-text"
                    >
                      <option value="low">منخفضة</option>
                      <option value="medium">متوسطة</option>
                      <option value="high">عالية</option>
                      <option value="urgent">عاجلة</option>
                    </select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="subject" className="arabic-text">موضوع الرسالة</Label>
                  <Input
                    id="subject"
                    value={newTicket.subject}
                    onChange={(e) => setNewTicket(prev => ({ ...prev, subject: e.target.value }))}
                    placeholder="اكتب موضوع رسالتك..."
                    className="arabic-text"
                  />
                </div>

                <div>
                  <Label htmlFor="description" className="arabic-text">تفاصيل المشكلة</Label>
                  <Textarea
                    id="description"
                    value={newTicket.description}
                    onChange={(e) => setNewTicket(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="اشرح مشكلتك بالتفصيل..."
                    rows={5}
                    className="arabic-text"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Button variant="outline" className="arabic-text">
                    <Paperclip className="h-4 w-4 mr-2" />
                    إرفاق ملف
                  </Button>
                  <span className="text-sm text-gray-500 arabic-text">
                    يمكنك إرفاق صور أو مستندات (حد أقصى 10 ميجا)
                  </span>
                </div>

                <Button 
                  onClick={handleSubmitTicket}
                  disabled={!newTicket.subject || !newTicket.description}
                  className="w-full arabic-text"
                >
                  <Send className="h-4 w-4 mr-2" />
                  إرسال طلب الدعم
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Support Tickets */}
          <TabsContent value="tickets" className="space-y-6 mt-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold arabic-text">طلبات الدعم الخاصة بك</h2>
              <Button onClick={() => setActiveTab('contact')} className="arabic-text">
                <Plus className="h-4 w-4 mr-2" />
                طلب دعم جديد
              </Button>
            </div>

            <div className="space-y-4">
              {supportTickets.map((ticket) => (
                <Card key={ticket.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="arabic-text">{ticket.subject}</CardTitle>
                        <CardDescription className="arabic-text">
                          رقم التذكرة: {ticket.id}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Badge className={getStatusColor(ticket.status)}>
                          {getStatusText(ticket.status)}
                        </Badge>
                        <Badge className={getPriorityColor(ticket.priority)}>
                          {ticket.priority === 'urgent' ? 'عاجل' :
                           ticket.priority === 'high' ? 'عالي' :
                           ticket.priority === 'medium' ? 'متوسط' : 'منخفض'}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-400 mb-4 arabic-text">
                      {ticket.description}
                    </p>

                    {/* Responses */}
                    <div className="space-y-3 mb-4">
                      {ticket.responses.map((response) => (
                        <div
                          key={response.id}
                          className={`p-3 rounded-lg ${
                            response.isFromSupport
                              ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'
                              : 'bg-gray-50 dark:bg-gray-800 border-l-4 border-gray-300'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <span className="font-medium arabic-text">
                              {response.isFromSupport ? 'فريق الدعم' : 'أنت'}
                            </span>
                            <span className="text-sm text-gray-500">
                              {new Date(response.timestamp).toLocaleString('ar-SA')}
                            </span>
                          </div>
                          <p className="text-sm arabic-text">{response.message}</p>
                        </div>
                      ))}
                    </div>

                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>
                        تم الإنشاء: {new Date(ticket.createdAt).toLocaleDateString('ar-SA')}
                      </span>
                      <span>
                        آخر تحديث: {new Date(ticket.updatedAt).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* FAQ */}
          <TabsContent value="faq" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الأسئلة الشائعة</CardTitle>
                <CardDescription className="arabic-text">
                  ابحث في الأسئلة الشائعة للحصول على إجابات سريعة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative mb-6">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="ابحث في الأسئلة الشائعة..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 arabic-text"
                  />
                </div>

                <div className="space-y-4">
                  {filteredFAQs.map((faq) => (
                    <Card key={faq.id}>
                      <CardContent className="p-4">
                        <h3 className="font-medium mb-2 arabic-text">{faq.question}</h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text">
                          {faq.answer}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <span className="text-sm text-gray-500 arabic-text">
                              هل كانت هذه الإجابة مفيدة؟
                            </span>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="sm">
                                <ThumbsUp className="h-4 w-4 mr-1" />
                                {faq.helpful}
                              </Button>
                              <Button variant="ghost" size="sm">
                                <ThumbsDown className="h-4 w-4 mr-1" />
                                {faq.notHelpful}
                              </Button>
                            </div>
                          </div>
                          <Badge variant="outline">{faq.category}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Guides */}
          <TabsContent value="guides" className="space-y-6 mt-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">دليل الطلبات</CardTitle>
                  <CardDescription className="arabic-text">
                    تعلم كيفية إنشاء وإدارة طلباتك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm arabic-text">
                    <li>• كيفية إنشاء طلب جديد</li>
                    <li>• تتبع حالة الطلب</li>
                    <li>• تعديل أو إلغاء الطلب</li>
                    <li>• فهم حالات الطلب</li>
                  </ul>
                  <Button variant="outline" className="mt-4 arabic-text">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    عرض الدليل
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">دليل الدفع</CardTitle>
                  <CardDescription className="arabic-text">
                    معلومات حول طرق الدفع والفواتير
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm arabic-text">
                    <li>• طرق الدفع المتاحة</li>
                    <li>• أمان المعاملات</li>
                    <li>• الفواتير والإيصالات</li>
                    <li>• استرداد الأموال</li>
                  </ul>
                  <Button variant="outline" className="mt-4 arabic-text">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    عرض الدليل
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">دليل التخصيص</CardTitle>
                  <CardDescription className="arabic-text">
                    كيفية تخصيص أزياء التخرج
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm arabic-text">
                    <li>• اختيار الألوان والأحجام</li>
                    <li>• إضافة التطريز والنصوص</li>
                    <li>• حفظ ومشاركة التصاميم</li>
                    <li>• نصائح للتصميم المثالي</li>
                  </ul>
                  <Button variant="outline" className="mt-4 arabic-text">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    عرض الدليل
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">دليل الحساب</CardTitle>
                  <CardDescription className="arabic-text">
                    إدارة حسابك وإعداداتك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm arabic-text">
                    <li>• إنشاء وتحديث الملف الشخصي</li>
                    <li>• إعدادات الأمان</li>
                    <li>• إدارة العناوين</li>
                    <li>• تفضيلات الإشعارات</li>
                  </ul>
                  <Button variant="outline" className="mt-4 arabic-text">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    عرض الدليل
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
    </PageLayout>
  )
}
