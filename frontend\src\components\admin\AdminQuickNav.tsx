
"use client"

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'

  Plus,
  Menu,
  Package,
  Download,
  FileText,
  Users,
  Settings
} from 'lucide-react'

interface QuickNavItem {
  label: string
  href?: string
  icon: React.ReactNode
  variant?: 'default' | 'outline' | 'secondary'
  disabled?: boolean
}

const quickNavItems: QuickNavItem[] = [
  {
    label: 'إضافة صفحة',
    href: '/dashboard/admin/pages-management',
    icon: <Plus className="h-4 w-4 mr-2" />,
    variant: 'outline'
  },
  {
    label: 'تحرير القائمة',
    href: '/dashboard/admin/menu-management',
    icon: <Menu className="h-4 w-4 mr-2" />,
    variant: 'outline'
  },
  {
    label: 'إدارة المنتجات',
    href: '/dashboard/admin/products',
    icon: <Package className="h-4 w-4 mr-2" />,
    variant: 'outline'
  },
  {
    label: 'تصدير التقارير',
    icon: <Download className="h-4 w-4 mr-2" />,
    variant: 'outline',
    disabled: true
  }
]

export function AdminQuickNav() {
  return (
    <div className="flex gap-3 flex-wrap">
      {quickNavItems.map((item, index) => {
        const ButtonComponent = (
          <Button 
            key={index}
            variant={item.variant || 'outline'} 
            size="sm"
            disabled={item.disabled}
            className="arabic-text"
          >
            {item.icon}
            {item.label}
          </Button>
        )

        return item.href && !item.disabled ? (
          <Link key={index} href={item.href}>
            {ButtonComponent}
          </Link>
        ) : (
          ButtonComponent
        )
      })}
    </div>
  )
}
