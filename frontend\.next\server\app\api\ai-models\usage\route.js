(()=>{var e={};e.id=451,e.ids=[451],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},81182:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>p,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var a={};s.r(a),s.d(a,{GET:()=>i});var r=s(96559),o=s(48088),u=s(37719),n=s(32190),l=s(38561);async function i(e){try{let{searchParams:t}=new URL(e.url),s=t.get("model_id"),a=t.get("provider"),r=t.get("date_from"),o=t.get("date_to"),u=t.get("type"),i=t.get("group_by")||"day",c="true"===t.get("include_details"),d=l.C.getAIModels(),g=l.C.getModelActivities(),m=d;s&&(m=d.filter(e=>e.id===s)),a&&(m=m.filter(e=>e.provider===a)),u&&(m=m.filter(e=>e.type===u));let p=g.filter(e=>"request"===e.type);s&&(p=p.filter(e=>e.modelId===s)),r&&(p=p.filter(e=>e.timestamp>=r)),o&&(p=p.filter(e=>e.timestamp<=o));let R={totalRequests:m.reduce((e,t)=>e+t.usage.totalRequests,0),totalTokens:m.reduce((e,t)=>e+t.usage.totalTokens,0),totalCost:m.reduce((e,t)=>e+t.usage.totalCost,0),averageResponseTime:m.length>0?Math.round(m.reduce((e,t)=>e+t.usage.averageResponseTime,0)/m.length):0,successRate:m.length>0?Math.round(m.reduce((e,t)=>e+t.usage.successRate,0)/m.length):0},x=m.reduce((e,t)=>{let s=t.provider;return e[s]||(e[s]={models:0,totalRequests:0,totalTokens:0,totalCost:0,averageResponseTime:0,successRate:0}),e[s].models+=1,e[s].totalRequests+=t.usage.totalRequests,e[s].totalTokens+=t.usage.totalTokens,e[s].totalCost+=t.usage.totalCost,e[s].averageResponseTime+=t.usage.averageResponseTime,e[s].successRate+=t.usage.successRate,e},{});Object.keys(x).forEach(e=>{let t=x[e];t.averageResponseTime=Math.round(t.averageResponseTime/t.models),t.successRate=Math.round(t.successRate/t.models)});let h=m.reduce((e,t)=>{let s=t.type;return e[s]||(e[s]={models:0,totalRequests:0,totalTokens:0,totalCost:0}),e[s].models+=1,e[s].totalRequests+=t.usage.totalRequests,e[s].totalTokens+=t.usage.totalTokens,e[s].totalCost+=t.usage.totalCost,e},{}),q=function(e,t,s,a){let r=new Date,o=s?new Date(s):r,u=new Date(t||r.getTime()-2592e6),n=[],l=new Date(u);for(;l<=o;){let t=l.toISOString().split("T")[0],s=Math.floor(100*Math.random())+10,a=s*(Math.floor(500*Math.random())+100),r=.001*a*(.01*Math.random()+.001),o=Math.floor(5*Math.random());n.push({date:t,requests:s,tokens:a,cost:Math.round(100*r)/100,errors:o,successRate:Math.round((s-o)/s*100)}),"day"===e?l.setDate(l.getDate()+1):"week"===e?l.setDate(l.getDate()+7):"month"===e&&l.setMonth(l.getMonth()+1)}return n}(i,r,o,0),b=m.filter(e=>e.usage.totalRequests>0).sort((e,t)=>t.usage.totalRequests-e.usage.totalRequests).slice(0,10).map(e=>({id:e.id,name:e.name,provider:e.provider,type:e.type,totalRequests:e.usage.totalRequests,totalCost:e.usage.totalCost,successRate:e.usage.successRate,averageResponseTime:e.usage.averageResponseTime,lastUsed:e.usage.lastUsed})),f=m.filter(e=>e.usage.totalCost>0).sort((e,t)=>t.usage.totalCost-e.usage.totalCost).slice(0,5).map(e=>({id:e.id,name:e.name,provider:e.provider,totalCost:e.usage.totalCost,totalRequests:e.usage.totalRequests,costPerRequest:e.usage.totalRequests>0?e.usage.totalCost/e.usage.totalRequests:0})),v=function(e){if(e.length<2)return{requests:"stable",cost:"stable",successRate:"stable"};let t=e.slice(-7),s=e.slice(-14,-7),a={requests:t.reduce((e,t)=>e+t.requests,0)/t.length,cost:t.reduce((e,t)=>e+t.cost,0)/t.length,successRate:t.reduce((e,t)=>e+t.successRate,0)/t.length},r={requests:s.reduce((e,t)=>e+t.requests,0)/s.length,cost:s.reduce((e,t)=>e+t.cost,0)/s.length,successRate:s.reduce((e,t)=>e+t.successRate,0)/s.length},o=(e,t)=>{let s=(e-t)/t*100;return s>10?"increasing":s<-10?"decreasing":"stable"};return{requests:o(a.requests,r.requests),cost:o(a.cost,r.cost),successRate:o(a.successRate,r.successRate),changes:{requests:Math.round((a.requests-r.requests)/r.requests*100),cost:Math.round((a.cost-r.cost)/r.cost*100),successRate:Math.round((a.successRate-r.successRate)/r.successRate*100)}}}(q),T={summary:R,byProvider:x,byType:h,timeSeries:q,topModels:b,mostExpensive:f,trends:v,period:{from:r||"all-time",to:o||new Date().toISOString(),groupBy:i}};return c&&(T.models=m.map(e=>({id:e.id,name:e.name,provider:e.provider,type:e.type,status:e.status,usage:e.usage,lastTestedAt:e.lastTestedAt,testResult:e.testResult})),T.recentActivities=p.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,50)),n.NextResponse.json(T)}catch(e){return n.NextResponse.json({error:"خطأ في جلب إحصائيات الاستخدام"},{status:500})}}let c=new r.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/ai-models/usage/route",pathname:"/api/ai-models/usage",filename:"route",bundlePath:"app/api/ai-models/usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\usage\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:m}=c;function p(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,580],()=>s(81182));module.exports=a})();