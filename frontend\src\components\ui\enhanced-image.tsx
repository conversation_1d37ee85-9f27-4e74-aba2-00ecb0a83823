
'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from './button'
import {
  Loader2,
  ImageOff,
  RefreshCw
} from 'lucide-react'

interface EnhancedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  fallbackSrc?: string
  showRetry?: boolean
  lazy?: boolean
  onLoad?: () => void
  onError?: (error: string) => void
}

export function EnhancedImage({
  src,
  alt,
  className = "",
  width,
  height,
  fallbackSrc,
  showRetry = true,
  lazy = true,
  onLoad,
  onError
}: EnhancedImageProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentSrc, setCurrentSrc] = useState(src)
  const [retryCount, setRetryCount] = useState(0)
  const imgRef = useRef<HTMLImageElement>(null)
  const [inView, setInView] = useState(!lazy)

  // Intersection Observer للـ lazy loading
  useEffect(() => {
    if (!lazy || inView) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [lazy, inView])

  // إعادة تعيين الحالة عند تغيير المصدر
  useEffect(() => {
    setCurrentSrc(src)
    setError(null)
    setLoading(true)
    setRetryCount(0)
  }, [src])

  const handleLoad = () => {
    setLoading(false)
    setError(null)
    onLoad?.()
  }

  const handleError = () => {
    setLoading(false)
    
    // محاولة استخدام الصورة البديلة
    if (fallbackSrc && currentSrc !== fallbackSrc && retryCount === 0) {
      setCurrentSrc(fallbackSrc)
      setRetryCount(1)
      return
    }

    // محاولة استخدام placeholder API
    if (!currentSrc.includes('/api/placeholder/') && retryCount < 2) {
      const dimensions = width && height ? `${width}/${height}` : '400/300'
      setCurrentSrc(`/api/placeholder/${dimensions}`)
      setRetryCount(prev => prev + 1)
      return
    }

    const errorMsg = `فشل في تحميل الصورة: ${alt}`
    setError(errorMsg)
    onError?.(errorMsg)
  }

  const handleRetry = () => {
    setError(null)
    setLoading(true)
    setRetryCount(0)
    setCurrentSrc(src)
  }

  // عرض placeholder أثناء انتظار lazy loading
  if (lazy && !inView) {
    return (
      <div 
        ref={imgRef}
        className={`bg-gray-200 dark:bg-gray-700 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <div className="text-gray-400 text-center">
          <ImageOff className="h-8 w-8 mx-auto mb-2" />
          <span className="text-sm">جاري التحميل...</span>
        </div>
      </div>
    )
  }

  // عرض حالة الخطأ
  if (error) {
    return (
      <div 
        className={`bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 flex flex-col items-center justify-center p-4 ${className}`}
        style={{ width, height }}
      >
        <ImageOff className="h-12 w-12 text-gray-400 mb-3" />
        <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-3 arabic-text">
          فشل في تحميل الصورة
        </p>
        <p className="text-xs text-gray-500 text-center mb-3 arabic-text">
          {alt}
        </p>
        {showRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            className="text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            إعادة المحاولة
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {/* مؤشر التحميل */}
      {loading && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-2" />
            <span className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
              جاري التحميل...
            </span>
          </div>
        </div>
      )}
      
      {/* الصورة */}
      <img
        ref={imgRef}
        src={currentSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          loading ? 'opacity-0' : 'opacity-100'
        }`}
        onLoad={handleLoad}
        onError={handleError}
        loading={lazy ? 'lazy' : 'eager'}
      />
      
      {/* مؤشر إعادة المحاولة */}
      {retryCount > 0 && !loading && !error && (
        <div className="absolute top-2 right-2">
          <div className="bg-orange-500 text-white text-xs px-2 py-1 rounded">
            محاولة {retryCount + 1}
          </div>
        </div>
      )}
    </div>
  )
}

// مكون مبسط للصور مع معالجة أخطاء أساسية
export function SimpleImage({ 
  src, 
  alt, 
  className = "",
  fallback = "/api/placeholder/400/300",
  ...props 
}: {
  src: string
  alt: string
  className?: string
  fallback?: string
  [key: string]: any
}) {
  const [imgSrc, setImgSrc] = useState(src)
  const [hasError, setHasError] = useState(false)

  const handleError = () => {
    if (!hasError && imgSrc !== fallback) {
      setImgSrc(fallback)
      setHasError(true)
    }
  }

  useEffect(() => {
    setImgSrc(src)
    setHasError(false)
  }, [src])

  return (
    <img
      src={imgSrc}
      alt={alt}
      className={className}
      onError={handleError}
      loading="lazy"
      {...props}
    />
  )
}
