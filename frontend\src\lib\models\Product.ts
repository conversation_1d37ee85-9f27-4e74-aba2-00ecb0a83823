import { query, transaction } from '../database'
import { PoolClient } from 'pg'


export interface Product {
  id: string
  name: string
  description?: string
  category_id?: string
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  is_published: boolean
  features: string[]
  specifications: Record<string, unknown>
  rating: number
  reviews_count: number
  created_at: Date
  updated_at: Date
}

export interface ProductFilters {
  category?: string
  available?: boolean
  published?: boolean
  search?: string
  minPrice?: number
  maxPrice?: number
  limit?: number
  offset?: number
  sortBy?: 'name' | 'price' | 'rating' | 'created_at'
  sortOrder?: 'ASC' | 'DESC'
}

export class ProductModel {
  // جلب جميع المنتجات مع الفلترة
  static async getAll(filters: ProductFilters = {}): Promise<{ products: Product[], total: number }> {
    const whereConditions: string[] = []
    const params: unknown[] = []
    let paramIndex = 1

    // بناء شروط WHERE
    if (filters.category) {
      whereConditions.push(`category_id = $${paramIndex}`)
      params.push(filters.category)
      paramIndex++
    }

    if (filters.available !== undefined) {
      whereConditions.push(`is_available = $${paramIndex}`)
      params.push(filters.available)
      paramIndex++
    }

    if (filters.published !== undefined) {
      whereConditions.push(`is_published = $${paramIndex}`)
      params.push(filters.published)
      paramIndex++
    }

    if (filters.search) {
      whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`)
      params.push(`%${filters.search}%`)
      paramIndex++
    }

    if (filters.minPrice !== undefined) {
      whereConditions.push(`price >= $${paramIndex}`)
      params.push(filters.minPrice)
      paramIndex++
    }

    if (filters.maxPrice !== undefined) {
      whereConditions.push(`price <= $${paramIndex}`)
      params.push(filters.maxPrice)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // ترتيب النتائج
    const sortBy = filters.sortBy || 'created_at'
    const sortOrder = filters.sortOrder || 'DESC'
    const orderClause = `ORDER BY ${sortBy} ${sortOrder}`

    // حد النتائج والإزاحة
    const limit = filters.limit || 50
    const offset = filters.offset || 0
    const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    params.push(limit, offset)

    // استعلام العد الكلي
    const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`
    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)

    // استعلام البيانات
    const dataQuery = `
      SELECT 
        id, name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count, created_at, updated_at
      FROM products 
      ${whereClause} 
      ${orderClause} 
      ${limitClause}
    `
    
    const result = await query(dataQuery, params)
    
    return {
      products: result.rows,
      total
    }
  }

  // جلب منتج واحد بالمعرف
  static async getById(id: string): Promise<Product | null> {
    const result = await query(
      'SELECT * FROM products WHERE id = $1',
      [id]
    )
    
    return result.rows[0] || null
  }

  // إنشاء منتج جديد
  static async create(productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> {
    const result = await query(`
      INSERT INTO products (
        name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
      ) RETURNING *
    `, [
      productData.name,
      productData.description,
      productData.category_id,
      productData.price,
      productData.rental_price,
      productData.colors,
      productData.sizes,
      productData.images,
      productData.stock_quantity,
      productData.is_available,
      productData.is_published,
      productData.features,
      JSON.stringify(productData.specifications),
      productData.rating || 0,
      productData.reviews_count || 0
    ])

    return result.rows[0]
  }

  // تحديث منتج
  static async update(id: string, updates: Partial<Product>): Promise<Product | null> {
    const setClause: string[] = []
    const params: unknown[] = []
    let paramIndex = 1

    // بناء جملة SET ديناميكياً
    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'id' && key !== 'created_at' && value !== undefined) {
        if (key === 'specifications') {
          setClause.push(`${key} = $${paramIndex}`)
          params.push(JSON.stringify(value))
        } else {
          setClause.push(`${key} = $${paramIndex}`)
          params.push(value)
        }
        paramIndex++
      }
    })

    if (setClause.length === 0) {
      throw new Error('لا توجد حقول للتحديث')
    }

    // إضافة updated_at
    setClause.push(`updated_at = NOW()`)
    params.push(id)

    const result = await query(`
      UPDATE products 
      SET ${setClause.join(', ')} 
      WHERE id = $${paramIndex} 
      RETURNING *
    `, params)

    return result.rows[0] || null
  }

  // حذف منتج
  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM products WHERE id = $1',
      [id]
    )

    return result.rowCount > 0
  }

  // تحديث تقييم المنتج
  static async updateRating(productId: string): Promise<void> {
    await query(`
      UPDATE products 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE product_id = $1
        ),
        reviews_count = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE product_id = $1
        ),
        updated_at = NOW()
      WHERE id = $1
    `, [productId])
  }

  // تحديث المخزون
  static async updateStock(productId: string, quantity: number): Promise<boolean> {
    return await transaction(async (client: PoolClient) => {
      // التحقق من المخزون الحالي
      const stockResult = await client.query(
        'SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE',
        [productId]
      )

      if (stockResult.rows.length === 0) {
        throw new Error('المنتج غير موجود')
      }

      const currentStock = stockResult.rows[0].stock_quantity
      const newStock = currentStock + quantity

      if (newStock < 0) {
        throw new Error('المخزون غير كافي')
      }

      // تحديث المخزون
      const updateResult = await client.query(
        'UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2',
        [newStock, productId]
      )

      return updateResult.rowCount > 0
    })
  }

  // البحث في المنتجات
  static async search(searchTerm: string, limit: number = 20): Promise<Product[]> {
    const result = await query(`
      SELECT * FROM products 
        is_published = true 
        AND is_available = true 
        AND (
          name ILIKE $1 
          OR description ILIKE $1 
          OR $2 = ANY(features)
        )
      ORDER BY 
          WHEN name ILIKE $1 THEN 1
          WHEN description ILIKE $1 THEN 2
          ELSE 3
        rating DESC
      LIMIT $3
    `, [`%${searchTerm}%`, searchTerm, limit])

    return result.rows
  }

  // جلب المنتجات الأكثر مبيعاً
  static async getBestSellers(limit: number = 10): Promise<Product[]> {
    const result = await query(`
      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.is_published = true AND p.is_available = true
        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))
      GROUP BY p.id
      ORDER BY total_sold DESC, p.rating DESC
      LIMIT $1
    `, [limit])

    return result.rows
  }

  // جلب المنتجات الجديدة
  static async getNewProducts(limit: number = 10): Promise<Product[]> {
    const result = await query(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true
      ORDER BY created_at DESC
      LIMIT $1
    `, [limit])

    return result.rows
  }

  // جلب المنتجات ذات التقييم العالي
  static async getTopRated(limit: number = 10): Promise<Product[]> {
    const result = await query(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true AND rating > 0
      ORDER BY rating DESC, reviews_count DESC
      LIMIT $1
    `, [limit])

    return result.rows
  }
}
