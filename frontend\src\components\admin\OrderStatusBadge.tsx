
import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  AlertCircle,
  Clock,
  Truck,
  AlertCircle
} from 'lucide-react'

interface OrderStatusBadgeProps {
  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'
  showIcon?: boolean
}

export function OrderStatusBadge({ status, showIcon = true }: OrderStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          text: 'في الانتظار',
          className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
          icon: Clock
        }
      case 'confirmed':
        return {
          text: 'مؤكد',
          className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
          icon: CheckCircle
        }
      case 'in_production':
        return {
          text: 'قيد الإنتاج',
          className: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
          icon: Package
        }
      case 'shipped':
        return {
          text: 'تم الشحن',
          className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
          icon: Truck
        }
      case 'delivered':
        return {
          text: 'تم التسليم',
          className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
          icon: CheckCircle
        }
      case 'cancelled':
        return {
          text: 'ملغي',
          className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
          icon: XCircle
        }
      default:
        return {
          text: status,
          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
          icon: AlertCircle
        }
    }
  }

  const config = getStatusConfig(status)
  const Icon = config.icon

  return (
    <Badge className={config.className}>
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      <span className="arabic-text">{config.text}</span>
    </Badge>
  )
}

interface PaymentStatusBadgeProps {
  status: 'pending' | 'paid' | 'failed' | 'refunded'
  showIcon?: boolean
}

export function PaymentStatusBadge({ status, showIcon = true }: PaymentStatusBadgeProps) {
  const getPaymentStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          text: 'في الانتظار',
          className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
          icon: Clock
        }
      case 'paid':
        return {
          text: 'مدفوع',
          className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
          icon: CheckCircle
        }
      case 'failed':
        return {
          text: 'فشل',
          className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
          icon: XCircle
        }
      case 'refunded':
        return {
          text: 'مسترد',
          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
          icon: AlertCircle
        }
      default:
        return {
          text: status,
          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
          icon: AlertCircle
        }
    }
  }

  const config = getPaymentStatusConfig(status)
  const Icon = config.icon

  return (
    <Badge className={config.className}>
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      <span className="arabic-text">{config.text}</span>
    </Badge>
  )
}
