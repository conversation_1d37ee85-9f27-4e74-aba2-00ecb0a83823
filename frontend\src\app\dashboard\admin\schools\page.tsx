
'use client'

import {
  useState,
  useEffect
} from 'react'
import {
  <PERSON><PERSON>
} from '@/components/ui/button'

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  Content,
  Header,
  Title,
  Trigger,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  Content,
  Item,
  Trigger,
  Value,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Search,
  Trash2,
  ArrowLeft,
  s,
  SortAsc,
  import {
  SortDesc,
  SortDesc
} from 'lucide-react'
import {
  Mock
} from '@/lib/mockData'
import Form from '@/components/admin/ Form'



import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  TabsContent
} from '@/components/ui/tabs'

interface Stats {
  total: number
  active: number
  inactive: number
  totalStudents: number
  averageStudents: number
}

export default function sManagement() {
  const [schools, set s] = useState<Mock []>([])
  const [stats, setStats] = useState< Stats>({
    total: 0,
    active: 0,
    inactive: 0,
    totalStudents: 0,
    averageStudents: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [city setCity ] = useState('all')
  const [status setStatus ] = useState('all')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [is Open, setIs Open] = useState(false)
  const [editing setLoading ] = useState<Mock | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // جلب المدارس
  const fetch s = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        include_inactive: 'true',
        search: searchTerm,
        city: city === 'all' ? '' : city sort_by: sortBy,
        sort_order: sortOrder
      })

      const response = await fetch(`/api/schools?${params}`)
      const data = await response.json()

      if (response.ok) {
        set s(data.schools)
        setStats(data.stats)
      } else {}
    } catch (_error) {} finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetch s()
  }, [searchTerm, city status sortBy, sortOrder])

  // فلترة المدارس حسب الحالة
  const filtered s = schools.filter(school => {
    if (status === 'active') return school.is_active
    if (status === 'inactive') return !school.is_active
    return true
  })

  // إضافة أو تحديث مدرسة
  const handleSubmit = async (schoolData: Partial<Mock >) => {
    try {
      setIsSubmitting(true)
      
      const url = editing ? `/api/schools/${editing .id}` : '/api/schools'
      const method = editing ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json' },
        body: JSON.stringify(schoolData) })

      const data = await response.json()

      if (response.ok) {
        await fetch s()
        setIs Open(false)
        setLoading (null)
      } else {
        alert(data.error || 'حدث خطأ أثناء حفظ المدرسة')
      }
    } catch (_error) {alert('حدث خطأ أثناء حفظ المدرسة')
    } finally {
      setIsSubmitting(false)
    }
  }

  // حذف مدرسة
  const handleDelete = async (school: Mock ) => {
    if (!confirm(`هل أنت متأكد من حذف مدرسة "${school.name}"؟`)) {
      return
    }

    try {
      const response = await fetch(`/api/schools/${school.id}`, {
        method: 'DELETE' })

      const data = await response.json()

      if (response.ok) {
        await fetch s()
      } else {
        alert(data.error || 'حدث خطأ أثناء حذف المدرسة')
      }
    } catch (_error) {alert('حدث خطأ أثناء حذف المدرسة')
    }
  }

  // تغيير حالة النشاط
  const toggle Status = async (school: Mock ) => {
    try {
      const response = await fetch(`/api/schools/${school.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...school,
          is_active: !school.is_active
        }) })

      const data = await response.json()

      if (response.ok) {
        await fetch s()
      } else {
        alert(data.error || 'حدث خطأ أثناء تحديث حالة المدرسة')
      }
    } catch (_error) {alert('حدث خطأ أثناء تحديث حالة المدرسة')
    }
  }

  const resetForm = () => {
    setLoading (null)
  }

  const open = (school: Mock ) => {
    setLoading (school)
    setIs Open(true)
  }

  // الحصول على قائمة المدن الفريدة
  const uniqueCities = Array.from(new Set(schools.map(s => s.city).filter(Boolean)))

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <a href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </a>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة المدارس 🏫
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة المدارس المسجلة والشراكات التعليمية
              </p>
            </div>
            
            <Dialog open={is Open} onOpenChange={setIs Open}>
              <SelectTrigger asChild>
                <Button onClick={resetForm}>
                  <div className="h-4 w-4 mr-2" />
                  إضافة مدرسة جديدة
                </Button>
              </TabsTrigger>
              < Content className="max-w-4xl max-h-[90vh] overflow-y-auto">
                < Header>
                  < Title className="arabic-text">
                    {editing ? 'تحديث المدرسة' : 'إضافة مدرسة جديدة'}
                  </ Title>
                </ Header>
                < Form
                  school={editing || undefined}
                  onSubmit={handleSubmit}
                  onCancel={() => setIs Open(false)}
                  isLoading={isSubmitting}
                />
              </TabsContent>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي المدارس</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
                </div>
                <div className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">المدارس النشطة</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="h-4 w-4 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">المدارس غير النشطة</p>
                  <p className="text-2xl font-bold text-red-600">{stats.inactive}</p>
                </div>
                <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
                  <div className="h-4 w-4 bg-red-500 rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي الطلاب</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.totalStudents.toLocaleString()}</p>
                </div>
                < s className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط الطلاب</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.averageStudents.toLocaleString()}</p>
                </div>
                <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
                  < s className="h-4 w-4 text-orange-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="arabic-text flex items-center gap-2">
              <div className="h-5 w-5" />
              البحث والفلترة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input placeholder="البحث في المدارس..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={city } onValueChange={setCity }>
                <TabsTrigger>
                  <SelectValue placeholder="جميع المدن" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المدن</SelectItem>
                  {uniqueCities.map((city) => (
                    <SelectItem key={city} value={city!}>{city}</SelectItem>
                  ))}
                </TabsContent>
              </div>

              <Select value={status } onValueChange={setStatus }>
                <TabsTrigger>
                  <SelectValue placeholder="جميع الحالات" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشطة</SelectItem>
                  <SelectItem value="inactive">غير نشطة</SelectItem>
                </TabsContent>
              </div>

              <Select value={sortBy} onValueChange={setSortBy}>
                <TabsTrigger>
                  <SelectValue placeholder="ترتيب حسب" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="created_at">تاريخ الإنشاء</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                  <SelectItem value="student_count">عدد الطلاب</SelectItem>
                  <SelectItem value="city">المدينة</SelectItem>
                  <SelectItem value="graduation_date">تاريخ التخرج</SelectItem>
                </TabsContent>
              </div>

              <Button
                variant="outline"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="flex items-center gap-2"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                {sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* جدول المدارس */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">قائمة المدارس ({filtered s.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-400 arabic-text">جاري التحميل...</p>
              </div>
            ) : filtered s.length === 0 ? (
              <div className="text-center py-8">
                <div className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400 arabic-text">لا توجد مدارس مطابقة للبحث</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-4 arabic-text">المدرسة</th>
                      <th className="text-right p-4 arabic-text">المدينة</th>
                      <th className="text-right p-4 arabic-text">عدد الطلاب</th>
                      <th className="text-right p-4 arabic-text">تاريخ التخرج</th>
                      <th className="text-right p-4 arabic-text">الحالة</th>
                      <th className="text-right p-4 arabic-text">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filtered s.map((school) => (
                      <tr key={school.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="p-4">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white arabic-text">{school.name}</p>
                            {school.name_en && (
                              <p className="text-sm text-gray-600 dark:text-gray-400">{school.name_en}</p>
                            )}
                            {school.email && (
                              <div className="flex items-center gap-1 mt-1">
                                <div className="h-3 w-3 text-gray-400" />
                                <p className="text-xs text-gray-500">{school.email}</p>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-1">
                            <div className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-900 dark:text-white arabic-text">{school.city || 'غير محدد'}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-1">
                            < s className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-900 dark:text-white">{school.student_count.toLocaleString()}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          {school.graduation_date ? (
                            <div className="flex items-center gap-1">
                              <div className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-900 dark:text-white">
                                {new Date(school.graduation_date).toLocaleDateString('ar-AE')}
                              </span>
                            </div>
                          ) : (
                            <span className="text-gray-500 arabic-text">غير محدد</span>
                          )}
                        </td>
                        <td className="p-4">
                          < variant={school.is_active ? "default" : "secondary"}
                            className="cursor-pointer"
                            onClick={() => toggle Status(school)}
                          >
                            {school.is_active ? 'نشطة' : 'غير نشطة'}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => open (school)}
                            >
                              <div className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(school)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
