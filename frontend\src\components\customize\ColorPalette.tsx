
"use client"

import {
  useState
} from 'react'
import {
  Button
} from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  Content,
  List,
  Trigger,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
} from '@/components/ui/tabs'
import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
  Search
} from 'lucide-react'

interface Color {
  name: string
  value: string
  hex: string
  category: 'classic' | 'modern' | 'premium'
  popularity?: number
  isNew?: boolean
}

interface ColorProps {
  title: string
  selectedColor: string
  onColorChange: (color: string) => void
  colors: Color[]
  showCategories?: boolean
  showSearch?: boolean
  allowCustom?: boolean
  className?: string
}

const colorCategories = {
  classic: {
    name: 'كلاسيكي',
    icon: '🎩',
    description: 'الألوان التقليدية الأنيقة'
  },
  modern: {
    name: 'عصري',
    icon: '✨',
    description: 'ألوان معاصرة وجريئة'
  },
  premium: {
    name: 'فاخر',
    icon: '💎',
    description: 'ألوان راقية ومميزة'
  }
}

export function Color ({
  title,
  selectedColor,
  onColorChange,
  colors,
  showCategories = true,
  showSearch = false,
  allowCustom = false,
  className = ""
}: ColorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState<string>('all')
  const [customColor, setCustomColor] = useState('#000000')
  const [favorites, setFavorites] = useState<string[]>([])

  const filteredColors = colors.filter(color => {
    const matchesSearch = color.name.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = activeCategory === 'all' || color.category === activeCategory
    return matchesSearch && matchesCategory
  })

  const toggleFavorite = (colorValue: string) => {
    setFavorites(prev => 
      prev.includes(colorValue) 
        ? prev.filter(c => c !== colorValue)
        : [...prev, colorValue]
    )
  }

  const getPopularityStars = (popularity: number = 0) => {
    return Array.from({ length: 5 }, (_, i) => (
      < key={i}
        className={`h-3 w-3 ${
          i < popularity ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 arabic-text">
          <div className="h-5 w-5" />
          {title}
        </CardTitle>
        
        {showSearch && (
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input placeholder="ابحث عن لون..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 arabic-text"
            />
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {showCategories && (
          <Select value={activeCategory} onValueChange={setActiveCategory}>
            <TabsList className="category-grid grid w-full grid-cols-4">
              <TabsTrigger value="all" className="arabic-text">الكل</TabsTrigger>
              {Object.entries(colorCategories).map(([key, category]) => (
                <SelectTrigger key={key} value={key} className="arabic-text">
                  <span className="mr-1">{category.icon}</span>
                  {category.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        )}

        {/* Color Grid */}
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3">
          {filteredColors.map((color) => (
            <div key={color.value} className="relative group">
              <button
                onClick={() => onColorChange(color.value)}
                className={`relative w-full aspect-square rounded-lg border-3 transition-all duration-200 hover:scale-105 hover:shadow-lg ${
                  selectedColor === color.value
                    ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                }`}
                style={{ backgroundColor: color.hex }}
              >
                {/* ion Indicator */}
                {selectedColor === color.value && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white dark:bg-gray-900 rounded-full p-1">
                      <div className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                )}

                {/* New */}
                {color.isNew && (
                  <div className="absolute -top-2 -right-2 bg-green-500 text-xs px-1 py-0">
                    جديد
                  </div>
                )}
              </button>

              {/* Favorite Button - moved outside the main button */}
              <div
                onClick={(e) => {
                  e.stopPropagation()
                  toggleFavorite(color.value)
                }}
                className="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10"
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    e.stopPropagation()
                    toggleFavorite(color.value)
                  }
                }}
              >
                <div className={`h-3 w-3 ${
                    favorites.includes(color.value)
                      ? 'text-red-500 fill-current'
                      : 'text-white drop-shadow-lg'
                  }`}
                />
              </div>

              {/* Color */}
              <div className="mt-2 text-center">
                <div className="text-xs font-medium arabic-text truncate">
                  {color.name}
                </div>
                <div className="text-xs text-gray-500 uppercase">
                  {color.hex}
                </div>
                
                {/* Popularity s */}
                {color.popularity && (
                  <div className="flex justify-center gap-0.5 mt-1">
                    {getPopularityStars(color.popularity)}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Custom Color Picker */}
        {allowCustom && (
          <div className="border-t pt-4">
            <div className="text-sm font-medium arabic-text mb-3 block">
              لون مخصص
            </div>
            <div className="flex gap-3 items-center">
              <input
                type="color"
                value={customColor}
                onChange={(e) => setCustomColor(e.target.value)}
                className="w-12 h-12 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
              />
              <div className="flex-1">
                <Select value={customColor}
                  onChange={(e) => setCustomColor(e.target.value)}
                  placeholder="#000000"
                  className="font-mono"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onColorChange(customColor)}
                className="arabic-text"
              >
                تطبيق
              </Button>
            </div>
          </div>
        )}

        {/* Popular Combinations */}
        <div className="border-t pt-4">
          <div className="text-sm font-medium arabic-text mb-3 block">
            تركيبات شائعة
          </div>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => onColorChange('black')}
              className="flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex gap-1">
                <div className="w-4 h-4 rounded-full bg-black border" />
                <div className="w-4 h-4 rounded-full bg-yellow-400 border" />
              </div>
              <span className="text-xs arabic-text">كلاسيكي</span>
            </button>
            
            <button
              onClick={() => onColorChange('navy')}
              className="flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex gap-1">
                <div className="w-4 h-4 rounded-full bg-blue-900 border" />
                <div className="w-4 h-4 rounded-full bg-gray-300 border" />
              </div>
              <span className="text-xs arabic-text">أنيق</span>
            </button>
          </div>
        </div>

        {/* Color rmation */}
        {selectedColor && (
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <div 
                className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                style={{ backgroundColor: colors.find(c => c.value === selectedColor)?.hex }}
              />
              <div>
                <div className="font-medium arabic-text">
                  {colors.find(c => c.value === selectedColor)?.name}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {colors.find(c => c.value === selectedColor)?.hex}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
