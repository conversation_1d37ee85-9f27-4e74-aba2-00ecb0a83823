(()=>{var e={};e.id=9722,e.ids=[9722],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(38561);async function d(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_inactive"),s=u.C.getCategories();return r||(s=s.filter(e=>e.is_active)),s.sort((e,t)=>e.order_index-t.order_index),i.NextResponse.json({categories:s,total:s.length})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e){try{let{name_ar:t,name_en:r,name_fr:s,slug:n,icon:o,description:a,is_active:d,order_index:p}=await e.json();if(!t||!n)return i.NextResponse.json({error:"الاسم العربي والرابط المختصر مطلوبان"},{status:400});let x=u.C.getCategories();if(x.find(e=>e.slug===n))return i.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let c={id:u.C.generateId(),name_ar:t,name_en:r||void 0,name_fr:s||void 0,slug:n,icon:o||void 0,description:a||void 0,is_active:d??!0,order_index:p||x.length+1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return x.push(c),u.C.saveCategories(x),i.NextResponse.json({message:"تم إضافة الفئة بنجاح",category:c},{status:201})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:m}=x;function g(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(45750));module.exports=s})();