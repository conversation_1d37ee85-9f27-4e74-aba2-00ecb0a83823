(()=>{var e={};e.id=2165,e.ids=[2165],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>p});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(38561);async function p(e){try{let t,{prompt:r,language:s,category:n,style:o,colors:a,includeImages:p,includeText:d,pageType:l,targetAudience:c,businessType:m,modelId:x,includeMainHeader:g,mainMenuItems:b}=await e.json();if(!r||!s)return i.NextResponse.json({error:"الوصف واللغة مطلوبان"},{status:400});let h=null,f=null;if(!x)return i.NextResponse.json({error:"لم يتم تحديد نموذج للتوليد"},{status:400});{let e=x.split("-"),t=e[0],r=e.slice(1).join("-");h={id:t,providerName:"Google AI",status:"active",models:[r]},f=r}if(!h||!f)return i.NextResponse.json({error:"لا يوجد نموذج متاح للتوليد. يرجى إضافة وتفعيل مزود ذكاء اصطناعي أولاً."},{status:400});Date.now();let v=Math.floor(5e3*Math.random())+2e3;h.providerName;try{t=function(e,t){let r=[];return r.push({id:u.C.generateId(),type:"hero",name:"Hero Section",props:{content:"مرحباً بكم في موقعنا",style:{backgroundColor:"#1F2937",color:"#FFFFFF",textAlign:"center",padding:"4rem 2rem"}},position:{x:0,y:0},size:{width:"100%",height:"500px"},isVisible:!0}),r}(0,0)}catch(e){return i.NextResponse.json({error:"خطأ في توليد مكونات الصفحة"},{status:500})}let y=Math.ceil(r.length/4)+Math.floor(1e3*Math.random())+500,j=y/1e3*.002,k=u.C.getModelActivities();k.push({id:u.C.generateId(),modelId:`${h.id}-${f}`,type:"request",description:`توليد صفحة: ${r.substring(0,50)}${r.length>50?"...":""}`,details:{prompt:r,language:s,category:n,pageType:l,componentsGenerated:t.length,provider:h.providerName,model:f},timestamp:new Date().toISOString(),duration:v,tokensUsed:y,cost:j,success:!0}),u.C.saveModelActivities(k);let w=function(e,t){let r=[];return t.length<3&&r.push("يمكنك إضافة المزيد من الأقسام لجعل الصفحة أكثر تفصيلاً"),t.some(e=>"contact"===e.type)||r.push("فكر في إضافة نموذج اتصال لتسهيل التواصل مع الزوار"),t.some(e=>"testimonial"===e.type)||r.push("إضافة قسم آراء العملاء يمكن أن يزيد من الثقة"),t.some(e=>"gallery"===e.type)||r.push("معرض الصور يمكن أن يجعل الصفحة أكثر جاذبية"),r.push("تأكد من تحسين الصفحة للهواتف المحمولة"),r.push("استخدم ألوان متناسقة مع هوية علامتك التجارية"),r}(0,t),C={success:!0,components:t,suggestions:w,metadata:{tokensUsed:y,generationTime:v,modelUsed:`${h.providerName} - ${f}`,componentsCount:t.length,estimatedCost:j}};return i.NextResponse.json(C)}catch(e){return i.NextResponse.json({error:"خطأ في توليد الصفحة: "+(e instanceof Error?e.message:"خطأ غير معروف")},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/page-builder/generate/route",pathname:"/api/page-builder/generate",filename:"route",bundlePath:"app/api/page-builder/generate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\generate\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:m}=d;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(74261));module.exports=s})();