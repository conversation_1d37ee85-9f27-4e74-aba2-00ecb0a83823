const fs = require('fs');
const path = require('path');

// Function to find all TypeScript/TSX files
function findTsxFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (file !== 'node_modules' && file !== '.next' && file !== '.git') {
        results = results.concat(findTsxFiles(filePath));
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Function to fix critical import issues
function fixCriticalImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern 1: Fix "import { SomeIcon,\nimport {" pattern
    const pattern1 = /import\s*\{\s*([^}]*),\s*\nimport\s*\{\s*([^}]*)\s*\}\s*from\s*['"]([^'"]+)['"]/g;
    content = content.replace(pattern1, (match, imports1, imports2, module) => {
      const allImports = (imports1 + ',' + imports2)
        .split(',')
        .map(imp => imp.trim())
        .filter(imp => imp && imp !== 'import');
      
      modified = true;
      return `import {\n  ${allImports.join(',\n  ')}\n} from '${module}'`;
    });
    
    // Pattern 2: Fix standalone "} from 'module'" lines
    content = content.replace(/^\s*}\s*from\s*['"]([^'"]+)['"]\s*$/gm, '');
    
    // Pattern 3: Fix "< className=" to "<div className="
    if (content.includes('< className=')) {
      content = content.replace(/< className=/g, '<div className=');
      modified = true;
    }
    
    // Pattern 4: Fix "< htmlFor=" to "<label htmlFor="
    if (content.includes('< htmlFor=')) {
      content = content.replace(/< htmlFor=/g, '<label htmlFor=');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed critical imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Starting critical import fixes...');

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} files`);

let fixedCount = 0;

tsxFiles.forEach(file => {
  if (fixCriticalImports(file)) {
    fixedCount++;
  }
});

console.log(`\n✅ Fixed ${fixedCount} files`);
console.log('🚀 Run type-check to see remaining issues');
