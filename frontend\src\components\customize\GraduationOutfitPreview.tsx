
"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'


import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
  Move3D,
  Move3D
} from 'lucide-react'

interface OutfitConfiguration {
  gown: {
    color: string
    style: string
    fabric: string
  }
  cap: {
    color: string
    tassel: {
      color: string
      style: string
    }
  }
  stole: {
    enabled: boolean
    color: string
    embroidery: boolean
  }
  accessories: {
    hood: boolean
    sash: boolean
    medal: boolean
  }
}

interface GraduationOutfitPreviewProps {
  configuration: OutfitConfiguration
  className?: string
}

export function GraduationOutfitPreview({ 
  configuration, 
  className = "" 
}: GraduationOutfitPreviewProps) {
  const [rotation, setRotation] = useState(0)
  const [zoom, setZoom] = useState(1)
  const [isAnimating, setIsAnimating] = useState(false)

  // ألوان CSS للمعاينة
  const colorMap: Record<string, string> = {
    black: '#000000',
    navy: '#1e3a8a',
    burgundy: '#7c2d12',
    forest: '#166534',
    purple: '#7c3aed',
    gray: '#4b5563',
    gold: '#fbbf24',
    silver: '#e5e7eb',
    white: '#ffffff',
    blue: '#3b82f6',
    red: '#ef4444'
  }

  const handleRotate = () => {
    setIsAnimating(true)
    setRotation(prev => prev + 90)
    setTimeout(() => setIsAnimating(false), 500)
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.2, 2))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.2, 0.5))
  }

  const handleReset = () => {
    setRotation(0)
    setZoom(1)
  }

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardContent className="p-0">
        {/* Preview Controls */}
        <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 border-b">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="arabic-text">
              معاينة مباشرة
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleRotate}>
              <Move3D className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 3D Preview Area */}
        <div className="relative aspect-square bg-gradient-to-br from-gray-100 via-white to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-800 overflow-hidden">
          {/* Graduation Outfit Illustration */}
          <div 
            className={`absolute inset-0 flex items-center justify-center transition-transform duration-500 ${
              isAnimating ? 'animate-pulse' : ''
            }`}
            style={{ 
              transform: `rotate(${rotation}deg) scale(${zoom})`,
              transformOrigin: 'center'
            }}
          >
            {/* Gown */}
            <div className="relative">
              {/* Main Gown Body */}
              <div 
                className="w-32 h-40 rounded-t-full relative"
                style={{ 
                  backgroundColor: colorMap[configuration.gown.color] || '#000000',
                  opacity: configuration.gown.fabric === 'luxury' ? 0.9 : 0.8
                }}
              >
                {/* Gown Details */}
                <div className="absolute inset-x-0 top-0 h-8 bg-gradient-to-b from-white/20 to-transparent rounded-t-full" />
                
                {/* Sleeves */}
                <div 
                  className="absolute -left-6 top-4 w-12 h-16 rounded-full transform -rotate-12"
                  style={{ backgroundColor: colorMap[configuration.gown.color] || '#000000' }}
                />
                <div 
                  className="absolute -right-6 top-4 w-12 h-16 rounded-full transform rotate-12"
                  style={{ backgroundColor: colorMap[configuration.gown.color] || '#000000' }}
                />

                {/* Hood (if enabled) */}
                {configuration.accessories.hood && (
                  <div 
                    className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-20 h-12 rounded-t-full border-2"
                    style={{ 
                      backgroundColor: colorMap[configuration.gown.color] || '#000000',
                      borderColor: colorMap[configuration.cap.color] || '#000000'
                    }}
                  />
                )}
              </div>

              {/* Graduation Cap */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                {/* Cap Base */}
                <div 
                  className="w-16 h-4 rounded-full"
                  style={{ backgroundColor: colorMap[configuration.cap.color] || '#000000' }}
                />
                {/* Cap Top */}
                <div 
                  className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-20 h-20 border-4 border-gray-300"
                  style={{ backgroundColor: colorMap[configuration.cap.color] || '#000000' }}
                />
                {/* Tassel */}
                <div 
                  className="absolute top-0 right-0 w-1 h-8 transform rotate-12"
                  style={{ backgroundColor: colorMap[configuration.cap.tassel.color] || '#fbbf24' }}
                >
                  <div 
                    className="absolute bottom-0 w-3 h-3 rounded-full"
                    style={{ backgroundColor: colorMap[configuration.cap.tassel.color] || '#fbbf24' }}
                  />
                </div>
              </div>

              {/* Stole (if enabled) */}
              {configuration.stole.enabled && (
                <div className="absolute top-8 left-1/2 transform -translate-x-1/2">
                  <div 
                    className="w-6 h-32 rounded-full opacity-90"
                    style={{ backgroundColor: colorMap[configuration.stole.color] || '#fbbf24' }}
                  >
                    {configuration.stole.embroidery && (
                      <div className="absolute inset-0 bg-gradient-to-b from-yellow-200/50 to-transparent rounded-full" />
                    )}
                  </div>
                </div>
              )}

              {/* Sash (if enabled) */}
              {configuration.accessories.sash && (
                <div 
                  className="absolute top-12 left-0 w-full h-4 transform -rotate-12 opacity-80"
                  style={{ backgroundColor: '#ef4444' }}
                />
              )}

              {/* Medal (if enabled) */}
              {configuration.accessories.medal && (
                <div className="absolute top-16 left-1/2 transform -translate-x-1/2">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 border-2 border-yellow-300" />
                </div>
              )}
            </div>
          </div>

          {/* Floating Elements for Visual Appeal */}
          <div className="absolute top-4 left-4 w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
          <div className="absolute top-8 right-6 w-1 h-1 bg-purple-400 rounded-full animate-pulse" />
          <div className="absolute bottom-8 left-8 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce delay-300" />
          <div className="absolute bottom-4 right-4 w-2 h-2 bg-green-400 rounded-full animate-pulse delay-500" />
        </div>

        {/* Configuration Summary */}
        <div className="p-4 bg-white dark:bg-gray-900 border-t">
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400 arabic-text">الثوب:</span>
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full border"
                  style={{ backgroundColor: colorMap[configuration.gown.color] }}
                />
                <span className="arabic-text">
                  {configuration.gown.color === 'black' ? 'أسود' : 
                   configuration.gown.color === 'navy' ? 'أزرق داكن' : 
                   configuration.gown.color === 'burgundy' ? 'بورجوندي' : 
                   configuration.gown.color}
                </span>
              </div>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400 arabic-text">القبعة:</span>
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full border"
                  style={{ backgroundColor: colorMap[configuration.cap.color] }}
                />
                <span className="arabic-text">
                  {configuration.cap.color === 'black' ? 'أسود' : 
                   configuration.cap.color === 'navy' ? 'أزرق داكن' : 
                   configuration.cap.color}
                </span>
              </div>
            </div>

            {configuration.stole.enabled && (
              <div className="flex justify-between col-span-2">
                <span className="text-gray-600 dark:text-gray-400 arabic-text">الوشاح:</span>
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full border"
                    style={{ backgroundColor: colorMap[configuration.stole.color] }}
                  />
                  <span className="arabic-text">
                    {configuration.stole.color === 'gold' ? 'ذهبي' : 
                     configuration.stole.color === 'silver' ? 'فضي' : 
                     configuration.stole.color}
                  </span>
                  {configuration.stole.embroidery && (
                    <Badge variant="secondary" className="text-xs">مطرز</div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 mt-4">
            <Button variant="outline" size="sm" className="flex-1">
              <div className="h-4 w-4 mr-2" />
              <span className="arabic-text">تحميل</span>
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              <div className="h-4 w-4 mr-2" />
              <span className="arabic-text">مشاركة</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
