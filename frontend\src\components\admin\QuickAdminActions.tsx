
"use client"

import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'


import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
  Truck,
  ShoppingCart,
  ArrowRight,
  Truck
} from 'lucide-react'
  s,

interface AdminAction {
  title: string
  description: string
  href?: string
  icon: React.ReactNode
  color: string
  available: boolean
}

const adminActions: AdminAction[] = [
  {
    title: 'إدارة الصفحات',
    description: 'إنشاء وتحرير الصفحات الديناميكية',
    href: '/dashboard/admin/pages-management',
    icon: <FileText className="h-6 w-6" />,
    color: 'blue',
    available: true
  },
  {
    title: 'إدارة القائمة الرئيسية',
    description: 'تحكم في عناصر القائمة وترتيبها',
    href: '/dashboard/admin/menu-management',
    icon: <Menu className="h-6 w-6" />,
    color: 'green',
    available: true
  },
  {
    title: 'إدارة المنتجات',
    description: 'إضافة وتعديل منتجات المنصة',
    href: '/dashboard/admin/products',
    icon: <div className="h-6 w-6" />,
    color: 'purple',
    available: true
  },
  {
    title: 'إدارة المستخدمين',
    description: 'إدارة حسابات المستخدمين والصلاحيات',
    href: '/dashboard/admin/users',
    icon: < s className="h-6 w-6" />,
    color: 'orange',
    available: true
  },
  {
    title: 'إدارة المدارس',
    description: 'إدارة المدارس المسجلة والشراكات',
    href: '/dashboard/admin/schools',
    icon: <div className="h-6 w-6" />,
    color: 'teal',
    available: true
  },
  {
    title: 'إدارة الطلبات',
    description: 'متابعة ومعالجة طلبات العملاء',
    href: '/dashboard/admin/orders',
    icon: <ShoppingCart className="h-6 w-6" />,
    color: 'red',
    available: true
  },
  {
    title: 'إدارة التوصيل',
    description: 'إدارة عمليات التوصيل والموصلين',
    href: '/dashboard/admin/delivery',
    icon: <Truck className="h-6 w-6" />,
    color: 'yellow',
    available: true
  },
  {
    title: 'إعدادات النظام',
    description: 'إعدادات عامة وتكوين النظام',
    href: '/dashboard/admin/settings',
    icon: <div className="h-6 w-6" />,
    color: 'gray',
    available: true
  }
]

const getColorClasses = (color: string) => {
  const colorMap = {
    blue: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 group-hover:text-blue-600',
    green: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 group-hover:text-green-600',
    purple: 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 group-hover:text-purple-600',
    orange: 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 group-hover:text-orange-600',
    teal: 'bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-400 group-hover:text-teal-600',
    red: 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 group-hover:text-red-600',
    yellow: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400 group-hover:text-yellow-600',
    gray: 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 group-hover:text-gray-600'
  }
  return colorMap[color as keyof typeof colorMap] || colorMap.gray
}

interface QuickAdminActionsProps {
  title?: string
  showTitle?: boolean
  columns?: number
}

export function QuickAdminActions({ 
  title = "الإدارة السريعة", 
  showTitle = true,
  columns = 4 
}: QuickAdminActionsProps) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }

  return (
    <div className="mb-8">
      {showTitle && (
        <h2 className="text-xl font-semibold mb-6 arabic-text flex items-center gap-2">
          <div className="h-5 w-5" />
          {title}
        </h2>
      )}
      
      <div className={`grid ${gridCols[columns as keyof typeof gridCols]} gap-4`}>
        {adminActions.map((action, index) => {
          const ActionCard = (
            <Card 
              key={index}
              className={`hover:shadow-lg transition-shadow cursor-pointer group ${
                !action.available ? 'opacity-75' : ''
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(action.color)}`}>
                    {action.icon}
                  </div>
                  <ArrowRight className={`h-4 w-4 text-gray-400 transition-colors ${getColorClasses(action.color).split(' ').pop()}`} />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white arabic-text mb-2">
                  {action.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  {action.description}
                </p>
                {!action.available && (
                  <Badge variant="secondary" className="mt-2 text-xs">قريباً</div>
                )}
              </CardContent>
            </Card>
          )

          return action.available && action.href ? (
            <Link key={index} href={action.href}>
              {ActionCard}
            </Link>
          ) : (
          )
        })}
      </div>
    </div>
  )
}
