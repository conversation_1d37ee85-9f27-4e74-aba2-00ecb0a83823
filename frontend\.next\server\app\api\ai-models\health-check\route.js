(()=>{var e={};e.id=1239,e.ids=[1239],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>u,PUT:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),l=r(38561);async function u(e){try{let{provider:t,baseUrl:r,apiKey:s,selectedModels:a}=await e.json();if(!t||!r)return i.NextResponse.json({error:"مقدم الخدمة و Base URL مطلوبان"},{status:400});let n=await m(t,r,s,a);return i.NextResponse.json(n)}catch(e){return i.NextResponse.json({error:"خطأ في فحص حالة API"},{status:500})}}async function d(e){try{let e=l.C.getAIModels(),t=[];for(let r of e)if(r.isActive&&r.baseUrl){let s=await m(r.provider,r.baseUrl,r.apiKey,r.selectedModels||[]);t.push({modelId:r.id,provider:r.provider,name:r.name,...s});let a=e.findIndex(e=>e.id===r.id);-1!==a&&(e[a].status=s.status,e[a].lastCheckedAt=new Date().toISOString(),e[a].healthCheck=s)}return l.C.saveAIModels(e),i.NextResponse.json({results:t,summary:{total:t.length,healthy:t.filter(e=>"active"===e.status).length,unhealthy:t.filter(e=>"error"===e.status).length,warning:t.filter(e=>"warning"===e.status).length}})}catch(e){return i.NextResponse.json({error:"خطأ في فحص حالة النماذج"},{status:500})}}async function m(e,t,r,s=[]){let a=Date.now();try{let a=await c(e,t,r),n=await h(e,s,r);return{status:function(e,t){let r=e.filter(e=>"healthy"===e.status).length,s=e.length;if(0===s)return t>5e3?"warning":"active";let a=r/s*100;return a>=90?t>5e3?"warning":"active":a>=50?"warning":"error"}(n,a),responseTime:a,timestamp:new Date().toISOString(),baseUrl:t,hasApiKey:!!r,models:n,details:{endpoint:t,provider:e,modelsChecked:s.length,modelsHealthy:n.filter(e=>"healthy"===e.status).length,averageResponseTime:a}}}catch(s){return{status:"error",responseTime:Date.now()-a,timestamp:new Date().toISOString(),baseUrl:t,hasApiKey:!!r,error:s instanceof Error?s.message:"خطأ غير معروف",models:[],details:{endpoint:t,provider:e,modelsChecked:0,modelsHealthy:0,averageResponseTime:0}}}}async function c(e,t,r){let s=Date.now(),a={openai:{min:500,max:2e3},anthropic:{min:800,max:3e3},google:{min:400,max:1500},meta:{min:1e3,max:4e3},stability:{min:2e3,max:8e3},cohere:{min:600,max:2500},huggingface:{min:1500,max:5e3},deepseek:{min:700,max:2800}}[e]||{min:1e3,max:3e3},n=Math.floor(Math.random()*(a.max-a.min))+a.min;if(await new Promise(e=>setTimeout(e,Math.min(n,1e3))),.05>Math.random())throw Error(`فشل في الاتصال بـ ${e}`);if(!t.startsWith("http"))throw Error("Base URL غير صالح");if("openai"===e&&!r)throw Error("مفتاح API مطلوب لـ OpenAI");return Date.now()-s}async function h(e,t,r){let s=[];for(let e of t){let t=Math.random()>.1,r=Math.floor(1e3*Math.random())+200;s.push({name:e,status:t?"healthy":"error",responseTime:r,available:t,error:t?void 0:`النموذج ${e} غير متاح حالياً`})}return s}async function p(e){try{let{modelId:t,status:r,notes:s}=await e.json();if(!t||!r)return i.NextResponse.json({error:"معرف النموذج والحالة مطلوبان"},{status:400});let a=l.C.getAIModels(),n=a.findIndex(e=>e.id===t);if(-1===n)return i.NextResponse.json({error:"النموذج غير موجود"},{status:404});a[n].status=r,a[n].lastCheckedAt=new Date().toISOString(),a[n].healthCheck={status:r,timestamp:new Date().toISOString(),manual:!0,notes:s},l.C.saveAIModels(a);let o=l.C.getModelActivities();return o.push({id:l.C.generateId(),modelId:t,type:"health_check",description:`تم تحديث حالة النموذج يدوياً إلى: ${r}`,timestamp:new Date().toISOString(),success:!0,details:{notes:s,manual:!0}}),l.C.saveModelActivities(o),i.NextResponse.json({message:"تم تحديث حالة النموذج بنجاح",model:a[n]})}catch(e){return i.NextResponse.json({error:"خطأ في تحديث حالة النموذج"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai-models/health-check/route",pathname:"/api/ai-models/health-check",filename:"route",bundlePath:"app/api/ai-models/health-check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\health-check\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:w}=x;function b(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(54261));module.exports=s})();