{"name": "critters", "version": "0.0.23", "description": "Inline critical CSS and lazy-load the rest.", "main": "dist/critters.js", "module": "dist/critters.mjs", "source": "src/index.js", "exports": {"import": "./dist/critters.mjs", "require": "./dist/critters.js", "default": "./dist/critters.mjs"}, "typings": "src/index.d.ts", "files": ["src", "dist"], "license": "Apache-2.0", "author": "The Chromium Authors", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["critical css", "inline css", "critical", "critters", "webpack plugin", "performance"], "repository": {"type": "git", "url": "https://github.com/GoogleChromeLabs/critters", "directory": "packages/critters"}, "scripts": {"build": "microbundle --target node --no-sourcemap -f cjs,esm", "docs": "documentation readme -q --no-markdown-toc -a public -s Usage --sort-order alpha src", "prepare": "npm run -s build"}, "dependencies": {"chalk": "^4.1.0", "css-select": "^5.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.2", "htmlparser2": "^8.0.2", "postcss": "^8.4.23", "postcss-media-query-parser": "^0.2.3"}, "devDependencies": {"documentation": "^13.0.2", "microbundle": "^0.12.3"}}