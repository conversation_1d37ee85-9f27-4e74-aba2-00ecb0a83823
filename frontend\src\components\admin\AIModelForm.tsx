
'use client'

import { useState, useEffect } from 'react'
import { AIModel, AIProvider, ModelType, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'
import { Button } from '@/components/ui/button'

import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import {
  import {
  Eye,
  EyeOff,
  TestTube,
  Save,
  X,
  Activity,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Play,
  Eye
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'







import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'

interface AIModelFormProps {
  model?: AIModel
  isOpen: boolean
  onClose: () => void
  onSave: (data: CreateModelRequest | UpdateModelRequest) => Promise<void>
  onTest?: (data: any) => Promise<void>
}

const PROVIDERS: {
  value: AIProvider;
  label: string;
  description: string;
  baseUrl: string;
  models: string[];
}[] = [
  {
    value: 'openai',
    label: 'OpenAI',
    description: 'GPT, DALL-E, Whisper',
    baseUrl: 'https://api.openai.com/v1',
    models: ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'dall-e-3', 'dall-e-2', 'whisper-1', 'tts-1', 'text-embedding-ada-002']
  },
  {
    value: 'anthropic',
    label: 'Anthropic',
    description: 'Claude Models',
    baseUrl: 'https://api.anthropic.com',
    models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1', 'claude-2.0', 'claude-instant-1.2']
  },
  {
    value: 'google',
    label: 'Google',
    description: 'Gemini, PaLM',
    baseUrl: 'https://generativelanguage.googleapis.com/v1',
    models: ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash', 'text-bison-001', 'chat-bison-001']
  },
  {
    value: 'meta',
    label: 'Meta',
    description: 'LLaMA Models',
    baseUrl: 'https://api.llama-api.com/v1',
    models: ['llama-2-70b-chat', 'llama-2-13b-chat', 'llama-2-7b-chat', 'code-llama-34b', 'code-llama-13b', 'code-llama-7b']
  },
  {
    value: 'stability',
    label: 'Stability AI',
    description: 'Stable Diffusion',
    baseUrl: 'https://api.stability.ai/v1',
    models: ['stable-diffusion-xl-1024-v1-0', 'stable-diffusion-v1-6', 'stable-diffusion-512-v2-1', 'stable-diffusion-xl-beta-v2-2-2']
  },
  {
    value: 'cohere',
    label: 'Cohere',
    description: 'Command Models',
    baseUrl: 'https://api.cohere.ai/v1',
    models: ['command', 'command-light', 'command-nightly', 'command-r', 'command-r-plus', 'embed-english-v3.0', 'embed-multilingual-v3.0']
  },
  {
    value: 'huggingface',
    label: 'Hugging Face',
    description: 'Open Source Models',
    baseUrl: 'https://api-inference.huggingface.co',
    models: ['microsoft/DialoGPT-large', 'facebook/blenderbot-400M-distill', 'microsoft/DialoGPT-medium', 'facebook/blenderbot-1B-distill']
  },
  {
    value: 'deepseek',
    label: 'DeepSeek',
    description: 'DeepSeek Models',
    baseUrl: 'https://api.deepseek.com/v1',
    models: ['deepseek-chat', 'deepseek-coder', 'deepseek-math', 'deepseek-67b-chat', 'deepseek-7b-chat']
  }
]

const MODEL_TYPES: { value: ModelType; label: string; icon: string }[] = [
  { value: 'text', label: 'نص', icon: '📝' },
  { value: 'image', label: 'صورة', icon: '🖼️' },
  { value: 'audio', label: 'صوت', icon: '🎵' },
  { value: 'multimodal', label: 'متعدد الوسائط', icon: '🔄' },
  { value: 'code', label: 'كود', icon: '💻' },
  { value: 'embedding', label: 'تضمين', icon: '🔗' }
]

export function AIModelForm({ model, isOpen, onClose, onSave, onTest }: AIModelFormProps) {
  const [formData, setFormData] = useState<any>({
    provider: 'openai' as AIProvider,
    description: '',
    apiKey: '',
    baseUrl: '',
    isActive: true,
    settings: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      systemPrompt: ''
    }
  })

  const [showApiKey, setShowApiKey] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [testPrompt, setTestPrompt] = useState('مرحباً، كيف يمكنني مساعدتك؟')
  const [selectedModels, setSelectedModels] = useState<string[]>([])
  const [availableModels, setAvailableModels] = useState<string[]>([])
  const [healthCheckLoading, setHealthCheckLoading] = useState(false)
  const [healthCheckResult, setHealthCheckResult] = useState<any>(null)

  useEffect(() => {
    if (model) {
      setFormData({
        provider: model.provider,
        description: model.description || '',
        apiKey: model.apiKey || '',
        baseUrl: model.baseUrl || '',
        isActive: model.isActive,
        settings: { ...model.settings }
      })
      // تحديد النماذج المحددة من النماذج الفرعية الموجودة
      setSelectedModels(model.selectedModels || model.subModels?.map(sm => sm.name) || [])
    } else {
      // إعادة تعيين النموذج للإضافة الجديدة
      const defaultProvider = PROVIDERS[0]
      setFormData({
        provider: 'openai' as AIProvider,
        description: '',
        apiKey: '',
        baseUrl: defaultProvider.baseUrl,
        isActive: true,
        settings: {
          temperature: 0.7,
          maxTokens: 2048,
          topP: 1,
          frequencyPenalty: 0,
          presencePenalty: 0,
          systemPrompt: ''
        }
      })
      setSelectedModels([])
      setAvailableModels(defaultProvider.models)
    }
  }, [model, isOpen])

  // تحديث النماذج المتاحة عند تغيير مقدم الخدمة
  useEffect(() => {
    const provider = PROVIDERS.find(p => p.value === formData.provider)
    if (provider) {
      setAvailableModels(provider.models)
      setFormData(prev => ({
        ...prev,
        baseUrl: provider.baseUrl
      }))
      // إعادة تعيين النماذج المحددة عند تغيير المقدم
      setSelectedModels([])
    }
  }, [formData.provider])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // التحقق من تحديد نماذج
    if (selectedModels.length === 0) {
      alert('يرجى تحديد نموذج واحد على الأقل')
      return
    }

    setIsLoading(true)

    try {
      // توليد اسم النموذج تلقائياً من مقدم الخدمة
      const provider = PROVIDERS.find(p => p.value === formData.provider)
      const generatedName = provider ? provider.label : formData.provider

      // تحديد نوع النموذج تلقائياً بناءً على النماذج المحددة
      const modelType = determineModelType(selectedModels)

      // إنشاء النماذج الفرعية من النماذج المحددة
      const subModels = selectedModels.map((modelName, index) => ({
        id: `${formData.provider}-${modelName}-${Date.now()}-${index}`,
        name: modelName,
        modelId: model?.id || `new-model-${Date.now()}`,
        description: `نموذج ${modelName} من ${formData.provider}`,
        version: '1.0',
        capabilities: getModelCapabilities(modelName),
        pricing: getModelPricing(formData.provider, modelName),
        limits: getModelLimits(modelName),
        isActive: true,
        isDefault: index === 0, // النموذج الأول يكون افتراضي
        tags: [formData.provider, modelType],
        releaseDate: new Date().toISOString()
      }))

      const dataToSave = {
        ...formData,
        name: generatedName,
        type: modelType,
        subModels,
        selectedModels
      }

      await onSave(dataToSave)
      onClose()
    } catch (error) {
      console.error('Error saving model:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // دالة لتحديد نوع النموذج تلقائياً
  const determineModelType = (models: string[]): ModelType => {
    // فحص النماذج لتحديد النوع الأساسي
    const hasImageModel = models.some(m =>
      m.includes('dall-e') || m.includes('stable-diffusion') || m.includes('vision')
    )
    const hasAudioModel = models.some(m =>
      m.includes('whisper') || m.includes('tts')
    )
    const hasEmbeddingModel = models.some(m =>
      m.includes('embed')
    )
    const hasCodeModel = models.some(m =>
      m.includes('code') || m.includes('coder')
    )
    const hasMultimodalModel = models.some(m =>
      m.includes('vision') || m.includes('multimodal')
    )

    if (hasMultimodalModel) return 'multimodal'
    if (hasImageModel) return 'image'
    if (hasAudioModel) return 'audio'
    if (hasEmbeddingModel) return 'embedding'
    if (hasCodeModel) return 'code'

    return 'text' // افتراضي
  }

  // دالة مساعدة لتحديد قدرات النموذج
  const getModelCapabilities = (modelName: string): string[] => {
    const capabilities = ['text-generation']

    if (modelName.includes('gpt-4') || modelName.includes('claude-3')) {
      capabilities.push('reasoning', 'analysis', 'code')
    }
    if (modelName.includes('vision') || modelName.includes('dall-e')) {
      capabilities.push('image-understanding', 'image-generation')
    }
    if (modelName.includes('whisper') || modelName.includes('tts')) {
      capabilities.push('audio-processing')
    }
    if (modelName.includes('embed')) {
      capabilities.push('embeddings')
    }
    if (modelName.includes('code') || modelName.includes('coder')) {
      capabilities.push('code-generation', 'code-analysis')
    }

    return capabilities
  }

  // دالة مساعدة لتحديد التسعير
  const getModelPricing = (provider: string, modelName: string) => {
    const pricingMap: Record<string, { input: number, output: number }> = {
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },
      'claude-3-opus': { input: 0.015, output: 0.075 },
      'claude-3-sonnet': { input: 0.003, output: 0.015 },
      'claude-3-haiku': { input: 0.00025, output: 0.00125 },
      'gemini-pro': { input: 0.0005, output: 0.0015 },
      'default': { input: 0.001, output: 0.002 }
    }

    const pricing = pricingMap[modelName] || pricingMap['default']
    return {
      inputTokens: pricing.input,
      outputTokens: pricing.output,
      currency: 'USD',
      unit: '1K tokens'
    }
  }

  // دالة مساعدة لتحديد حدود النموذج
  const getModelLimits = (modelName: string) => {
    const limitsMap: Record<string, any> = {
      'gpt-4-turbo': { maxTokens: 128000, requestsPerMinute: 500, requestsPerDay: 10000, contextWindow: 128000 },
      'gpt-4': { maxTokens: 8192, requestsPerMinute: 200, requestsPerDay: 5000, contextWindow: 8192 },
      'gpt-3.5-turbo': { maxTokens: 16385, requestsPerMinute: 3500, requestsPerDay: 50000, contextWindow: 16385 },
      'claude-3-opus': { maxTokens: 200000, requestsPerMinute: 50, requestsPerDay: 1000, contextWindow: 200000 },
      'default': { maxTokens: 4096, requestsPerMinute: 60, requestsPerDay: 1000, contextWindow: 4096 }
    }

    return limitsMap[modelName] || limitsMap['default']
  }

  const handleTest = async () => {
    if (!onTest) return
    
    setIsLoading(true)
    try {
      await onTest({
        modelId: model?.id,
        prompt: testPrompt,
        settings: formData.settings
      })
    } catch (error) {
      console.error('Error testing model:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateFormData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateSettings = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      settings: {
        ...prev.settings,
        [field]: value
      }
    }))
  }

  const handleModelToggle = (modelName: string) => {
    setSelectedModels(prev => {
      if (prev.includes(modelName)) {
        return prev.filter(m => m !== modelName)
      } else {
        return [...prev, modelName]
      }
    })
  }

  const handleSelectAllModels = () => {
    if (selectedModels.length === availableModels.length) {
      setSelectedModels([])
    } else {
      setSelectedModels([...availableModels])
    }
  }

  const handleHealthCheck = async () => {
    if (!formData.baseUrl || selectedModels.length === 0) {
      alert('يرجى إدخال Base URL وتحديد نماذج للفحص')
      return
    }

    setHealthCheckLoading(true)
    setHealthCheckResult(null)

    try {
      const response = await fetch('/api/ai-models/health-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: formData.provider,
          baseUrl: formData.baseUrl,
          apiKey: formData.apiKey,
          selectedModels: selectedModels
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setHealthCheckResult(result)
      } else {
        setHealthCheckResult({
          status: 'error',
          error: result.error || 'فشل في فحص الصحة'
        })
      }
    } catch (error) {
      setHealthCheckResult({
        status: 'error',
        error: 'خطأ في الاتصال بالخادم'
      })
    } finally {
      setHealthCheckLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="arabic-text">
            {model ? 'تحرير إعدادات مقدم الخدمة' : 'إضافة مقدم خدمة جديد'}
          </DialogTitle>
          <DialogDescription>
            {model
              ? 'قم بتحديث إعدادات مقدم الخدمة والنماذج المحددة'
              : 'اختر مقدم خدمة وحدد النماذج المطلوبة للعمل بها'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">المعلومات الأساسية</TabsTrigger>
              <TabsTrigger value="settings">الإعدادات</TabsTrigger>
              <TabsTrigger value="test">الاختبار</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">مقدم الخدمة *</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => updateFormData('provider', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {PROVIDERS.map((provider) => (
                        <SelectItem key={provider.value} value={provider.value}>
                          <div className="flex flex-col">
                            <span>{provider.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {provider.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isActive" className="flex items-center gap-2">
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => updateFormData('isActive', checked)}
                    />
                    نشط
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder="وصف مختصر للنموذج وقدراته"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiKey">مفتاح API</Label>
                <div className="relative">
                  <Input
                    id="apiKey"
                    type={showApiKey ? 'text' : 'password'}
                    value={formData.apiKey}
                    onChange={(e) => updateFormData('apiKey', e.target.value)}
                    placeholder="أدخل مفتاح API"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-2 top-1/2 -translate-y-1/2"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="baseUrl">Base URL *</Label>
                <Input
                  id="baseUrl"
                  value={formData.baseUrl}
                  onChange={(e) => updateFormData('baseUrl', e.target.value)}
                  placeholder="https://api.example.com/v1"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  رابط API الأساسي لمقدم الخدمة
                </p>
              </div>

              {/* اختيار النماذج المتاحة */}
              {availableModels.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>النماذج المتاحة</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAllModels}
                    >
                      {selectedModels.length === availableModels.length ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto border rounded-lg p-3">
                    {availableModels.map((modelName) => (
                      <div key={modelName} className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="checkbox"
                          id={`model-${modelName}`}
                          checked={selectedModels.includes(modelName)}
                          onChange={() => handleModelToggle(modelName)}
                          className="rounded border-gray-300"
                        />
                        <Label
                          htmlFor={`model-${modelName}`}
                          className="text-sm font-normal cursor-pointer flex-1"
                        >
                          {modelName}
                        </Label>
                      </div>
                    ))}
                  </div>

                  <p className="text-xs text-muted-foreground">
                    تم تحديد {selectedModels.length} من {availableModels.length} نموذج
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">إعدادات التوليد</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label>درجة الحرارة: {formData.settings.temperature}</Label>
                    <Slider
                      value={[formData.settings.temperature]}
                      onValueChange={([value]) => updateSettings('temperature', value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      قيم أعلى تعني إجابات أكثر إبداعاً وعشوائية
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>الحد الأقصى للرموز: {formData.settings.maxTokens}</Label>
                    <Slider
                      value={[formData.settings.maxTokens]}
                      onValueChange={([value]) => updateSettings('maxTokens', value)}
                      max={8192}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Top P: {formData.settings.topP}</Label>
                    <Slider
                      value={[formData.settings.topP]}
                      onValueChange={([value]) => updateSettings('topP', value)}
                      max={1}
                      min={0}
                      step={0.01}
                      className="w-full"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>عقوبة التكرار: {formData.settings.frequencyPenalty}</Label>
                      <Slider
                        value={[formData.settings.frequencyPenalty]}
                        onValueChange={([value]) => updateSettings('frequencyPenalty', value)}
                        max={2}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>عقوبة الحضور: {formData.settings.presencePenalty}</Label>
                      <Slider
                        value={[formData.settings.presencePenalty]}
                        onValueChange={([value]) => updateSettings('presencePenalty', value)}
                        max={2}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="systemPrompt">الرسالة النظامية</Label>
                    <Textarea
                      id="systemPrompt"
                      value={formData.settings.systemPrompt}
                      onChange={(e) => updateSettings('systemPrompt', e.target.value)}
                      placeholder="أنت مساعد ذكي مفيد ومهذب..."
                      rows={4}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="test" className="space-y-4">
              {/* فحص صحة API */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    فحص صحة API
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">
                      تحقق من حالة الاتصال بـ API ومدى توفر النماذج المحددة
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleHealthCheck}
                      disabled={healthCheckLoading || !formData.baseUrl || selectedModels.length === 0}
                    >
                      {healthCheckLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                          جاري الفحص...
                        </>
                      ) : (
                        <>
                          <Activity className="h-4 w-4 mr-2" />
                          فحص الاتصال
                        </>
                      )}
                    </Button>
                  </div>

                  {healthCheckResult && (
                    <div className={`p-4 rounded-lg border ${
                      healthCheckResult.status === 'active' ? 'bg-green-50 border-green-200' :
                      healthCheckResult.status === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                      'bg-red-50 border-red-200'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        {healthCheckResult.status === 'active' && <CheckCircle className="h-5 w-5 text-green-600" />}
                        {healthCheckResult.status === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-600" />}
                        {healthCheckResult.status === 'error' && <XCircle className="h-5 w-5 text-red-600" />}
                        <span className={`font-semibold ${
                          healthCheckResult.status === 'active' ? 'text-green-800' :
                          healthCheckResult.status === 'warning' ? 'text-yellow-800' :
                          'text-red-800'
                        }`}>
                          {healthCheckResult.status === 'active' ? 'API يعمل بشكل طبيعي' :
                           healthCheckResult.status === 'warning' ? 'API يعمل مع تحذيرات' :
                           'API لا يعمل'}
                        </span>
                      </div>

                      {healthCheckResult.responseTime && (
                        <p className="text-sm text-muted-foreground mb-2">
                          وقت الاستجابة: {healthCheckResult.responseTime}ms
                        </p>
                      )}

                      {healthCheckResult.error && (
                        <p className="text-sm text-red-600 mb-2">
                          خطأ: {healthCheckResult.error}
                        </p>
                      )}

                      {healthCheckResult.models && healthCheckResult.models.length > 0 && (
                        <div className="mt-3">
                          <h4 className="text-sm font-medium mb-2">حالة النماذج:</h4>
                          <div className="space-y-1">
                            {healthCheckResult.models.map((model: any, index: number) => (
                              <div key={index} className="flex items-center justify-between text-sm">
                                <span>{model.name}</span>
                                <div className="flex items-center gap-2">
                                  <span className={`px-2 py-1 rounded text-xs ${
                                    model.status === 'healthy' ? 'bg-green-100 text-green-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {model.status === 'healthy' ? 'متاح' : 'غير متاح'}
                                  </span>
                                  <span className="text-muted-foreground">{model.responseTime}ms</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {model && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">اختبار النموذج</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="testPrompt">النص التجريبي</Label>
                      <Textarea
                        id="testPrompt"
                        value={testPrompt}
                        onChange={(e) => setTestPrompt(e.target.value)}
                        placeholder="أدخل نص لاختبار النموذج"
                        rows={3}
                      />
                    </div>

                    <Button 
                      type="button" 
                      onClick={handleTest}
                      disabled={isLoading || !testPrompt.trim()}
                      className="w-full"
                    >
                      <TestTube className="h-4 w-4 mr-2" />
                      {isLoading ? 'جاري الاختبار...' : 'اختبار النموذج'}
                    </Button>

                    {model.testResult && (
                      <div className="mt-4 p-4 border rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant={model.testResult.success ? "default" : "destructive"}>
                            {model.testResult.success ? 'نجح' : 'فشل'}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {model.testResult.responseTime}ms
                          </span>
                        </div>
                        {model.testResult.error && (
                          <p className="text-sm text-destructive">{model.testResult.error}</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'جاري الحفظ...' : 'حفظ'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
