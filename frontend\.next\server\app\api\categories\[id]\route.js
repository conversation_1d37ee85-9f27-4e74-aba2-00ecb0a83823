(()=>{var e={};e.id=4096,e.ids=[4096],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56727:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>u,PUT:()=>p});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),d=t(38561);async function u(e,{params:r}){try{let e=d.C.getCategories().find(e=>e.id===r.id);if(!e)return a.NextResponse.json({error:"الفئة غير موجودة"},{status:404});return a.NextResponse.json({category:e})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:r}){try{let{name_ar:t,name_en:s,name_fr:n,slug:o,icon:i,description:u,is_active:p,order_index:x}=await e.json(),c=d.C.getCategories(),m=c.findIndex(e=>e.id===r.id);if(-1===m)return a.NextResponse.json({error:"الفئة غير موجودة"},{status:404});if(o&&o!==c[m].slug&&c.find(e=>e.slug===o&&e.id!==r.id))return a.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let g={...c[m],name_ar:t||c[m].name_ar,name_en:void 0!==s?s:c[m].name_en,name_fr:void 0!==n?n:c[m].name_fr,slug:o||c[m].slug,icon:void 0!==i?i:c[m].icon,description:void 0!==u?u:c[m].description,is_active:void 0!==p?p:c[m].is_active,order_index:void 0!==x?x:c[m].order_index,updated_at:new Date().toISOString()};return c[m]=g,d.C.saveCategories(c),a.NextResponse.json({message:"تم تحديث الفئة بنجاح",category:g})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function x(e,{params:r}){try{let e=d.C.getCategories(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return a.NextResponse.json({error:"الفئة غير موجودة"},{status:404});let s=d.C.getProducts(),n=e[t].slug,o=s.filter(e=>e.category===n);if(o.length>0)return a.NextResponse.json({error:`لا يمكن حذف الفئة لأنها مستخدمة في ${o.length} منتج`},{status:400});return e.splice(t,1),d.C.saveCategories(e),a.NextResponse.json({message:"تم حذف الفئة بنجاح"})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:l}=c;function b(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(56727));module.exports=s})();