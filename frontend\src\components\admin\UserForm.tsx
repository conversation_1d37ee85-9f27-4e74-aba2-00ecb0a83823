
"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'

import {  Content, Item, Trigger, Value } from '@/components/ui/select'
import {  Content, Description, Footer, Header, Title } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
  Shield,
  GraduationCap,
  Truck,
  Off
} from 'lucide-react'

interface FormProps {
  onSubmit: (userData: unknown) => void
  onCancel: () => void
}

interface FormData {
  email: string
  full_name: string
  phone: string
  role: 'admin' | 'student' | 'school' | 'delivery'
  password: string
  confirmPassword: string
  status: 'active' | 'inactive'
  verified: boolean
  // حقول إضافية حسب الدور
  student_id?: string
  school_name?: string
  school_address?: string
  delivery_company?: string
  delivery_license?: string
  notes?: string
}

export function Form({ onSubmit, onCancel }: FormProps) {
  const [formData, setFormData] = useState< FormData>({
    email: '',
    full_name: '',
    phone: '',
    role: 'student',
    password: '',
    confirmPassword: '',
    status: 'active',
    verified: false,
    notes: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  // التحقق من صحة البيانات
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // التحقق من البريد الإلكتروني
    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    // التحقق من الاسم
    if (!formData.full_name) {
      newErrors.full_name = 'الاسم الكامل مطلوب'
    } else if (formData.full_name.length < 2) {
      newErrors.full_name = 'الاسم يجب أن يكون أكثر من حرفين'
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    // التحقق من تأكيد كلمة المرور
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة'
    }

    // التحقق من الهاتف
    if (formData.phone && !/^\+?[1-9]\d{1,14}$/.test(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح'
    }

    // التحقق من الحقول الإضافية حسب الدور
    if (formData.role === 'student' && formData.student_id && formData.student_id.length < 3) {
      newErrors.student_id = 'رقم الطالب يجب أن يكون 3 أحرف على الأقل'
    }

    if (formData.role === 'school' && !formData.school_name) {
      newErrors.school_name = 'اسم المدرسة مطلوب'
    }

    if (formData.role === 'delivery' && !formData.delivery_company) {
      newErrors.delivery_company = 'اسم شركة التوصيل مطلوب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // معالجة إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      // إزالة تأكيد كلمة المرور من البيانات المرسلة
      const { confirmPassword, ...userData } = formData
      await onSubmit(userData)
    } catch (_error) {} finally {
      setLoading(false)
    }
  }

  // تحديث البيانات
  const updateFormData = (field: keyof FormData, value: unknown) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // الحصول على أيقونة الدور
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Shield className="h-4 w-4" />
      case 'student': return <GraduationCap className="h-4 w-4" />
      case 'school': return <div className="h-4 w-4" />
      case 'delivery': return <Truck className="h-4 w-4" />
      default: return <div className="h-4 w-4" />
    }
  }

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      < Content className="max-w-2xl max-h-[90vh] overflow-y-auto">
        < Header>
          < Title className="arabic-text">إضافة مستخدم جديد</ Title>
          < Description className="arabic-text">
            أدخل بيانات المستخدم الجديد وحدد دوره في النظام
          </ Description>
        </ Header>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* المعلومات الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg arabic-text">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="full_name" className="arabic-text">الاسم الكامل *</Label>
                  <Input id="full_name"
                    value={formData.full_name}
                    onChange={(e) => updateFormData('full_name', e.target.value)}
                    placeholder="أدخل الاسم الكامل"
                    className={errors.full_name ? 'border-red-500' : ''}
                  />
                  {errors.full_name && (
                    <p className="text-sm text-red-500 arabic-text">{errors.full_name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="arabic-text">البريد الإلكتروني *</Label>
                  <Input id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 arabic-text">{errors.email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="arabic-text">رقم الهاتف</Label>
                  <Input id="phone"
                    value={formData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                    placeholder="+971501234567"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-500 arabic-text">{errors.phone}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="arabic-text">الدور *</Label>
                  <Select value={formData.role} onValueChange={(value: unknown) => updateFormData('role', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الدور" />
                    </TabsTrigger>
                    <SelectContent>
                      <SelectItem value="student">
                        <div className="flex items-center gap-2">
                          <GraduationCap className="h-4 w-4" />
                          طالب
                        </div>
                      </SelectItem>
                      <SelectItem value="school">
                        <div className="flex items-center gap-2">
                          <div className="h-4 w-4" />
                          مدرسة
                        </div>
                      </SelectItem>
                      <SelectItem value="delivery">
                        <div className="flex items-center gap-2">
                          <Truck className="h-4 w-4" />
                          شركة توصيل
                        </div>
                      </SelectItem>
                      <SelectItem value="admin">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4" />
                          مدير
                        </div>
                      </SelectItem>
                    </TabsContent>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* كلمة المرور */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg arabic-text">كلمة المرور</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password" className="arabic-text">كلمة المرور *</Label>
                  <div className="relative">
                    <Input id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => updateFormData('password', e.target.value)}
                      placeholder="أدخل كلمة المرور"
                      className={errors.password ? 'border-red-500' : ''}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? < Off className="h-4 w-4" /> : <div className="h-4 w-4" />}
                    </Button>
                  </div>
                  {errors.password && (
                    <p className="text-sm text-red-500 arabic-text">{errors.password}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="arabic-text">تأكيد كلمة المرور *</Label>
                  <div className="relative">
                    <Input id="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={(e) => updateFormData('confirmPassword', e.target.value)}
                      placeholder="أعد إدخال كلمة المرور"
                      className={errors.confirmPassword ? 'border-red-500' : ''}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? < Off className="h-4 w-4" /> : <div className="h-4 w-4" />}
                    </Button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-500 arabic-text">{errors.confirmPassword}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الحقول الإضافية حسب الدور */}
          {formData.role === 'student' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg arabic-text">معلومات الطالب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="student_id" className="arabic-text">رقم الطالب</Label>
                  <Input id="student_id"
                    value={formData.student_id || ''}
                    onChange={(e) => updateFormData('student_id', e.target.value)}
                    placeholder="STU2024001"
                    className={errors.student_id ? 'border-red-500' : ''}
                  />
                  {errors.student_id && (
                    <p className="text-sm text-red-500 arabic-text">{errors.student_id}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {formData.role === 'school' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg arabic-text">معلومات المدرسة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="school_name" className="arabic-text">اسم المدرسة *</Label>
                  <Input id="school_name"
                    value={formData.school_name || ''}
                    onChange={(e) => updateFormData('school_name', e.target.value)}
                    placeholder="جامعة الإمارات العربية المتحدة"
                    className={errors.school_name ? 'border-red-500' : ''}
                  />
                  {errors.school_name && (
                    <p className="text-sm text-red-500 arabic-text">{errors.school_name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="school_address" className="arabic-text">عنوان المدرسة</Label>
                  <Input id="school_address"
                    value={formData.school_address || ''}
                    onChange={(e) => updateFormData('school_address', e.target.value)}
                    placeholder="العنوان الكامل للمدرسة"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {formData.role === 'delivery' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg arabic-text">معلومات شركة التوصيل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="delivery_company" className="arabic-text">اسم الشركة *</Label>
                  <Input id="delivery_company"
                    value={formData.delivery_company || ''}
                    onChange={(e) => updateFormData('delivery_company', e.target.value)}
                    placeholder="شركة التوصيل السريع"
                    className={errors.delivery_company ? 'border-red-500' : ''}
                  />
                  {errors.delivery_company && (
                    <p className="text-sm text-red-500 arabic-text">{errors.delivery_company}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="delivery_license" className="arabic-text">رقم الترخيص</Label>
                  <Input id="delivery_license"
                    value={formData.delivery_license || ''}
                    onChange={(e) => updateFormData('delivery_license', e.target.value)}
                    placeholder="رقم ترخيص الشركة"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* الإعدادات */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg arabic-text">إعدادات الحساب</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="arabic-text">حالة الحساب</div>
                  <p className="text-sm text-gray-500 arabic-text">
                    تحديد ما إذا كان الحساب نشطاً أم لا
                  </p>
                </div>
                <Switch
                  checked={formData.status === 'active'}
                  onCheckedChange={(checked) => updateFormData('status', checked ? 'active' : 'inactive')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="arabic-text">حساب محقق</div>
                  <p className="text-sm text-gray-500 arabic-text">
                    تحديد ما إذا كان الحساب محققاً أم لا
                  </p>
                </div>
                <Switch
                  checked={formData.verified}
                  onCheckedChange={(checked) => updateFormData('verified', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes" className="arabic-text">ملاحظات</Label>
                <Input id="notes"
                  value={formData.notes || ''}
                  onChange={(e) => updateFormData('notes', e.target.value)}
                  placeholder="ملاحظات إضافية حول المستخدم"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          < Footer>
            <Button type="button" variant="outline" onClick={onCancel}>
              إلغاء
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'جاري الإضافة...' : 'إضافة المستخدم'}
            </Button>
          </ Footer>
        </form>
      </TabsContent>
    </div>
  )
}
