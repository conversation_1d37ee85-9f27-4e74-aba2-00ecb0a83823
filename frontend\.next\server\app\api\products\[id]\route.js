(()=>{var e={};e.id=3856,e.ids=[3856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{AD:()=>d,P:()=>o,Rn:()=>n,wU:()=>u});var a=r(64939),i=e([a]);let c=new(a=(i.then?(await i)():i)[0]).Pool({user:process.env.POSTGRES_USER||"postgres",host:process.env.POSTGRES_HOST||"localhost",database:process.env.POSTGRES_DB||"graduation_platform",password:process.env.POSTGRES_PASSWORD||"password",port:parseInt(process.env.POSTGRES_PORT||"5432"),max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3,ssl:{rejectUnauthorized:!1}});async function E(){try{return await c.connect()}catch(e){throw Error("فشل في الاتصال بقاعدة البيانات")}}async function o(e,t){let r=await E();try{Date.now();let s=await r.query(e,t);return Date.now(),s}catch(e){throw e}finally{r.release()}}async function n(e){let t=await E();try{await t.query("BEGIN");let r=await e(t);return await t.query("COMMIT"),r}catch(e){throw await t.query("ROLLBACK"),e}finally{t.release()}}async function d(){try{return(await o("SELECT NOW() as current_time")).rows.length>0}catch(e){return!1}}async function u(){try{await o(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)"),await o("CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)"),await o("CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)"),await o("CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)"),await o("CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)"),await o("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),await o("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)")}catch(e){throw e}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21253:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{L:()=>E});var a=r(6710),i=e([a]);a=(i.then?(await i)():i)[0];class E{static async getAll(e={}){let t=[],r=[],s=1;e.category&&(t.push(`category_id = $${s}`),r.push(e.category),s++),void 0!==e.available&&(t.push(`is_available = $${s}`),r.push(e.available),s++),void 0!==e.published&&(t.push(`is_published = $${s}`),r.push(e.published),s++),e.search&&(t.push(`(name ILIKE $${s} OR description ILIKE $${s})`),r.push(`%${e.search}%`),s++),void 0!==e.minPrice&&(t.push(`price >= $${s}`),r.push(e.minPrice),s++),void 0!==e.maxPrice&&(t.push(`price <= $${s}`),r.push(e.maxPrice),s++);let i=t.length>0?`WHERE ${t.join(" AND ")}`:"",E=e.sortBy||"created_at",o=e.sortOrder||"DESC",n=`ORDER BY ${E} ${o}`,d=e.limit||50,u=e.offset||0,c=`LIMIT $${s} OFFSET $${s+1}`;r.push(d,u);let T=`SELECT COUNT(*) as total FROM products ${i}`,p=await (0,a.P)(T,r.slice(0,-2)),A=parseInt(p.rows[0].total),R=`
      SELECT 
        id, name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count, created_at, updated_at
      FROM products 
      ${i} 
      ${n} 
      ${c}
    `;return{products:(await (0,a.P)(R,r)).rows,total:A}}static async getById(e){return(await (0,a.P)("SELECT * FROM products WHERE id = $1",[e])).rows[0]||null}static async create(e){return(await (0,a.P)(`
      INSERT INTO products (
        name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
      ) RETURNING *
    `,[e.name,e.description,e.category_id,e.price,e.rental_price,e.colors,e.sizes,e.images,e.stock_quantity,e.is_available,e.is_published,e.features,JSON.stringify(e.specifications),e.rating||0,e.reviews_count||0])).rows[0]}static async update(e,t){let r=[],s=[],i=1;if(Object.entries(t).forEach(([e,t])=>{"id"!==e&&"created_at"!==e&&void 0!==t&&("specifications"===e?(r.push(`${e} = $${i}`),s.push(JSON.stringify(t))):(r.push(`${e} = $${i}`),s.push(t)),i++)}),0===r.length)throw Error("لا توجد حقول للتحديث");return r.push("updated_at = NOW()"),s.push(e),(await (0,a.P)(`
      UPDATE products 
      SET ${r.join(", ")} 
      WHERE id = $${i} 
      RETURNING *
    `,s)).rows[0]||null}static async delete(e){return(await (0,a.P)("DELETE FROM products WHERE id = $1",[e])).rowCount>0}static async updateRating(e){await (0,a.P)(`
      UPDATE products 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE product_id = $1
        ),
        reviews_count = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE product_id = $1
        ),
        updated_at = NOW()
      WHERE id = $1
    `,[e])}static async updateStock(e,t){return await (0,a.Rn)(async r=>{let s=await r.query("SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE",[e]);if(0===s.rows.length)throw Error("المنتج غير موجود");let a=s.rows[0].stock_quantity+t;if(a<0)throw Error("المخزون غير كافي");return(await r.query("UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2",[a,e])).rowCount>0})}static async search(e,t=20){return(await (0,a.P)(`
      SELECT * FROM products 
        is_published = true 
        AND is_available = true 
        AND (
          name ILIKE $1 
          OR description ILIKE $1 
          OR $2 = ANY(features)
        )
      ORDER BY 
          WHEN name ILIKE $1 THEN 1
          WHEN description ILIKE $1 THEN 2
          ELSE 3
        rating DESC
      LIMIT $3
    `,[`%${e}%`,e,t])).rows}static async getBestSellers(e=10){return(await (0,a.P)(`
      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.is_published = true AND p.is_available = true
        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))
      GROUP BY p.id
      ORDER BY total_sold DESC, p.rating DESC
      LIMIT $1
    `,[e])).rows}static async getNewProducts(e=10){return(await (0,a.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true
      ORDER BY created_at DESC
      LIMIT $1
    `,[e])).rows}static async getTopRated(e=10){return(await (0,a.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true AND rating > 0
      ORDER BY rating DESC, reviews_count DESC
      LIMIT $1
    `,[e])).rows}}s()}catch(e){s(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},66862:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>T});var a=r(96559),i=r(48088),E=r(37719),o=r(73904),n=e([o]);o=(n.then?(await n)():n)[0];let u=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:T,serverHooks:p}=u;function d(){return(0,E.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:T})}s()}catch(e){s(e)}})},73904:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{DELETE:()=>c,GET:()=>d,PUT:()=>u});var a=r(32190),i=r(21253),E=r(6710),o=r(38561),n=e([i,E]);async function d(e,{params:t}){try{if(!await (0,E.AD)()){let e=o.C.getProducts().find(e=>e.id===t.id);if(!e)return a.NextResponse.json({error:"المنتج غير موجود"},{status:404});return a.NextResponse.json({product:e,source:"mock_data"})}let{id:e}=t;if(!e)return a.NextResponse.json({error:"معرف المنتج مطلوب"},{status:400});let r=await i.L.getById(e);if(!r)return a.NextResponse.json({error:"المنتج غير موجود"},{status:404});return a.NextResponse.json({product:r,source:"postgresql"})}catch(r){let e=o.C.getProducts().find(e=>e.id===t.id);if(!e)return a.NextResponse.json({error:"المنتج غير موجود"},{status:404});return a.NextResponse.json({product:e,source:"mock_data_fallback"})}}async function u(e,{params:t}){try{let{id:r}=t,s=await e.json();if(!r)return a.NextResponse.json({error:"معرف المنتج مطلوب"},{status:400});if(!await i.L.getById(r))return a.NextResponse.json({error:"المنتج غير موجود"},{status:404});let E={};void 0!==s.name&&(E.name=s.name),void 0!==s.description&&(E.description=s.description),void 0!==s.category_id&&(E.category_id=s.category_id),void 0!==s.price&&(E.price=parseFloat(s.price)),void 0!==s.rental_price&&(E.rental_price=s.rental_price?parseFloat(s.rental_price):null),void 0!==s.colors&&(E.colors=Array.isArray(s.colors)?s.colors:[]),void 0!==s.sizes&&(E.sizes=Array.isArray(s.sizes)?s.sizes:[]),void 0!==s.images&&(E.images=Array.isArray(s.images)?s.images:[]),void 0!==s.stock_quantity&&(E.stock_quantity=parseInt(s.stock_quantity)),void 0!==s.is_available&&(E.is_available=s.is_available),void 0!==s.is_published&&(E.is_published=s.is_published),void 0!==s.features&&(E.features=Array.isArray(s.features)?s.features:[]),void 0!==s.specifications&&(E.specifications=s.specifications);let o=await i.L.update(r,E);if(!o)return a.NextResponse.json({error:"فشل في تحديث المنتج"},{status:500});return a.NextResponse.json({message:"تم تحديث المنتج بنجاح",product:o})}catch(e){return a.NextResponse.json({error:"فشل في تحديث المنتج"},{status:500})}}async function c(e,{params:t}){try{let{id:e}=t;if(!e)return a.NextResponse.json({error:"معرف المنتج مطلوب"},{status:400});if(!await i.L.getById(e))return a.NextResponse.json({error:"المنتج غير موجود"},{status:404});if(!await i.L.delete(e))return a.NextResponse.json({error:"فشل في حذف المنتج"},{status:500});return a.NextResponse.json({message:"تم حذف المنتج بنجاح",deletedProductId:e})}catch(e){return a.NextResponse.json({error:"فشل في حذف المنتج"},{status:500})}}[i,E]=n.then?(await n)():n,s()}catch(e){s(e)}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(66862));module.exports=s})();