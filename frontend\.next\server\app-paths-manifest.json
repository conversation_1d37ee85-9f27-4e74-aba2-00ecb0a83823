{"/api/ai-models/health-check/route": "app/api/ai-models/health-check/route.js", "/api/ai-models/[id]/route": "app/api/ai-models/[id]/route.js", "/api/ai-models/route": "app/api/ai-models/route.js", "/api/ai-providers/route": "app/api/ai-providers/route.js", "/api/ai-models/test/route": "app/api/ai-models/test/route.js", "/api/ai-models/usage/route": "app/api/ai-models/usage/route.js", "/api/categories/[id]/route": "app/api/categories/[id]/route.js", "/api/database/init/route": "app/api/database/init/route.js", "/api/ai/product-enhancement/route": "app/api/ai/product-enhancement/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/health/route": "app/api/health/route.js", "/api/database/seed/route": "app/api/database/seed/route.js", "/api/menu-items/[id]/route": "app/api/menu-items/[id]/route.js", "/api/menu-items/reorder/route": "app/api/menu-items/reorder/route.js", "/api/menu-items/route": "app/api/menu-items/route.js", "/api/orders/route": "app/api/orders/route.js", "/api/orders/[id]/route": "app/api/orders/[id]/route.js", "/api/orders/postgres/route": "app/api/orders/postgres/route.js", "/api/page-builder/export/route": "app/api/page-builder/export/route.js", "/api/page-builder/[id]/route": "app/api/page-builder/[id]/route.js", "/api/page-builder/route": "app/api/page-builder/route.js", "/api/pages/[id]/route": "app/api/pages/[id]/route.js", "/api/page-builder/generate/route": "app/api/page-builder/generate/route.js", "/api/pages/slug/[slug]/route": "app/api/pages/slug/[slug]/route.js", "/api/page-builder/templates/route": "app/api/page-builder/templates/route.js", "/api/pages/route": "app/api/pages/route.js", "/api/products/postgres/route": "app/api/products/postgres/route.js", "/api/placeholder/[...params]/route": "app/api/placeholder/[...params]/route.js", "/api/products/[id]/route": "app/api/products/[id]/route.js", "/api/schools/[id]/route": "app/api/schools/[id]/route.js", "/api/products/route": "app/api/products/route.js", "/api/schools/route": "app/api/schools/route.js", "/api/stats/route": "app/api/stats/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/users/route": "app/api/users/route.js", "/images/products/[...params]/route": "app/images/products/[...params]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/about/page": "app/about/page.js", "/admin/products/page": "app/admin/products/page.js", "/analytics/page": "app/analytics/page.js", "/_not-found/page": "app/_not-found/page.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/auth/page": "app/auth/page.js", "/blog/page": "app/blog/page.js", "/catalog/page": "app/catalog/page.js", "/contact/page": "app/contact/page.js", "/dashboard/admin/ai-models/page": "app/dashboard/admin/ai-models/page.js", "/dashboard/admin/categories/page": "app/dashboard/admin/categories/page.js", "/cart/page": "app/cart/page.js", "/customize/page": "app/customize/page.js", "/dashboard/admin/menu-management/page": "app/dashboard/admin/menu-management/page.js", "/dashboard/admin/checkout-settings/page": "app/dashboard/admin/checkout-settings/page.js", "/dashboard/admin/page": "app/dashboard/admin/page.js", "/dashboard/admin/delivery/page": "app/dashboard/admin/delivery/page.js", "/checkout/page": "app/checkout/page.js", "/dashboard/admin/page-builder/page": "app/dashboard/admin/page-builder/page.js", "/dashboard/admin/payment-methods/page": "app/dashboard/admin/payment-methods/page.js", "/dashboard/admin/schools/page": "app/dashboard/admin/schools/page.js", "/dashboard/admin/settings/page": "app/dashboard/admin/settings/page.js", "/dashboard/admin/pages-management/page": "app/dashboard/admin/pages-management/page.js", "/dashboard/admin/products/page": "app/dashboard/admin/products/page.js", "/dashboard/admin/users/page": "app/dashboard/admin/users/page.js", "/faq/page": "app/faq/page.js", "/dashboard/delivery/page": "app/dashboard/delivery/page.js", "/dashboard/page": "app/dashboard/page.js", "/notifications/page": "app/notifications/page.js", "/order-confirmation/page": "app/order-confirmation/page.js", "/dashboard/student/page": "app/dashboard/student/page.js", "/dashboard/school/page": "app/dashboard/school/page.js", "/pages/[slug]/page": "app/pages/[slug]/page.js", "/page": "app/page.js", "/payment/confirmation/page": "app/payment/confirmation/page.js", "/payment/bank-transfer/page": "app/payment/bank-transfer/page.js", "/privacy-policy/page": "app/privacy-policy/page.js", "/profile/page": "app/profile/page.js", "/product/[id]/page": "app/product/[id]/page.js", "/reviews/page": "app/reviews/page.js", "/track-order/page": "app/track-order/page.js", "/support/page": "app/support/page.js", "/terms-conditions/page": "app/terms-conditions/page.js", "/dashboard/admin/orders/page": "app/dashboard/admin/orders/page.js", "/unauthorized/page": "app/unauthorized/page.js", "/wishlist/page": "app/wishlist/page.js"}