(()=>{var e={};e.id=7378,e.ids=[7378],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},82216:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>l,POST:()=>d});var o=s(96559),n=s(48088),a=s(37719),i=s(32190),u=s(38561);async function d(e){try{let{modelId:t,subModelId:s,prompt:r,settings:o}=await e.json();if(!t||!r)return i.NextResponse.json({error:"معرف النموذج والنص المطلوب مطلوبان"},{status:400});let n=u.C.getAIModels(),a=n.find(e=>e.id===t);if(!a)return i.NextResponse.json({error:"النموذج غير موجود"},{status:404});if(!a.isActive)return i.NextResponse.json({error:"النموذج غير نشط"},{status:400});let d=null;if(s){if(!(d=a.subModels.find(e=>e.id===s)))return i.NextResponse.json({error:"النموذج الفرعي غير موجود"},{status:404});if(!d.isActive)return i.NextResponse.json({error:"النموذج الفرعي غير نشط"},{status:400})}Date.now();let l=("openai"===a.provider?1e3:"anthropic"===a.provider?1500:"google"===a.provider?800:1200)+Math.floor(1e3*Math.random()),p=Math.random()>.05,c=Math.ceil(r.length/4),g=p?Math.floor(500*Math.random())+50:0,m=c+g,x=d?.pricing||{inputTokens:.001,outputTokens:.002,currency:"USD"},f=p?c/1e3*x.inputTokens+g/1e3*x.outputTokens:0,h={success:p,responseTime:l,tokensUsed:m,cost:f,metadata:{model:a.name,subModel:d?.name,provider:a.provider,promptTokens:c,completionTokens:g,settings:{...a.settings,...o}}};if(p){let e=["مرحباً! أنا مساعد ذكي جاهز لمساعدتك في أي استفسار.","شكراً لك على اختبار النموذج. يبدو أن كل شيء يعمل بشكل صحيح.","هذا اختبار ناجح للنموذج. يمكنني مساعدتك في مختلف المهام.","النموذج يعمل بكفاءة عالية ومستعد للاستخدام.","تم اختبار النموذج بنجاح. جودة الاستجابة ممتازة."];h.response=e[Math.floor(Math.random()*e.length)]}else h.error="فشل في الاتصال بالنموذج. يرجى التحقق من إعدادات API.";let b=n.findIndex(e=>e.id===t);if(-1!==b){let e=n[b];if(e.lastTestedAt=new Date().toISOString(),e.testResult={success:p,responseTime:l,error:p?void 0:h.error},e.status=p?"active":"error",p){e.usage.totalRequests+=1,e.usage.totalTokens+=m,e.usage.totalCost+=f,e.usage.lastUsed=new Date().toISOString();let t=e.usage.averageResponseTime*(e.usage.totalRequests-1)+l;e.usage.averageResponseTime=Math.round(t/e.usage.totalRequests);let s=Math.round(e.usage.successRate*(e.usage.totalRequests-1)/100)+1;e.usage.successRate=Math.round(s/e.usage.totalRequests*100)}e.updatedAt=new Date().toISOString(),n[b]=e,u.C.saveAIModels(n)}let v=u.C.getModelActivities();return v.push({id:u.C.generateId(),modelId:t,subModelId:s,type:"test",description:`اختبار النموذج: ${p?"نجح":"فشل"}`,details:{prompt:r.substring(0,100)+(r.length>100?"...":""),settings:{...a.settings,...o}},timestamp:new Date().toISOString(),duration:l,tokensUsed:m,cost:f,success:p,errorMessage:p?void 0:h.error}),u.C.saveModelActivities(v),i.NextResponse.json(h)}catch(e){return i.NextResponse.json({error:"خطأ في اختبار النموذج"},{status:500})}}async function l(e){try{let{searchParams:t}=new URL(e.url),s=t.get("model_id"),r=parseInt(t.get("limit")||"20"),o=u.C.getModelActivities();o=o.filter(e=>"test"===e.type),s&&(o=o.filter(e=>e.modelId===s)),o.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),o=o.slice(0,r);let n=u.C.getModelActivities().filter(e=>"test"===e.type),a={total:n.length,successful:n.filter(e=>e.success).length,failed:n.filter(e=>!e.success).length,averageResponseTime:n.length>0?Math.round(n.reduce((e,t)=>e+(t.duration||0),0)/n.length):0,totalCost:n.reduce((e,t)=>e+(t.cost||0),0),byModel:n.reduce((e,t)=>{let s=t.modelId;return e[s]||(e[s]={total:0,successful:0,failed:0}),e[s].total+=1,t.success?e[s].successful+=1:e[s].failed+=1,e},{})};return i.NextResponse.json({tests:o,stats:a,total:o.length})}catch(e){return i.NextResponse.json({error:"خطأ في جلب نتائج الاختبارات"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai-models/test/route",pathname:"/api/ai-models/test",filename:"route",bundlePath:"app/api/ai-models/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\test\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:c,workUnitAsyncStorage:g,serverHooks:m}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:g})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(82216));module.exports=r})();