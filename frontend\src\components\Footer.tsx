
"use client"

import Link from 'next/link'
import { useTranslation } from '@/hooks/useTranslation'
import {
  import {
  GraduationCap,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Heart,
  GraduationCap
} from 'lucide-react'

export function Footer() {
  const { t } = useTranslation()

  const currentYear = new Date().getFullYear()

  const footerLinks = {
    company: [
      { href: '/about', label: 'من نحن' },
      { href: '/contact', label: 'تواصل معنا' },
      { href: '/support', label: 'الدعم الفني' },
      { href: '/privacy', label: 'سياسة الخصوصية' }
    ],
    services: [
      { href: '/catalog', label: 'الكتالوج' },
      { href: '/customize', label: 'التخصيص' },
      { href: '/track-order', label: 'تتبع الطلب' },
      { href: '/size-guide', label: 'دليل المقاسات' }
    ],
    support: [
      { href: '/faq', label: 'الأسئلة الشائعة' },
      { href: '/terms-conditions', label: 'الشروط والأحكام' },
      { href: '/privacy-policy', label: 'سياسة الخصوصية' },
      { href: '/support', label: 'الدعم الفني' }
    ]
  }

  const socialLinks = [
    { href: '#', icon: Facebook, label: 'Facebook' },
    { href: '#', icon: Twitter, label: 'Twitter' },
    { href: '#', icon: Instagram, label: 'Instagram' },
    { href: '#', icon: Linkedin, label: 'LinkedIn' }
  ]

  return (
    <footer className="bg-gray-900 text-white mt-16">
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <GraduationCap className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold">Graduation Toqs</span>
            </div>
            <p className="text-gray-300 arabic-text leading-relaxed">
              أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي
            </p>
            
            {/* Contact Info */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-blue-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-blue-400" />
                <span>+212 6 12 34 56 78</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-blue-400" />
                <span className="arabic-text">بني ملال، المغرب</span>
              </div>
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 arabic-text">الشركة</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors arabic-text"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 arabic-text">الخدمات</h3>
            <ul className="space-y-2">
              {footerLinks.services.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors arabic-text"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 arabic-text">الدعم</h3>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors arabic-text"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Media & Copyright */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Social Links */}
            <div className="flex items-center gap-4">
              <span className="text-gray-400 arabic-text">تابعنا على:</span>
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    className="text-gray-400 hover:text-blue-400 transition-colors"
                    aria-label={social.label}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                )
              })}
            </div>

            {/* Copyright */}
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-sm arabic-text">
                © {currentYear} Graduation Toqs. جميع الحقوق محفوظة
              </p>
              <p className="text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1">
                <span className="arabic-text">صُنع بـ</span>
                <Heart className="h-3 w-3 text-red-500" />
                <span className="arabic-text">في المغرب</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
