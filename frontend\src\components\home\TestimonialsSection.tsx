
"use client"

import { useState, useEffect, useRef } from 'react'
import { useTranslation } from "@/hooks/useTranslation"
import { useTestimonials, type Testimonial } from "@/hooks/useTestimonials"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {  Fallback, Image } from "@/components/ui/avatar"
import { Quote,
  ArrowLeft,
  ArrowRight } from "lucide-react"

interface TestimonialCardProps {
  testimonial: Testimonial
  delay: number
}

function TestimonialCard({ testimonial, delay }: TestimonialCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 relative overflow-hidden">
        {/* Quote Icon */}
        <div className="absolute top-4 right-4 text-blue-100 dark:text-blue-900">
          <Quote className="w-8 h-8" />
        </div>

        <CardContent className="p-6">
          {/* Rating */}
          <div className="flex items-center gap-1 mb-4">
            {[...Array(5)].map((_, i) => (
              < key={i}
                className={`w-4 h-4 ${
                  i < testimonial.rating
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>

          {/* Content */}
          <p className="text-gray-700 dark:text-gray-300 arabic-text leading-relaxed mb-6 text-lg">
            "{testimonial.content}"
          </p>

          {/* Author */}
          <div className="flex items-center gap-4">
            <div className="w-12 h-12">
              < Image src={testimonial.avatar} alt={testimonial.name} />
              < Fallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                {testimonial.name.split(' ').map(n => n[0]).join('')}
              </ Fallback>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white arabic-text">
                {testimonial.name}
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400 arabic-text">
                {testimonial.role}
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400 arabic-text">
                {testimonial.university}
              </p>
            </div>
          </div>

          {/* Date */}
          <div className="mt-4 text-xs text-gray-400">
            {testimonial.date}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function TestimonialsSection() {
  const { } = useTranslation() // ✅ إصلاح: إزالة متغير غير مستخدم
  const { testimonials, loading } = useTestimonials()
  const [currentIndex, setCurrentIndex] = useState(0)

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.ceil(testimonials.length / 3))
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + Math.ceil(testimonials.length / 3)) % Math.ceil(testimonials.length / 3))
  }

  const visibleTestimonials = testimonials.slice(currentIndex * 3, (currentIndex + 1) * 3)

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <div className="w-4 h-4" />
            آراء العملاء
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            {t('home.testimonials.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text">
            {t('home.testimonials.subtitle')}
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {visibleTestimonials.map((testimonial, index) => (
            <TestimonialCard
              key={testimonial.id}
              testimonial={testimonial}
              delay={index * 200}
            />
          ))}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={prevTestimonial}
            className="w-10 h-10 p-0 rounded-full"
          >
            <ArrowRight className="w-4 h-4" />
          </Button>
          
          <div className="flex gap-2">
            {Array.from({ length: Math.ceil(testimonials.length / 3) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={nextTestimonial}
            className="w-10 h-10 p-0 rounded-full"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <Button variant="ghost" className="text-blue-600 dark:text-blue-400 arabic-text">
            {t('home.testimonials.viewAll')}
          </Button>
        </div>
      </div>
    </section>
  )
}
