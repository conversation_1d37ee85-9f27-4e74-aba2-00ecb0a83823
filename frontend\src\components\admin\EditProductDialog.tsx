
"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle
} from '@/components/ui/dialog'
import { ProductForm } from './ProductForm'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

interface Product {
  id: string
  name: string
  description: string
  category: string
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
  rating?: number
  reviews_count?: number
  features?: string[]
  specifications?: Record<string, any>
}

interface EditProductDialogProps {
  product: Product | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (productData: any) => Promise<void>
}

export function EditProductDialog({
  product,
  open,
  onOpenChange,
  onSave
}: EditProductDialogProps) {
  const [loading, setLoading] = useState(false)

  // تحويل بيانات المنتج إلى تنسيق النموذج
  const convertProductToFormData = (product: Product) => {
    return {
      name: product.name,
      description: product.description,
      category: product.category,
      price: product.price,
      rental_price: product.rental_price,
      colors: product.colors,
      sizes: product.sizes,
      images: product.images.map((url, index) => ({
        id: `existing-${index}`,
        file: new File([], 'existing-image'),
        preview: url,
        uploaded: true,
        fallbackUrl: url
      })),
      stock_quantity: product.stock_quantity,
      is_available: product.is_available,
      features: product.features || [],
      specifications: product.specifications || {}
    }
  }

  const handleSubmit = async (formData: any) => {
    try {
      setLoading(true)
      await onSave({
        ...formData,
        id: product?.id
      })
      onOpenChange(false)
    } catch (error) {
      console.error('Error updating product:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="arabic-text">
            تعديل المنتج: {product.name}
          </DialogTitle>
        </DialogHeader>
        
        <ProductForm
          initialData={convertProductToFormData(product)}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isEditing={true}
        />
      </DialogContent>
    </Dialog>
  )
}
