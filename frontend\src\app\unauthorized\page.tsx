
"use client"

import { useTranslation } from '@/hooks/useTranslation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'
import {
  Shield,
  ArrowLeft
} from 'lucide-react'

export default function UnauthorizedPage() {
  const { t } = useTranslation()

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
            <Shield className="h-10 w-10 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-red-600 dark:text-red-400">
            غير مصرح
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-300">
            ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة
          </p>
          <div className="flex flex-col gap-2">
            <Button asChild>
              <Link href="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                العودة للرئيسية
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/auth">
                تسجيل دخول بحساب آخر
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
