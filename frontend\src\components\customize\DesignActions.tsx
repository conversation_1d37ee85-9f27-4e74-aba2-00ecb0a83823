
"use client"

import {
  useState
} from 'react'
import {
  Button
} from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  Content,
  Description,
  Header,
  Title,
  Trigger,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  Copy,
  import { Label } from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
} from '@/components/ui/tabs'
  Facebook,
  Twitter,
  Instagram,
  MessageCircle,
  Link,
  QrCode,
  Printer,
  Facebook
} from 'lucide-react'

interface DesignActionsProps {
  designData: any
  designName?: string
  on ?: (name: string, description: string) => void
  onShare?: (platform: string) => void
  className?: string
}

export function DesignActions({
  designData,
  designName = "تصميمي المخصص",
  onShare,
  className = ""
}: DesignActionsProps) {
  const [saveOpen, setSaveOpen] = useState(false)
  const [shareOpen, setShareOpen] = useState(false)
  const [designTitle, setDesignTitle] = useState(designName)
  const [designDescription, setDesignDescription] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [shareUrl] = useState(`https://graduation-toqs.com/design/${Date.now()}`)

  const handle = async () => {
    if (!designTitle.trim()) return
    
    setIsSaving(true)
    try {
      await on ?.(designTitle, designDescription)
      setSaveOpen(false)
      // Show success message
    } catch (_error) {
      // Show error message
    } finally {
      setIsSaving(false)
    }
  }

  const handleShare = (platform: string) => {
    onShare?.(platform)
    
    const text = `شاهد تصميم زي التخرج المخصص الخاص بي على Graduation Toqs!`
    const url = shareUrl

    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank')
        break
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank')
        break
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`, '_blank')
        break
      case 'copy':
        navigator.clipboard.writeText(url)
        break
    }
  }

  const handle = (format: string) => {
    // Implement download functionality}

  const handlePrint = () => {
    window.print()
  }

  const shareOptions = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: <Facebook className="h-5 w-5" />,
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: <Twitter className="h-5 w-5" />,
      color: 'bg-sky-500 hover:bg-sky-600'
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: <Instagram className="h-5 w-5" />,
      color: 'bg-pink-600 hover:bg-pink-700'
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: <MessageCircle className="h-5 w-5" />,
      color: 'bg-green-600 hover:bg-green-700'
    }
  ]

  const downloadOptions = [
    { format: 'png', name: 'صورة PNG', description: 'جودة عالية للطباعة' },
    { format: 'jpg', name: 'صورة JPG', description: 'حجم أصغر للمشاركة' },
    { format: 'pdf', name: 'ملف PDF', description: 'للطباعة الاحترافية' },
    { format: 'svg', name: 'ملف SVG', description: 'قابل للتحرير' }
  ]

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
        {/* Design */}
        <Dialog open={saveOpen} onOpenChange={setSaveOpen}>
          <SelectTrigger asChild>
            <Button variant="outline" className="arabic-text">
              <div className="h-4 w-4 mr-2" />
              حفظ التصميم
            </Button>
          </TabsTrigger>
          <SelectContent>
            < Header>
              < Title className="arabic-text">حفظ التصميم</ Title>
              < Description className="arabic-text">
                احفظ تصميمك المخصص لتتمكن من الوصول إليه لاحقاً
              </ Description>
            </ Header>
            <div className="space-y-4">
              <div>
                <Label htmlFor="design-title" className="arabic-text">اسم التصميم</Label>
                <Input id="design-title"
                  value={designTitle}
                  onChange={(e) => setDesignTitle(e.target.value)}
                  placeholder="أدخل اسم التصميم"
                  className="arabic-text"
                />
              </div>
              <div>
                <Label htmlFor="design-description" className="arabic-text">وصف التصميم (اختياري)</Label>
                <Input id="design-description"
                  value={designDescription}
                  onChange={(e) => setDesignDescription(e.target.value)}
                  placeholder="أضف وصفاً لتصميمك..."
                  className="arabic-text"
                  rows={3}
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={handle } 
                  disabled={!designTitle.trim() || isSaving}
                  className="flex-1 arabic-text"
                >
                  {isSaving ? 'جاري الحفظ...' : 'حفظ'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setSaveOpen(false)}
                  className="arabic-text"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </TabsContent>
        </div>

        {/* Share Design */}
        <Dialog open={shareOpen} onOpenChange={setShareOpen}>
          <TabsTrigger asChild>
            <Button variant="outline" className="arabic-text">
              <div className="h-4 w-4 mr-2" />
              مشاركة
            </Button>
          </TabsTrigger>
          <SelectContent>
            < Header>
              < Title className="arabic-text">مشاركة التصميم</ Title>
              < Description className="arabic-text">
                شارك تصميمك المميز مع الأصدقاء والعائلة
              </ Description>
            </ Header>
            <div className="space-y-4">
              {/* Share URL */}
              <div>
                <div className="arabic-text">رابط التصميم</div>
                <div className="flex gap-2">
                  <Input value={shareUrl} readOnly className="flex-1" />
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleShare('copy')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Social Media Buttons */}
              <div>
                <div className="arabic-text mb-3 block">مشاركة على وسائل التواصل</div>
                <div className="grid grid-cols-2 gap-3">
                  {shareOptions.map((option) => (
                    <Button
                      key={option.id}
                      variant="outline"
                      onClick={() => handleShare(option.id)}
                      className={`${option.color} text-white border-0 arabic-text`}
                    >
                      {option.icon}
                      <span className="mr-2">{option.name}</span>
                    </Button>
                  ))}
                </div>
              </div>

              {/* QR Code */}
              <div className="text-center">
                <div className="w-32 h-32 bg-gray-100 dark:bg-gray-800 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <QrCode className="h-16 w-16 text-gray-400" />
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  رمز QR للمشاركة السريعة
                </p>
              </div>
            </div>
          </TabsContent>
        </div>
      </div>

      {/* Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg arabic-text">تحميل التصميم</CardTitle>
          <div className="arabic-text">
            احصل على نسخة من تصميمك بصيغ مختلفة
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-3">
            {downloadOptions.map((option) => (
              <button
                key={option.format}
                onClick={() => handle (option.format)}
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="text-left">
                  <div className="font-medium arabic-text">{option.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                    {option.description}
                  </div>
                </div>
                <div className="h-5 w-5 text-gray-400" />
              </button>
            ))}
          </div>

          <Button 
            variant="outline" 
            onClick={handlePrint}
            className="w-full mt-4 arabic-text"
          >
            <Printer className="h-4 w-4 mr-2" />
            طباعة التصميم
          </Button>
        </CardContent>
      </Card>

      {/* Design Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg arabic-text">إحصائيات التصميم</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">12</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">مشاهدة</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">3</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إعجاب</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add to Favorites */}
      <Button variant="outline" className="w-full arabic-text">
        <div className="h-4 w-4 mr-2" />
        إضافة للمفضلة
      </Button>
    </div>
  )
}
