
"use client"

import {
  useState,
  useEffect
} from 'react'
import {
  Navigation
} from '@/components/Navigation'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Button
} from '@/components/ui/button'

import {
  Switch
} from '@/components/ui/switch'
import {
  Ta<PERSON>,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'

import {
  Separator
} from '@/components/ui/separator'
import { CheckoutSettingsManager,
  CheckoutSettings,
  CheckoutField,
  PaymentMethodConfig,
  DeliveryOption
} from '@/lib/checkoutSettings'




import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  Plus,
  Save,
  Eye,
  Truck
} from 'lucide-react'

export default function CheckoutSettingsPage() {
  const [settings, setSettings] = useState<CheckoutSettings | null>(null)
  const [activeTab, setActiveTab] = useState('fields')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    const loadSettings = () => {
      const currentSettings = CheckoutSettingsManager.getSettings()
      setSettings(currentSettings)
      setLoading(false)
    }
    loadSettings()
  }, [])

  const handleSaveSettings = () => {
    if (!settings) return
    setSaving(true)
    CheckoutSettingsManager.saveSettings(settings)
    setTimeout(() => setSaving(false), 1000)
  }

  const handleResetToDefaults = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
      CheckoutSettingsManager.resetToDefaults()
      setSettings(CheckoutSettingsManager.getSettings())
    }
  }

  const toggleFieldEnabled = (fieldId: string) => {
    if (!settings) return
    const updatedFields = settings.fields.map(field =>
      field.id === fieldId ? { ...field, enabled: !field.enabled } : field
    )
    setSettings({ ...settings, fields: updatedFields })
  }

  const togglePaymentMethodEnabled = (methodId: string) => {
    if (!settings) return
    const updatedMethods = settings.paymentMethods.map(method =>
      method.id === methodId ? { ...method, enabled: !method.enabled } : method
    )
    setSettings({ ...settings, paymentMethods: updatedMethods })
  }

  const toggleDeliveryOptionEnabled = (optionId: string) => {
    if (!settings) return
    const updatedOptions = settings.deliveryOptions.map(option =>
      option.id === optionId ? { ...option, enabled: !option.enabled } : option
    )
    setSettings({ ...settings, deliveryOptions: updatedOptions })
  }

  if (loading || !settings) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">جاري تحميل الإعدادات...</p>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button variant="outline" size="sm" asChild className="mb-4">
            <a href="/dashboard/admin">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة للوحة التحكم
            </a>
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                ⚙️ إعدادات صفحة الدفع
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                تخصيص حقول ومعلومات صفحة إتمام الطلب
              </p>
            </div>
            
            <div className="flex gap-3">
              <Button variant="outline" onClick={handleResetToDefaults}>
                <RotateCcw className="h-4 w-4 mr-2" />
                إعادة تعيين
              </Button>
              <Button onClick={handleSaveSettings} disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="fields" className="arabic-text">
              <User className="h-4 w-4 mr-2" />
              الحقول
            </TabsTrigger>
            <TabsTrigger value="payment" className="arabic-text">
              <CreditCard className="h-4 w-4 mr-2" />
              طرق الدفع
            </TabsTrigger>
            <TabsTrigger value="delivery" className="arabic-text">
              <Truck className="h-4 w-4 mr-2" />
              التوصيل
            </TabsTrigger>
            <TabsTrigger value="general" className="arabic-text">
              <Settings className="h-4 w-4 mr-2" />
              عام
            </TabsTrigger>
          </TabsList>

          {/* Fields Tab */}
          <TabsContent value="fields" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إدارة حقول المعلومات</CardTitle>
                <CardDescription className="arabic-text">
                  تحكم في الحقول المطلوبة في صفحة الدفع
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {settings.fields
                  .sort((a, b) => a.order - b.order)
                  .map((field) => (
                    <div key={field.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <GripVertical className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium arabic-text">{field.label}</h4>
                            {field.required && (
                              <Badge variant="destructive" className="text-xs">مطلوب</Badge>
                            )}
                            <Badge variant="outline" className="text-xs">{field.type}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            القسم: {field.section === 'personal' ? 'شخصي' : 
                                   field.section === 'shipping' ? 'الشحن' : 'الفواتير'}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={field.enabled}
                          onCheckedChange={() => toggleFieldEnabled(field.id)}
                        />
                        {field.enabled ? (
                          <Eye className="h-4 w-4 text-green-600" />
                        ) : (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        )}
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                
                <Button variant="outline" className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة حقل جديد
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payment Methods Tab */}
          <TabsContent value="payment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">طرق الدفع المتاحة</CardTitle>
                <CardDescription className="arabic-text">
                  تفعيل أو إلغاء طرق الدفع المختلفة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {settings.paymentMethods
                  .sort((a, b) => a.order - b.order)
                  .map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <CreditCard className="h-5 w-5 text-blue-600" />
                        <div>
                          <h4 className="font-medium arabic-text">{method.name}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {method.description}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={method.enabled}
                          onCheckedChange={() => togglePaymentMethodEnabled(method.id)}
                        />
                        {method.enabled ? (
                          <Badge variant="default">مفعل</Badge>
                        ) : (
                          <Badge variant="secondary">معطل</Badge>
                        )}
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Delivery Options Tab */}
          <TabsContent value="delivery" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">خيارات التوصيل</CardTitle>
                <CardDescription className="arabic-text">
                  إدارة طرق وأسعار التوصيل المختلفة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {settings.deliveryOptions
                  .sort((a, b) => a.order - b.order)
                  .map((option) => (
                    <div key={option.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Truck className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium arabic-text">{option.name}</h4>
                            <Badge variant="outline">{option.price} درهم</Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {option.description} - {option.estimatedDays}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={option.enabled}
                          onCheckedChange={() => toggleDeliveryOptionEnabled(option.id)}
                        />
                        {option.enabled ? (
                          <Badge variant="default">متاح</Badge>
                        ) : (
                          <Badge variant="secondary">غير متاح</Badge>
                        )}
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                
                <Button variant="outline" className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة خيار توصيل جديد
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* General Settings Tab */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الإعدادات العامة</CardTitle>
                <CardDescription className="arabic-text">
                  إعدادات عامة لصفحة الدفع
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label className="arabic-text">العملة الافتراضية</Label>
                    <Input 
                      value={settings.general.currency}
                      onChange={(e) => setSettings({
                        ...settings,
                        general: { ...settings.general, currency: e.target.value }
                      })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="arabic-text">البلد الافتراضي</Label>
                    <Input 
                      value={settings.general.defaultCountry}
                      onChange={(e) => setSettings({
                        ...settings,
                        general: { ...settings.general, defaultCountry: e.target.value }
                      })}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">طلب الموافقة على الشروط والأحكام</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                        إجبار المستخدمين على الموافقة قبل إتمام الطلب
                      </p>
                    </div>
                    <Switch
                      checked={settings.general.requireTermsAcceptance}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        general: { ...settings.general, requireTermsAcceptance: checked }
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">السماح بالدفع كضيف</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                        السماح للمستخدمين بإتمام الطلب بدون تسجيل
                      </p>
                    </div>
                    <Switch
                      checked={settings.general.allowGuestCheckout}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        general: { ...settings.general, allowGuestCheckout: checked }
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">عرض ملخص الطلب</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                        إظهار تفاصيل الطلب والأسعار
                      </p>
                    </div>
                    <Switch
                      checked={settings.general.showOrderSummary}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        general: { ...settings.general, showOrderSummary: checked }
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">تفعيل التعليمات الخاصة</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                        السماح للعملاء بإضافة ملاحظات خاصة
                      </p>
                    </div>
                    <Switch
                      checked={settings.general.enableSpecialInstructions}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        general: { ...settings.general, enableSpecialInstructions: checked }
                      })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
