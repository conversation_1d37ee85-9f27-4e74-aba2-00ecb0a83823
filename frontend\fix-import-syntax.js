#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixImportSyntax(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix the specific pattern: import { \n import { ... } from '...'
    const brokenImportPattern = /import\s*{\s*\nimport\s*{\s*([^}]+)\s*}\s*from\s*['"]([^'"]+)['"]/g;
    
    content = content.replace(brokenImportPattern, (match, imports, source) => {
      modified = true;
      return `import { ${imports.trim()} } from '${source}'`;
    });
    
    // Fix pattern: import { \n  SomeComponent, \n  AnotherComponent \n} from '...'
    // where there's a broken import in the middle
    const lines = content.split('\n');
    const fixedLines = [];
    let i = 0;
    
    while (i < lines.length) {
      const line = lines[i].trim();
      
      // Check if this is a broken import pattern
      if (line.startsWith('import {') && !line.includes('} from')) {
        // This is the start of a multi-line import
        let importLines = [line];
        let j = i + 1;
        
        // Look for the end of the import or a broken import
        while (j < lines.length) {
          const nextLine = lines[j].trim();
          
          if (nextLine.startsWith('import {') && nextLine.includes('} from')) {
            // This is a broken duplicate import, skip it
            modified = true;
            j++;
            continue;
          }
          
          importLines.push(lines[j]);
          
          if (nextLine.includes('} from')) {
            break;
          }
          j++;
        }
        
        // Add the fixed import
        fixedLines.push(...importLines);
        i = j + 1;
      } else {
        fixedLines.push(lines[i]);
        i++;
      }
    }
    
    if (modified) {
      const newContent = fixedLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ Fixed import syntax: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No import syntax fixes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing import syntax in ${filePath}:`, error.message);
    return false;
  }
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Fixing import syntax errors...');
console.log(`📁 Scanning directory: ${srcDir}`);

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} TypeScript/TSX files`);

let fixedCount = 0;
let totalCount = tsxFiles.length;

tsxFiles.forEach(file => {
  if (fixImportSyntax(file)) {
    fixedCount++;
  }
});

console.log('\n🎉 Import Syntax Fix Summary:');
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`📄 Total files: ${totalCount}`);
console.log(`⏭️  Files unchanged: ${totalCount - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n🚀 Restart the dev server to see the fixes!');
} else {
  console.log('\n✨ All import syntax is clean!');
}
