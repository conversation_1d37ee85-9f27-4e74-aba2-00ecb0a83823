
"use client"

import { useState, useEffect } from 'react'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { PageLayout } from '@/components/layouts/PageLayout'
import { EnhancedImage } from '@/components/ui/enhanced-image'
import { SimpleProductForm } from '@/components/admin/SimpleProductForm'
import {
  import {
import { toast
} from 'sonner'

import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  AlertCircle,
  Plus,
  Eye,
  RefreshCw
} from 'lucide-react'

interface Product {
  id: string
  name: string
  description?: string
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  is_published: boolean
  features: string[]
  rating: number
  reviews_count: number
  created_at: string
  updated_at: string
}

export default function AdminProductsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [seeding, setSeeding] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | undefined>(undefined)
  const [formLoading, setFormLoading] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    published: 0,
    available: 0,
    totalValue: 0
  })

  // جلب المنتجات
  const fetchProducts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/products')
      const data = await response.json()
      
      if (response.ok) {
        setProducts(data.products || [])
        calculateStats(data.products || [])
      } else {
        toast.error(data.error || 'فشل في جلب المنتجات')
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  // حساب الإحصائيات
  const calculateStats = (productList: Product[]) => {
    const stats = {
      total: productList.length,
      published: productList.filter(p => p.is_published).length,
      available: productList.filter(p => p.is_available).length,
      totalValue: productList.reduce((sum, p) => sum + (p.price * p.stock_quantity), 0)
    }
    setStats(stats)
  }

  // إضافة البيانات الأولية
  const seedDatabase = async () => {
    try {
      setSeeding(true)
      const response = await fetch('/api/database/seed', {
        method: 'POST'
      })
      const data = await response.json()
      
      if (response.ok) {
        toast.success(data.message)
        fetchProducts() // إعادة جلب المنتجات
      } else {
        toast.error(data.error || 'فشل في إضافة البيانات الأولية')
      }
    } catch (error) {
      console.error('Error seeding database:', error)
      toast.error('خطأ في إضافة البيانات الأولية')
    } finally {
      setSeeding(false)
    }
  }

  // حذف منتج
  const deleteProduct = async (productId: string, productName: string) => {
    if (!confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
      return
    }

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE'
      })
      const data = await response.json()
      
      if (response.ok) {
        toast.success(data.message)
        fetchProducts() // إعادة جلب المنتجات
      } else {
        toast.error(data.error || 'فشل في حذف المنتج')
      }
    } catch (error) {
      console.error('Error deleting product:', error)
      toast.error('خطأ في حذف المنتج')
    }
  }

  // تبديل حالة النشر
  const togglePublished = async (productId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_published: !currentStatus
        })
      })
      const data = await response.json()
      
      if (response.ok) {
        toast.success(data.message)
        fetchProducts() // إعادة جلب المنتجات
      } else {
        toast.error(data.error || 'فشل في تحديث المنتج')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error('خطأ في تحديث المنتج')
    }
  }

  // إنشاء منتج جديد
  const createProduct = async (productData: Product) => {
    try {
      setFormLoading(true)
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      })
      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        setShowForm(false)
        fetchProducts()
      } else {
        toast.error(data.error || 'فشل في إنشاء المنتج')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('خطأ في إنشاء المنتج')
    } finally {
      setFormLoading(false)
    }
  }

  // تحديث منتج موجود
  const updateProduct = async (productData: Product) => {
    if (!editingProduct?.id) return

    try {
      setFormLoading(true)
      const response = await fetch(`/api/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      })
      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        setShowForm(false)
        setEditingProduct(undefined)
        fetchProducts()
      } else {
        toast.error(data.error || 'فشل في تحديث المنتج')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error('خطأ في تحديث المنتج')
    } finally {
      setFormLoading(false)
    }
  }

  // إدارة حفظ النموذج
  const handleFormSave = (productData: Product) => {
    if (editingProduct) {
      updateProduct(productData)
    } else {
      createProduct(productData)
    }
  }

  // إلغاء النموذج
  const handleFormCancel = () => {
    setShowForm(false)
    setEditingProduct(undefined)
  }

  // فتح نموذج إضافة منتج جديد
  const openAddForm = () => {
    setEditingProduct(undefined)
    setShowForm(true)
  }

  // فتح نموذج تعديل منتج
  const openEditForm = (product: Product) => {
    setEditingProduct(product)
    setShowForm(true)
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />
      <PageLayout containerClassName="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
                🛍️ إدارة المنتجات
              </h1>
              <p className="text-gray-600 dark:text-gray-400 arabic-text">
                إدارة وتحرير منتجات المنصة
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={fetchProducts}
                variant="outline"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
              <Button
                onClick={seedDatabase}
                variant="outline"
                disabled={seeding || products.length > 0}
              >
                <Database className={`h-4 w-4 mr-2 ${seeding ? 'animate-pulse' : ''}`} />
                {seeding ? 'جاري الإضافة...' : 'إضافة بيانات أولية'}
              </Button>
              <Button onClick={openAddForm}>
                <Plus className="h-4 w-4 mr-2" />
                إضافة منتج جديد
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي المنتجات</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total}</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">منشور</p>
                    <p className="text-2xl font-bold text-green-600">{stats.published}</p>
                  </div>
                  <Eye className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متاح</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.available}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">قيمة المخزون</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.totalValue.toFixed(2)} Dhs</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Products List */}
        {loading ? (
          <div className="text-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600 dark:text-gray-400 arabic-text">جاري تحميل المنتجات...</p>
          </div>
        ) : products.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 arabic-text">
                لا توجد منتجات
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4 arabic-text">
                لم يتم العثور على أي منتجات في قاعدة البيانات
              </p>
              <Button onClick={seedDatabase} disabled={seeding}>
                <Database className={`h-4 w-4 mr-2 ${seeding ? 'animate-pulse' : ''}`} />
                {seeding ? 'جاري الإضافة...' : 'إضافة بيانات أولية'}
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="overflow-hidden">
                <div className="relative">
                  <EnhancedImage
                    src={product.images[0] || '/images/placeholder-product.jpg'}
                    alt={product.name}
                    width={400}
                    height={200}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 right-2 flex gap-2">
                    <Badge variant={product.is_published ? 'default' : 'secondary'}>
                      {product.is_published ? 'منشور' : 'مخفي'}
                    </Badge>
                    <Badge variant={product.is_available ? 'default' : 'destructive'}>
                      {product.is_available ? 'متاح' : 'غير متاح'}
                    </Badge>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="arabic-text line-clamp-1">{product.name}</CardTitle>
                  <CardDescription className="arabic-text line-clamp-2">
                    {product.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-blue-600">
                        {product.price} Dhs
                      </span>
                      {product.rental_price && (
                        <span className="text-sm text-gray-600">
                          إيجار: {product.rental_price} Dhs
                        </span>
                      )}
                    </div>
                    
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>المخزون: {product.stock_quantity}</span>
                      <span>التقييم: {product.rating}/5 ({product.reviews_count})</span>
                    </div>

                    <div className="flex gap-2 pt-3">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => openEditForm(product)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        تعديل
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => togglePublished(product.id, product.is_published)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        {product.is_published ? 'إخفاء' : 'نشر'}
                      </Button>
                      <Button 
                        size="sm" 
                        variant="destructive"
                        onClick={() => deleteProduct(product.id, product.name)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* نموذج إضافة/تعديل المنتج */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <SimpleProductForm
                product={editingProduct}
                onSave={handleFormSave}
                onCancel={handleFormCancel}
                isLoading={formLoading}
              />
            </div>
          </div>
        )}
      </PageLayout>
    </div>
  )
}
