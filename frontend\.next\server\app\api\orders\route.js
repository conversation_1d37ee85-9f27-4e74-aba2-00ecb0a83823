(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},87661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>l});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),d=r(38561);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status"),s=t.get("customer"),n=t.get("school"),a=t.get("date_from"),o=t.get("date_to"),u=t.get("limit"),l=t.get("offset"),p=t.get("sort_by")||"created_at",c=t.get("sort_order")||"desc",m=d.C.getOrders();r&&"all"!==r&&(m=m.filter(e=>e.status===r)),s&&(m=m.filter(e=>e.customer_name.toLowerCase().includes(s.toLowerCase())||e.customer_email.toLowerCase().includes(s.toLowerCase())||e.order_number.toLowerCase().includes(s.toLowerCase()))),n&&"all"!==n&&(m=m.filter(e=>e.school_id===n)),a&&(m=m.filter(e=>e.created_at>=a)),o&&(m=m.filter(e=>e.created_at<=o)),m.sort((e,t)=>{let r=e[p],s=t[p];return(("created_at"===p||"updated_at"===p)&&(r=new Date(r).getTime(),s=new Date(s).getTime()),"desc"===c)?s>r?1:-1:r>s?1:-1});let g=m.length;if(u&&l){let e=parseInt(u),t=parseInt(l);m=m.slice(t,t+e)}let x=d.C.getOrders(),f={total:x.length,pending:x.filter(e=>"pending"===e.status).length,confirmed:x.filter(e=>"confirmed"===e.status).length,in_production:x.filter(e=>"in_production"===e.status).length,shipped:x.filter(e=>"shipped"===e.status).length,delivered:x.filter(e=>"delivered"===e.status).length,cancelled:x.filter(e=>"cancelled"===e.status).length,total_revenue:x.filter(e=>"paid"===e.payment_status).reduce((e,t)=>e+t.total,0),pending_payments:x.filter(e=>"pending"===e.payment_status).reduce((e,t)=>e+t.total,0)};return i.NextResponse.json({orders:m,total:g,stats:f})}catch(e){return i.NextResponse.json({error:"خطأ في جلب الطلبات"},{status:500})}}async function l(e){try{let{customer_id:t,customer_name:r,customer_email:s,customer_phone:n,items:a,shipping_address:o,payment_method:u,notes:l,school_id:p,school_name:c}=await e.json();if(!r||!s||!a||0===a.length||!o)return i.NextResponse.json({error:"البيانات المطلوبة مفقودة"},{status:400});let m=a.reduce((e,t)=>e+t.unit_price*t.quantity,0),g=.05*m,x=m>500?0:25,f=d.C.getOrders(),b={id:d.C.generateId(),order_number:d.C.generateOrderNumber(),customer_id:t||d.C.generateId(),customer_name:r,customer_email:s,customer_phone:n,status:"pending",items:a.map(e=>({...e,id:d.C.generateId(),order_id:d.C.generateId()})),subtotal:m,tax:g,shipping_cost:x,total:m+g+x,payment_status:"pending",payment_method:u,shipping_address:o,notes:l,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),school_id:p,school_name:c};return f.push(b),d.C.saveOrders(f),i.NextResponse.json({message:"تم إنشاء الطلب بنجاح",order:b},{status:201})}catch(e){return i.NextResponse.json({error:"خطأ في إنشاء الطلب"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:m,serverHooks:g}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:m})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(87661));module.exports=s})();