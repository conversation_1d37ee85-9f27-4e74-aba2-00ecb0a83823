(()=>{var e={};e.id=761,e.ids=[761],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},41380:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>b,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var r={};t.r(r),t.d(r,{DELETE:()=>c,GET:()=>u,PUT:()=>p});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),d=t(38561);async function u(e,{params:s}){try{let e=d.C.getSchools().find(e=>e.id===s.id);if(!e)return a.NextResponse.json({error:"المدرسة غير موجودة"},{status:404});return a.NextResponse.json({school:e})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:s}){try{let{name:t,name_en:r,name_fr:o,address:n,city:i,phone:u,email:p,website:c,logo_url:x,graduation_date:l,student_count:m,is_active:b,settings:f}=await e.json(),v=d.C.getSchools(),g=v.findIndex(e=>e.id===s.id);if(-1===g)return a.NextResponse.json({error:"المدرسة غير موجودة"},{status:404});if(!t)return a.NextResponse.json({error:"اسم المدرسة مطلوب"},{status:400});if(void 0!==m&&(isNaN(m)||m<0))return a.NextResponse.json({error:"عدد الطلاب يجب أن يكون رقماً صحيحاً موجباً"},{status:400});if(p&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(p))return a.NextResponse.json({error:"البريد الإلكتروني غير صحيح"},{status:400});if(p&&v.find(e=>e.email===p&&e.id!==s.id))return a.NextResponse.json({error:"البريد الإلكتروني موجود بالفعل"},{status:400});let h={...v[g],name:t,name_en:r||void 0,name_fr:o||void 0,address:n||void 0,city:i||void 0,phone:u||void 0,email:p||void 0,website:c||void 0,logo_url:x||void 0,graduation_date:l||void 0,student_count:m??v[g].student_count,is_active:b??v[g].is_active,settings:f||v[g].settings,updated_at:new Date().toISOString()};return v[g]=h,d.C.saveSchools(v),a.NextResponse.json({message:"تم تحديث المدرسة بنجاح",school:h})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function c(e,{params:s}){try{let e=d.C.getSchools(),t=e.findIndex(e=>e.id===s.id);if(-1===t)return a.NextResponse.json({error:"المدرسة غير موجودة"},{status:404});let r=e.splice(t,1)[0];return d.C.saveSchools(e),a.NextResponse.json({message:"تم حذف المدرسة بنجاح",school:r})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/schools/[id]/route",pathname:"/api/schools/[id]",filename:"route",bundlePath:"app/api/schools/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\schools\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:b}=x;function f(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580],()=>t(41380));module.exports=r})();