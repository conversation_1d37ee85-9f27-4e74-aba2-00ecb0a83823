
'use client'

import { useState, useEffect } from 'react'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { PageLayout } from '@/components/layouts/PageLayout'
import { UserRole } from '@/types/auth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import Link from 'next/link'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertCircle, Plus, Brain } from 'lucide-react'

  Brain,
  Plus,
  ArrowLeft,
  Home,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Wifi,
  Loader2,
  Zap
} from 'lucide-react'

export default function AIModelsPage() {
  const [addedProviders, setAddedProviders] = useState<any[]>([])
  const [addModelOpen, setAddModelOpen] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState('')
  const [selectedModels, setSelectedModels] = useState<string[]>([])
  const [baseUrl, setBaseUrl] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [description, setDescription] = useState('')
  const [editingProvider, setEditingProvider] = useState<unknown>(null)
  const [testingProviders, setTestingProviders] = useState<{[key: string]: boolean}>({})
  const [connectionStatus, setConnectionStatus] = useState<{[key: string]: {
    status: 'success' | 'error' | null,
    lastTested: string | null,
    message?: string
  }}>({})
  const [loading, setLoading] = useState(true)

  // دوال حفظ واسترجاع حالة الاتصال
  const saveConnectionStatus = (status: typeof connectionStatus) => {
    try {
      localStorage.setItem('ai_providers_connection_status', JSON.stringify(status))
    } catch (error) {
      console.error('Error saving connection status:', error)
    }
  }

  const loadConnectionStatus = () => {
    try {
      const saved = localStorage.getItem('ai_providers_connection_status')
      if (saved) {
        const parsed = JSON.parse(saved)
        setConnectionStatus(parsed)
        return parsed
      }
    } catch (error) {
      console.error('Error loading connection status:', error)
    }
    return {}
  }

  // دالة للتحقق من انتهاء صلاحية حالة الاتصال (24 ساعة)
  const isConnectionStatusExpired = (lastTested: string | null) => {
    if (!lastTested) return true
    const now = new Date().getTime()
    const tested = new Date(lastTested).getTime()
    const hoursDiff = (now - tested) / (1000 * 60 * 60)
    return hoursDiff > 24 // انتهاء الصلاحية بعد 24 ساعة
  }

  // بيانات المزودين والنماذج الفرعية - موسعة حسب المتطلبات
  const providers: {[key: string]: {
    name: string
    baseUrl: string
    models: string[]
  }} = {
    'OpenAI': {
      name: 'OpenAI',
      baseUrl: 'https://api.openai.com/v1',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o', 'o1-preview', 'o1-mini', 'dall-e-3', 'dall-e-2', 'whisper-1', 'tts-1', 'text-embedding-ada-002', 'text-embedding-3-small']
    },
    'Anthropic': {
      name: 'Anthropic',
      baseUrl: 'https://api.anthropic.com',
      models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-3-5-sonnet-20241022', 'claude-2.1', 'claude-2.0', 'claude-instant-1.2']
    },
    'Google AI': {
      name: 'Google AI',
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      models: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro', 'gemini-pro-vision', 'text-bison-001']
    },
    'Microsoft Azure OpenAI': {
      name: 'Microsoft Azure OpenAI',
      baseUrl: 'https://your-resource.openai.azure.com',
      models: ['gpt-4', 'gpt-35-turbo', 'dall-e-3', 'text-embedding-ada-002']
    },
    'Grok (xAI)': {
      name: 'Grok (xAI)',
      baseUrl: 'https://api.x.ai/v1',
      models: ['grok-beta', 'grok-vision-beta']
    },
    'DeepSeek': {
      name: 'DeepSeek',
      baseUrl: 'https://api.deepseek.com',
      models: ['deepseek-chat', 'deepseek-coder', 'deepseek-math', 'deepseek-reasoner']
    },
    'Meta AI': {
      name: 'Meta AI',
      baseUrl: 'https://api.llama-api.com',
      models: ['llama-2-70b-chat', 'llama-2-13b-chat', 'llama-2-7b-chat', 'code-llama-34b', 'code-llama-13b', 'code-llama-7b', 'llama-3-8b', 'llama-3-70b']
    },
    'Mistral AI': {
      name: 'Mistral AI',
      baseUrl: 'https://api.mistral.ai/v1',
      models: ['mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest', 'mistral-tiny', 'mixtral-8x7b-instruct', 'mixtral-8x22b-instruct']
    },
    'OpenRouter': {
      name: 'OpenRouter',
      baseUrl: 'https://openrouter.ai/api/v1',
      models: ['openai/gpt-4-turbo', 'anthropic/claude-3-opus', 'google/gemini-pro', 'meta-llama/llama-2-70b-chat', 'mistralai/mixtral-8x7b-instruct', 'cohere/command-r-plus']
    },
    'Cohere': {
      name: 'Cohere',
      baseUrl: 'https://api.cohere.ai/v1',
      models: ['command-r-plus', 'command-r', 'command', 'command-nightly', 'command-light', 'command-light-nightly', 'embed-english-v3.0']
    },
    'Hugging Face': {
      name: 'Hugging Face',
      baseUrl: 'https://api-inference.huggingface.co',
      models: ['microsoft/DialoGPT-large', 'facebook/blenderbot-400M-distill', 'microsoft/DialoGPT-medium', 'facebook/blenderbot-1B-distill', 'EleutherAI/gpt-j-6B']
    },
    'Together AI': {
      name: 'Together AI',
      baseUrl: 'https://api.together.xyz/v1',
      models: ['meta-llama/Llama-2-70b-chat-hf', 'mistralai/Mixtral-8x7B-Instruct-v0.1', 'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO', 'teknium/OpenHermes-2.5-Mistral-7B']
    }
  }

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    const loadData = async () => {
      try {
        // تحميل المزودين المحفوظين
        const savedProviders = localStorage.getItem('ai_providers')
        if (savedProviders) {
          setAddedProviders(JSON.parse(savedProviders))
        }

        // تحميل حالة الاتصال
        loadConnectionStatus()
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // حفظ المزودين في localStorage
  const saveProviders = (providers: unknown[]) => {
    try {
      localStorage.setItem('ai_providers', JSON.stringify(providers))
    } catch (error) {
      console.error('Error saving providers:', error)
    }
  }

  // دالة تغيير المزود
  const handleProviderChange = (provider: string) => {
    setSelectedProvider(provider)
    setSelectedModels([])
    if (providers[provider]) {
      setBaseUrl(providers[provider].baseUrl)
    }
  }

  // دالة تبديل تحديد النماذج
  const handleModelToggle = (model: string) => {
    setSelectedModels(prev =>
      prev.includes(model)
        ? prev.filter(m => m !== model)
        : [...prev, model]
    )
  }

  // دالة حفظ المزود
  const handleSaveProvider = () => {
    if (!selectedProvider || !apiKey || selectedModels.length === 0) {
      toast.error('يرجى ملء جميع البيانات المطلوبة وتحديد نموذج واحد على الأقل')
      return
    }

    const newProvider = {
      id: editingProvider?.id || Date.now().toString(),
      provider: selectedProvider,
      providerName: providers[selectedProvider].name,
      baseUrl: baseUrl,
      apiKey: apiKey,
      models: selectedModels,
      description: description,
      status: 'active',
      createdAt: editingProvider?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    if (editingProvider) {
      // تحديث مزود موجود
      const updatedProviders = addedProviders.map(p =>
        p.id === editingProvider.id ? newProvider : p
      )
      setAddedProviders(updatedProviders)
      saveProviders(updatedProviders)
      toast.success('تم تحديث المزود بنجاح')
    } else {
      // إضافة مزود جديد
      const updatedProviders = [...addedProviders, newProvider]
      setAddedProviders(updatedProviders)
      saveProviders(updatedProviders)
      toast.success('تم إضافة المزود بنجاح')
    }

    // إعادة تعيين النموذج
    setAddModelOpen(false)
    setSelectedProvider('')
    setSelectedModels([])
    setBaseUrl('')
    setApiKey('')
    setDescription('')
    setEditingProvider(null)
  }

  // دالة اختبار الاتصال
  const handleTestConnection = async (provider: unknown) => {
    const providerId = provider.id

    // بدء الاختبار
    setTestingProviders(prev => ({ ...prev, [providerId]: true }))
    setConnectionStatus(prev => ({ ...prev, [providerId]: { status: null, lastTested: null } }))

    toast.loading(`جاري اختبار الاتصال مع ${provider.providerName}...`)

    try {
      // محاكاة اختبار الاتصال مع احتمالات نجاح مختلفة لكل مزود
      const providerTests: {[key: string]: () => boolean} = {
        'OpenAI': () => Math.random() > 0.05,        // 95% نجاح
        'Anthropic': () => Math.random() > 0.08,     // 92% نجاح
        'Google AI': () => Math.random() > 0.1,      // 90% نجاح
        'Microsoft Azure OpenAI': () => Math.random() > 0.12, // 88% نجاح
        'Grok (xAI)': () => Math.random() > 0.15,    // 85% نجاح
        'DeepSeek': () => Math.random() > 0.15,      // 85% نجاح
        'Meta AI': () => Math.random() > 0.2,        // 80% نجاح
        'Mistral AI': () => Math.random() > 0.18,    // 82% نجاح
        'OpenRouter': () => Math.random() > 0.1,     // 90% نجاح
        'Cohere': () => Math.random() > 0.12,        // 88% نجاح
        'Hugging Face': () => Math.random() > 0.25,  // 75% نجاح
        'Together AI': () => Math.random() > 0.2     // 80% نجاح
      }

      // انتظار لمحاكاة الاختبار
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))

      const testFunction = providerTests[provider.providerName] || (() => Math.random() > 0.3)
      const isSuccess = testFunction()

      const now = new Date().toISOString()
      const newStatus = {
        status: isSuccess ? 'success' as const : 'error' as const,
        lastTested: now,
        message: isSuccess
          ? `تم الاتصال بنجاح مع ${provider.providerName}`
          : `فشل الاتصال مع ${provider.providerName} - تحقق من مفتاح API`
      }

      setConnectionStatus(prev => {
        const updated = { ...prev, [providerId]: newStatus }
        saveConnectionStatus(updated)
        return updated
      })

      toast.dismiss()
      if (isSuccess) {
        toast.success(`✅ ${newStatus.message}`)
      } else {
        toast.error(`❌ ${newStatus.message}`)
      }

    } catch (error) {
      const now = new Date().toISOString()
      const errorStatus = {
        status: 'error' as const,
        lastTested: now,
        message: `خطأ في اختبار الاتصال مع ${provider.providerName}`
      }

      setConnectionStatus(prev => {
        const updated = { ...prev, [providerId]: errorStatus }
        saveConnectionStatus(updated)
        return updated
      })

      toast.dismiss()
      toast.error(`❌ ${errorStatus.message}`)
    } finally {
      setTestingProviders(prev => ({ ...prev, [providerId]: false }))
    }
  }

  // دالة تعديل المزود
  const handleEditProvider = (provider: unknown) => {
    setEditingProvider(provider)
    setSelectedProvider(provider.provider)
    setSelectedModels(provider.models)
    setBaseUrl(provider.baseUrl)
    setApiKey(provider.apiKey)
    setDescription(provider.description || '')
    setAddModelOpen(true)
  }

  // دالة حذف المزود
  const handleDeleteProvider = (providerId: string) => {
    const updatedProviders = addedProviders.filter(p => p.id !== providerId)
    setAddedProviders(updatedProviders)
    saveProviders(updatedProviders)

    // حذف حالة الاتصال أيضاً
    setConnectionStatus(prev => {
      const updated = { ...prev }
      delete updated[providerId]
      saveConnectionStatus(updated)
      return updated
    })

    toast.success('تم حذف المزود بنجاح')
  }

  // دالة تفعيل/إيقاف المزود
  const handleToggleProvider = (providerId: string) => {
    const updatedProviders = addedProviders.map(p =>
      p.id === providerId
        ? { ...p, status: p.status === 'active' ? 'inactive' : 'active' }
        : p
    )
    setAddedProviders(updatedProviders)
    saveProviders(updatedProviders)

    const provider = updatedProviders.find(p => p.id === providerId)
    toast.success(`تم ${provider?.status === 'active' ? 'تفعيل' : 'إيقاف'} المزود`)
  }

  // دالة اختبار جميع المزودين
  const handleTestAllProviders = async () => {
    if (addedProviders.length === 0) {
      toast.error('لا توجد مزودين لاختبارهم')
      return
    }

    toast.info('جاري اختبار جميع المزودين...')

    for (const provider of addedProviders) {
      await handleTestConnection(provider)
      // انتظار ثانية بين كل اختبار
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    toast.success('تم الانتهاء من اختبار جميع المزودين')
  }

  // حساب الإحصائيات
  const stats = {
    totalProviders: addedProviders.length,
    activeProviders: addedProviders.filter(p => p.status === 'active').length,
    totalModels: addedProviders.reduce((sum, p) => sum + p.models.length, 0),
    successfulConnections: Object.values(connectionStatus).filter(s => s.status === 'success').length
  }

  if (loading) {
    return (
      <ProtectedRoute requiredRole={UserRole.ADMIN}>
        <PageLayout
          title="إدارة مقدمي خدمات الذكاء الاصطناعي"
          description="إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم"
        >
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        </PageLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <PageLayout
        title="إدارة مقدمي خدمات الذكاء الاصطناعي"
        description="إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم"
      >
        {/* شريط التنقل العلوي */}
        <div className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard/admin"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="arabic-text">العودة للوحة التحكم</span>
            </Link>
            <div className="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              <Home className="h-4 w-4" />
              <span className="arabic-text">الصفحة الرئيسية</span>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-gray-900 dark:text-white arabic-text">نماذج الذكاء الاصطناعي</span>
          </div>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium arabic-text">إجمالي المزودين</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProviders}</div>
              <p className="text-xs text-muted-foreground arabic-text">
                مزود مضاف
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium arabic-text">المزودين النشطين</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.activeProviders}</div>
              <p className="text-xs text-muted-foreground arabic-text">
                من أصل {stats.totalProviders}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium arabic-text">إجمالي النماذج</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalModels}</div>
              <p className="text-xs text-muted-foreground arabic-text">
                نموذج متاح
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium arabic-text">الاتصالات الناجحة</CardTitle>
              <Wifi className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.successfulConnections}</div>
              <p className="text-xs text-muted-foreground arabic-text">
                اتصال ناجح
              </p>
            </CardContent>
          </Card>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <Button
            onClick={() => setAddModelOpen(true)}
            className="flex items-center gap-2 arabic-text"
          >
            <Plus className="h-4 w-4" />
            إضافة مزود جديد
          </Button>

          {addedProviders.length > 0 && (
            <Button
              variant="outline"
              onClick={handleTestAllProviders}
              className="flex items-center gap-2 arabic-text"
            >
              <Wifi className="h-4 w-4" />
              اختبار جميع المزودين
            </Button>
          )}
        </div>

        {/* قائمة المزودين المضافين */}
        {addedProviders.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="arabic-text">المزودين المضافين ({addedProviders.length})</CardTitle>
              <CardDescription className="arabic-text">
                إدارة وتكوين مزودي خدمات الذكاء الاصطناعي
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {addedProviders.map((provider) => {
                  const status = connectionStatus[provider.id]
                  const isExpired = status?.lastTested ? isConnectionStatusExpired(status.lastTested) : true

                  return (
                    <div key={provider.id} className="border rounded-lg p-4 space-y-3">
                      {/* معلومات المزود */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Brain className="h-5 w-5 text-blue-600" />
                          <div>
                            <h3 className="font-semibold arabic-text">{provider.providerName}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {provider.models.length} نموذج • {provider.baseUrl}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {/* مؤشر الحالة */}
                          <Badge
                            variant={provider.status === 'active' ? 'default' : 'secondary'}
                            className="arabic-text"
                          >
                            {provider.status === 'active' ? 'نشط' : 'غير نشط'}
                          </Badge>

                          {/* حالة الاتصال */}
                          {status && !isExpired && (
                            <Badge
                              variant={status.status === 'success' ? 'default' : 'destructive'}
                              className="arabic-text"
                            >
                              {status.status === 'success' ? (
                                <><CheckCircle className="h-3 w-3 mr-1" /> متصل</>
                              ) : (
                                <><XCircle className="h-3 w-3 mr-1" /> غير متصل</>
                              )}
                            </Badge>
                          )}

                          {(!status || isExpired) && (
                            <Badge variant="outline" className="arabic-text">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              لم يتم الاختبار
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* النماذج المحددة */}
                      <div>
                        <p className="text-sm font-medium mb-2 arabic-text">النماذج المحددة:</p>
                        <div className="flex flex-wrap gap-1">
                          {provider.models.map((model: string) => (
                            <Badge key={model} variant="outline" className="text-xs">
                              {model}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* الوصف */}
                      {provider.description && (
                        <div>
                          <p className="text-sm font-medium mb-1 arabic-text">الوصف:</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{provider.description}</p>
                        </div>
                      )}

                      {/* معلومات إضافية */}
                      <div className="text-xs text-gray-500 space-y-1">
                        <p>تاريخ الإضافة: {new Date(provider.createdAt).toLocaleDateString('ar-SA')}</p>
                        <p>آخر تحديث: {new Date(provider.updatedAt).toLocaleDateString('ar-SA')}</p>
                        {status?.lastTested && (
                          <p>آخر اختبار: {new Date(status.lastTested).toLocaleString('ar-SA')}</p>
                        )}
                      </div>

                      {/* أزرار الإجراءات */}
                      <div className="flex flex-wrap gap-2 pt-2 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestConnection(provider)}
                          disabled={testingProviders[provider.id]}
                          className="arabic-text"
                        >
                          {testingProviders[provider.id] ? (
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          ) : (
                            <Wifi className="h-4 w-4 mr-1" />
                          )}
                          {testingProviders[provider.id] ? 'جاري الاختبار...' : 'اختبار الاتصال'}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditProvider(provider)}
                          className="arabic-text"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          تعديل
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleProvider(provider.id)}
                          className="arabic-text"
                        >
                          {provider.status === 'active' ? 'إيقاف' : 'تفعيل'}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteProvider(provider.id)}
                          className="text-red-600 hover:text-red-700 arabic-text"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          حذف
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* رسالة عدم وجود مزودين */}
        {addedProviders.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2 arabic-text">لا توجد مزودين مضافين</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4 arabic-text">
                ابدأ بإضافة مزود خدمة ذكاء اصطناعي لإدارة النماذج
              </p>
              <Button onClick={() => setAddModelOpen(true)} className="arabic-text">
                <Plus className="h-4 w-4 mr-2" />
                إضافة مزود جديد
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Dialog إضافة/تعديل المزود */}
        <Dialog open={addModelOpen} onOpenChange={setAddModelOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="arabic-text">
                {editingProvider ? 'تعديل مزود الخدمة' : 'إضافة مزود خدمة جديد'}
              </DialogTitle>
              <DialogDescription className="arabic-text">
                {editingProvider
                  ? 'قم بتحديث إعدادات مزود الخدمة والنماذج المحددة'
                  : 'اختر مزود خدمة وحدد النماذج المطلوبة للعمل بها'
                }
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* اختيار المزود */}
              <div className="space-y-2">
                <Label htmlFor="provider" className="arabic-text">مزود الخدمة</Label>
                <Select value={selectedProvider} onValueChange={handleProviderChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر مزود الخدمة" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(providers).map(([key, provider]) => (
                      <SelectItem key={key} value={key}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Base URL */}
              <div className="space-y-2">
                <Label htmlFor="baseUrl" className="arabic-text">رابط API الأساسي</Label>
                <Input
                  id="baseUrl"
                  value={baseUrl}
                  onChange={(e) => setBaseUrl(e.target.value)}
                  placeholder="https://api.example.com/v1"
                  dir="ltr"
                />
              </div>

              {/* مفتاح API */}
              <div className="space-y-2">
                <Label htmlFor="apiKey" className="arabic-text">مفتاح API</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="أدخل مفتاح API"
                  dir="ltr"
                />
              </div>

              {/* النماذج المتاحة */}
              {selectedProvider && providers[selectedProvider] && (
                <div className="space-y-2">
                  <Label className="arabic-text">النماذج المتاحة</Label>
                  <div className="border rounded-lg p-4 max-h-48 overflow-y-auto">
                    <div className="space-y-2">
                      {providers[selectedProvider].models.map((model) => (
                        <div key={model} className="flex items-center space-x-2 space-x-reverse">
                          <Checkbox
                            id={model}
                            checked={selectedModels.includes(model)}
                            onCheckedChange={() => handleModelToggle(model)}
                          />
                          <Label htmlFor={model} className="text-sm cursor-pointer">
                            {model}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 arabic-text">
                    تم تحديد {selectedModels.length} من أصل {providers[selectedProvider].models.length} نموذج
                  </p>
                </div>
              )}

              {/* الوصف */}
              <div className="space-y-2">
                <Label htmlFor="description" className="arabic-text">الوصف (اختياري)</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="أضف وصفاً لهذا المزود..."
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setAddModelOpen(false)
                  setEditingProvider(null)
                  setSelectedProvider('')
                  setSelectedModels([])
                  setBaseUrl('')
                  setApiKey('')
                  setDescription('')
                }}
                className="arabic-text"
              >
                إلغاء
              </Button>
              <Button
                onClick={handleSaveProvider}
                disabled={!selectedProvider || !apiKey || selectedModels.length === 0}
                className="arabic-text"
              >
                {editingProvider ? 'تحديث المزود' : 'إضافة المزود'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </PageLayout>
    </ProtectedRoute>
  )
}
