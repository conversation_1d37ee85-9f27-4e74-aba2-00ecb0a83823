import {
  X
} from 'lucide-react'

/**
 * ✅ إصلاح حرج: CSRF Protection
 * 
 * يوفر حماية من هجمات Cross-Site Request Forgery
 * ويضمن أن جميع الطلبات تأتي من مصادر موثوقة
 * 
 * @security CRITICAL - يجب استخدام هذا لجميع APIs الحساسة
 */

import { NextRequest, NextResponse } from 'next/server'
import { generateSecureToken } from './encryption'

// مدة صلاحية CSRF token (30 دقيقة)
const CSRF_TOKEN_EXPIRY = 30 * 60 * 1000

interface CSRFTokenData {
  token: string
  timestamp: number
  origin: string
}

/**
 * إنشاء CSRF token آمن
 */
export function generateCSRFToken(origin?: string): string {
  const tokenData: CSRFTokenData = {
    token: generateSecureToken(32),
    timestamp: Date.now(),
    origin: origin || 'unknown'
  }

  // تشفير البيانات وإرجاع token
  return btoa(JSON.stringify(tokenData))
}

/**
 * التحقق من صحة CSRF token
 */
export function validateCSRFToken(token: string, expectedOrigin?: string): boolean {
  try {
    const tokenData: CSRFTokenData = JSON.parse(atob(token))
    
    // التحقق من انتهاء الصلاحية
    const now = Date.now()
    if (now - tokenData.timestamp > CSRF_TOKEN_EXPIRY) {
      console.warn('🚨 CSRF token expired')
      return false
    }

    // التحقق من المصدر إذا تم تحديده
    if (expectedOrigin && tokenData.origin !== expectedOrigin) {
      console.warn('🚨 CSRF token origin mismatch')
      return false
    }

    // التحقق من وجود token صحيح
    if (!tokenData.token || tokenData.token.length < 32) {
      console.warn('🚨 Invalid CSRF token format')
      return false
    }

    return true
  } catch (error) {
    console.error('❌ CSRF token validation failed:', error)
    return false
  }
}

/**
 * استخراج CSRF token من الطلب
 */
export function extractCSRFToken(request: NextRequest): string | null {
  // البحث في headers أولاً
  const headerToken = request.headers.get('x-csrf-token')
  if (headerToken) {
    return headerToken
  }

  // البحث في cookies
  const cookieToken = request.cookies.get('csrf-token')?.value
  if (cookieToken) {
    return cookieToken
  }

  // البحث في body للطلبات POST
  if (request.method === 'POST') {
    try {
      const formData = request.formData()
      // Note: هذا مثال، في الواقع نحتاج لقراءة body بطريقة مختلفة
      // const bodyToken = formData.get('csrf_token')
      // return bodyToken as string
    } catch (error) {
      // تجاهل الأخطاء في قراءة body
    }
  }

  return null
}

/**
 * Middleware للحماية من CSRF
 */
export function csrfMiddleware(handler: Function) {
  return async (request: NextRequest) => {
    // تخطي GET requests (آمنة بطبيعتها)
    if (request.method === 'GET' || request.method === 'HEAD' || request.method === 'OPTIONS') {
      return handler(request)
    }

    // استخراج والتحقق من CSRF token
    const token = extractCSRFToken(request)
    const origin = request.headers.get('origin') || request.headers.get('referer')

    if (!token || !validateCSRFToken(token, origin || undefined)) {
      console.warn('🚨 CSRF protection triggered:', {
        method: request.method,
        url: request.url,
        origin,
        hasToken: !!token,
        userAgent: request.headers.get('user-agent')
      })

      return NextResponse.json(
        { 
          error: 'Invalid or missing CSRF token',
          code: 'CSRF_TOKEN_INVALID'
        }, 
        { status: 403 }
      )
    }

    return handler(request)
  }
}

/**
 * إضافة CSRF token للاستجابة
 */
export function addCSRFTokenToResponse(response: NextResponse, origin?: string): NextResponse {
  const token = generateCSRFToken(origin)
  
  // إضافة token في header
  response.headers.set('X-CSRF-Token', token)
  
  // إضافة token في cookie (HttpOnly للأمان)
  response.cookies.set('csrf-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_EXPIRY / 1000, // بالثواني
    path: '/'
  })

  return response
}

// إضافة imports مطلوبة
import { useState, useEffect, useCallback } from 'react'

/**
 * Hook للحصول على CSRF token في المكونات
 */
export function useCSRFToken(): {
  token: string | null
  refreshToken: () => Promise<string | null>
} {
  const [token, setToken] = useState<string | null>(null)

  const refreshToken = useCallback(async (): Promise<string | null> => {
    try {
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        const newToken = response.headers.get('X-CSRF-Token')
        setToken(newToken)
        return newToken
      }
    } catch (error) {
      console.error('❌ Failed to refresh CSRF token:', error)
    }
    return null
  }, [])

  useEffect(() => {
    refreshToken()
  }, [refreshToken])

  return { token, refreshToken }
}

/**
 * دالة مساعدة لإضافة CSRF token للطلبات
 */
export async function fetchWithCSRF(url: string, options: RequestInit = {}): Promise<Response> {
  // الحصول على CSRF token من cookie
  const csrfToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrf-token='))
    ?.split('=')[1]

  if (!csrfToken && options.method && options.method !== 'GET') {
    console.warn('⚠️ No CSRF token found for non-GET request')
  }

  // إضافة CSRF token للطلب
  const headers = new Headers(options.headers)
  if (csrfToken) {
    headers.set('X-CSRF-Token', csrfToken)
  }

  return fetch(url, {
    ...options,
    headers,
    credentials: 'include' // تضمين cookies
  })
}

/**
 * مكون لحقن CSRF token في النماذج
 */
export function CSRFTokenInput({ token }: { token: string | null }) {
  if (!token) {
    return null
  }

  return React.createElement('input', {
    type: 'hidden',
    name: 'csrf_token',
    value: token,
    'aria-hidden': 'true'
  })
}

// إضافة imports مطلوبة
import { useState, useEffect, useCallback } from 'react'

/**
 * ✅ إعداد CSRF protection للتطبيق
 */
export const csrfConfig = {
  // المسارات التي تحتاج حماية CSRF
  protectedPaths: [
    '/api/auth/',
    '/api/users/',
    '/api/orders/',
    '/api/products/',
    '/api/admin/',
    '/api/payment/'
  ],

  // المسارات المستثناة من CSRF protection
  excludedPaths: [
    '/api/health',
    '/api/csrf-token',
    '/api/public/'
  ],

  // إعدادات إضافية
  tokenExpiry: CSRF_TOKEN_EXPIRY,
  cookieName: 'csrf-token',
  headerName: 'X-CSRF-Token'
}

/**
 * التحقق من ضرورة CSRF protection للمسار
 */
export function requiresCSRFProtection(pathname: string): boolean {
  // تحقق من المسارات المستثناة
  if (csrfConfig.excludedPaths.some(path => pathname.startsWith(path))) {
    return false
  }

  // تحقق من المسارات المحمية
  return csrfConfig.protectedPaths.some(path => pathname.startsWith(path))
}
