{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["\nexport type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAGO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: unknown = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,qHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,8CAA8C;YAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,eAAe;gBAAC;gBAAM;gBAAM;aAAK,CAAC,QAAQ,CAAC,cAAc;gBAC3D,UAAU;YACZ;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAiB,YAAY,CAAC,OAAO;QAEzC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/NavigationSkeleton.tsx"], "sourcesContent": ["\n'use client'\n\nimport React from 'react'\n\ninterface NavigationSkeletonProps {\n  isMobile?: boolean\n}\n\nexport function NavigationSkeleton({ isMobile = false }: NavigationSkeletonProps) {\n  const skeletonItems = Array.from({ length: 5 }, (_, i) => i)\n\n  // قيم ثابتة للعروض لتجنب مشكلة hydration\n  const mobileWidths = ['75%', '85%', '65%', '80%', '70%']\n  const desktopWidths = ['60px', '75px', '55px', '70px', '65px']\n\n  if (isMobile) {\n    return (\n      <div className=\"flex flex-col gap-1 mb-6\">\n        {skeletonItems.map((index) => (\n          <div\n            key={index}\n            className=\"flex items-center gap-3 px-4 py-3 mx-2 rounded-xl animate-pulse\"\n            style={{\n              animationDelay: `${index * 100}ms`\n            }}\n          >\n            {/* Icon skeleton */}\n            <div className=\"w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer\" />\n\n            {/* Text skeleton */}\n            <div className=\"flex-1\">\n              <div\n                className=\"h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer\"\n                style={{ width: mobileWidths[index] }}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <nav className=\"hidden lg:flex items-center gap-1\">\n      {skeletonItems.map((index) => (\n        <div\n          key={index}\n          className=\"flex items-center gap-2 px-4 py-2.5 rounded-xl animate-pulse\"\n          style={{\n            animationDelay: `${index * 100}ms`\n          }}\n        >\n          {/* Icon skeleton */}\n          <div className=\"w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer\" />\n\n          {/* Text skeleton */}\n          <div\n            className=\"h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer\"\n            style={{ width: desktopWidths[index] }}\n          />\n        </div>\n      ))}\n    </nav>\n  )\n}\n\n// Skeleton للعناصر الجانبية (Cart, Wishlist, etc.)\nexport function SideElementsSkeleton() {\n  return (\n    <div className=\"flex items-center gap-3\">\n      {/* Search skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" />\n      \n      {/* Cart skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '100ms' }} />\n      \n      {/* Wishlist skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '200ms' }} />\n      \n      {/* Theme toggle skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '300ms' }} />\n      \n      {/* Language skeleton */}\n      <div className=\"w-16 h-8 bg-gray-300 dark:bg-gray-600 rounded shimmer animate-pulse\" style={{ animationDelay: '400ms' }} />\n      \n      {/* Profile skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '500ms' }} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;AAQO,SAAS,mBAAmB,EAAE,WAAW,KAAK,EAA2B;IAC9E,MAAM,gBAAgB,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM;IAE1D,yCAAyC;IACzC,MAAM,eAAe;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACxD,MAAM,gBAAgB;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAE9D,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;oBAEC,WAAU;oBACV,OAAO;wBACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;oBACpC;;sCAGA,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,YAAY,CAAC,MAAM;gCAAC;;;;;;;;;;;;mBAbnC;;;;;;;;;;IAoBf;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gBACpC;;kCAGA,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,aAAa,CAAC,MAAM;wBAAC;;;;;;;eAZlC;;;;;;;;;;AAkBf;KAxDgB;AA2DT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAG1H,6LAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAG1H,6LAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAG1H,6LAAC;gBAAI,WAAU;gBAAsE,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAGtH,6LAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;;;;;;;AAGhI;MAtBgB", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\n\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { useTheme } from \"next-themes\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Sun, Moon } from \"lucide-react\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AAMA;AAAA;;;AAXA;;;;;AAaO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;;;;;kCAE1C,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;;;;;kCAE1C,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;;;;;;;;;;;;;;;;;AAKlD;GAtBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\n\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  Globe,\n  ChevronDown\n} from 'lucide-react'\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguage = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguage ()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <div className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenu className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenu >\n        <DropdownMenu />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <div className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAKA;AACA;AAEA;;;AAPA;;;;AAaO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,qBAAqB;QACzB,OAAO;YACL,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC;;0BACC,6LAAC;gBAAoB,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC;gBACC,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,6LAAC;wBAAa,WAAU;kCACrB,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,6LAAC;;;;;oBACA,OAAO,OAAO,CAAC,qHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,6LAAC;4BAEC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,6LAAC;oCAAK,WAAU;8CAAW,qHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,6LAAC;oCAAI,WAAU;;;;;;;2BAhBZ;;;;;;;;;;;;;;;;;AAuBjB;GAnEgB;;QACmB,iIAAA,CAAA,iBAAc;;;KADjC", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Role } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport { AvatarFallback } from '@/components/ui/avatar'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: Role) => {\n    switch (role) {\n      case Role.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case Role.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case Role.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case Role.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <Shield className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRole = (role: Role) => {\n    switch (role) {\n      case Role.ADMIN:\n        return 'مدير'\n      case Role.SCHOOL:\n        return 'مدرسة'\n      case Role.DELIVERY:\n        return 'شريك توصيل'\n      case Role.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case Role.ADMIN:\n        return '/dashboard/admin'\n      case Role.SCHOOL:\n        return '/dashboard/school'\n      case Role.DELIVERY:\n        return '/dashboard/delivery'\n      case Role.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\"> menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRole (profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n\n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            {getRoleIcon(profile.role)}\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuSeparator />\n\n        <DropdownMenuItem\n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;;;AAPA;;;;;;AASO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,6LAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,uHAAA,CAAA,OAAI,CAAC,KAAK;gBACb,qBAAO,6LAAC;oBAAO,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,OAAI,CAAC,MAAM;gBACd,qBAAO,6LAAC;oBAAO,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,OAAI,CAAC,QAAQ;gBAChB,qBAAO,6LAAC;oBAAM,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,OAAI,CAAC,OAAO;gBACf,qBAAO,6LAAC;oBAAc,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC;oBAAO,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK,uHAAA,CAAA,OAAI,CAAC,KAAK;gBACb,OAAO;YACT,KAAK,uHAAA,CAAA,OAAI,CAAC,MAAM;gBACd,OAAO;YACT,KAAK,uHAAA,CAAA,OAAI,CAAC,QAAQ;gBAChB,OAAO;YACT,KAAK,uHAAA,CAAA,OAAI,CAAC,OAAO;gBACf,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,uHAAA,CAAA,OAAI,CAAC,KAAK;gBACb,OAAO;YACT,KAAK,uHAAA,CAAA,OAAI,CAAC,MAAM;gBACd,OAAO;YACT,KAAK,uHAAA,CAAA,OAAI,CAAC,QAAQ;gBAChB,OAAO;YACT,KAAK,uHAAA,CAAA,OAAI,CAAC,OAAO;gBACf,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAoB,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC;gBAAoB,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,6LAAC;wBAAkB,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,6LAAC;sDAAM,QAAS,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAIlC,6LAAC;;;;;kCAED,6LAAC;wBAAiB,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC;wBAAiB,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;gCACtC,YAAY,QAAQ,IAAI;8CACzB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC;;;;;kCAED,6LAAC;wBACC,WAAU;wBACV,SAAS;;0CAET,6LAAC;gCAAO,WAAU;;;;;;0CAClB,6LAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9HgB;;QACqB,kIAAA,CAAA,UAAO;QAC5B,iIAAA,CAAA,iBAAc;;;KAFd", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\n\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAIA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Bell } from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  // Mock data for now\n  const notifications: any[] = []\n  const unreadCount = 0\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\" className=\"relative\">\n          <Bell className=\"h-4 w-4\" />\n          {unreadCount > 0 && (\n            <Badge\n              variant=\"destructive\"\n              className=\"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs\"\n            >\n              {unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-80\">\n        <div className=\"p-4 text-center text-gray-500\">\n          لا توجد إشعارات\n        </div>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAMA;;;AAXA;;;;;;AAaO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,oBAAoB;IACpB,MAAM,gBAAuB,EAAE;IAC/B,MAAM,cAAc;IAEpB,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAO,WAAU;;sCAC9C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BA<PERSON>,WAAU;sCAET;;;;;;;;;;;;;;;;;0BAKT,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;0BACzC,cAAA,6LAAC;oBAAI,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAMvD;GA7BgB;KAAA", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect, useMemo, useCallback } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\n\nimport { useCart } from '@/contexts/CartContext'\nimport { useMenu } from '@/contexts/MenuContext'\nimport { NavigationSkeleton, SideElementsSkeleton } from '@/components/NavigationSkeleton'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\n\n\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3,\n  Settings,\n  Package,\n  Calendar,\n  ShoppingCart\n} from 'lucide-react'\n\n// ✅ تم حذف interface غير المستخدم - يتم استخدام النوع من MenuContext\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  id: string\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    id: string\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const { cartCount, wishlistCount } = useCart()\n  const { menuItems, loading } = useMenu()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [isTransitioning, setIsTransitioning] = useState(false)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Handle transition state when loading changes\n  useEffect(() => {\n    if (!loading) {\n      setIsTransitioning(true)\n      const timer = setTimeout(() => {\n        setIsTransitioning(false)\n      }, 300) // Short delay for smooth transition\n      return () => clearTimeout(timer)\n    }\n  }, [loading, menuItems.length])\n\n  // ✅ تحسين الأداء: تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = useMemo(() => {\n    if (!menuItems || menuItems.length === 0) {\n      return []\n    }\n\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems\n      .filter(item => !item.parent_id && item.is_active)\n      .sort((a, b) => a.order_index - b.order_index) // ترتيب العناصر الرئيسية\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // ✅ تحسين: استخدام Map للأيقونات لتحسين الأداء\n      const iconMap = {\n        'Home': <Home className=\"h-4 w-4\" />,\n        'ShoppingBag': <ShoppingBag className=\"h-4 w-4\" />,\n        'Palette': <Palette className=\"h-4 w-4\" />,\n        'Search': <Search className=\"h-4 w-4\" />,\n        'Info': <Info className=\"h-4 w-4\" />,\n        'Phone': <Phone className=\"h-4 w-4\" />,\n        'Grid3X3': <Grid3X3 className=\"h-4 w-4\" />,\n        'ExternalLink': <ExternalLink className=\"h-4 w-4\" />,\n        'FileText': <FileText className=\"h-4 w-4\" />,\n        'Settings': <Settings className=\"h-4 w-4\" />,\n        'Package': <Package className=\"h-4 w-4\" />,\n        'Calendar': <Calendar className=\"h-4 w-4\" />,\n        'ShoppingCart': <ShoppingCart className=\"h-4 w-4\" />\n      }\n\n      const icon = item.icon && iconMap[item.icon as keyof typeof iconMap]\n        ? iconMap[item.icon as keyof typeof iconMap]\n        : <LinkIcon className=\"h-4 w-4\" />\n\n      // البحث عن القوائم الفرعية لهذا العنصر مع الترتيب\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .sort((a, b) => a.order_index - b.order_index) // ترتيب العناصر الفرعية\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            id: subItem.id,\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        id: item.id,\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }, [menuItems, locale]) // ✅ إصلاح: dependencies صحيحة\n\n  // ✅ تحسين: القائمة الافتراضية مع useMemo لتجنب إعادة الإنشاء\n  const defaultNavItems: NavItem[] = useMemo(() => [\n    {\n      id: 'default-home',\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      id: 'default-catalog',\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      id: 'default-about',\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      id: 'default-contact',\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ], [t]) // ✅ إصلاح: dependency على t\n\n  // ✅ تحديد عناصر القائمة بناءً على الحالة مع تحسين الاستقرار\n  const allNavItems: NavItem[] = useMemo(() => {\n    if (loading) {\n      // أثناء التحميل، لا نعرض أي عناصر (سيتم عرض skeleton)\n      return []\n    } else if (menuItems.length > 0) {\n      // إذا كانت هناك عناصر من قاعدة البيانات، استخدمها\n      return getNavItemsFromDB\n    } else {\n      // إذا لم تكن هناك عناصر من قاعدة البيانات، استخدم القائمة الافتراضية\n      return defaultNavItems\n    }\n  }, [loading, menuItems.length, getNavItemsFromDB, defaultNavItems]) // ✅ إصلاح: إزالة isTransitioning غير الضروري\n\n  // ✅ تحسين الأداء: استخدام useCallback للدوال\n  const isActive = useCallback((href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }, [pathname])\n\n  // ✅ تحسين الأداء: useCallback لمعالجة النقر على القائمة المحمولة\n  const handleMobileMenuToggle = useCallback(() => {\n    setIsMobileMenuOpen(prev => !prev)\n  }, [])\n\n  // ✅ تحسين الأداء: useCallback لإغلاق القائمة المحمولة\n  const closeMobileMenu = useCallback(() => {\n    setIsMobileMenuOpen(false)\n  }, [])\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"mobile-container\">\n        <div className=\"flex justify-between items-center py-3 min-h-[60px]\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-2 sm:gap-3 hover:opacity-80 transition-all duration-300 group touch-target\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-8 w-8 sm:h-9 sm:w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-lg sm:text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium hidden sm:block\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"nav-container\">\n            {(loading || isTransitioning) ? (\n              <NavigationSkeleton />\n            ) : (\n              <nav className=\"hidden lg:flex items-center gap-1\" role=\"navigation\" aria-label=\"القائمة الرئيسية\">\n                {allNavItems.map((item, index) => {\n                  // تحديد ما إذا كان الرابط خارجي\n                  const isExternal = item.target_type === 'external'\n                  const hasSubItems = item.subItems && item.subItems.length > 0\n\n                  // إذا كان العنصر له قوائم فرعية\n                  if (hasSubItems) {\n                    return (\n                      <div\n                        key={item.id}\n                        className={`relative group nav-item-enter nav-stagger-${Math.min(index + 1, 6)}`}\n                      >\n                        <button\n                          className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                            isActive(item.href)\n                              ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                              : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                          }`}\n                          aria-label={`${item.label} - قائمة فرعية`}\n                          aria-haspopup=\"true\"\n                          aria-expanded=\"false\"\n                        >\n                          <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                            {item.icon}\n                          </span>\n                          <span className=\"text-sm font-medium\">\n                            {item.label}\n                          </span>\n                          <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                        </button>\n\n                        {/* القائمة الفرعية */}\n                        <div\n                          className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\"\n                          role=\"menu\"\n                          aria-label={`قائمة ${item.label} الفرعية`}\n                        >\n                          <div className=\"py-2\">\n                            {item.subItems?.map((subItem) => {\n                              const subIsExternal = subItem.target_type === 'external'\n                              const subLinkProps = subIsExternal\n                                ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                                : { href: subItem.href }\n\n                              return (\n                                <Link\n                                  key={subItem.id}\n                                  {...subLinkProps}\n                                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                                >\n                                  {subItem.label}\n                                  {subIsExternal && (\n                                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                                  )}\n                                </Link>\n                              )\n                            })}\n                          </div>\n                        </div>\n                      </div>\n                    )\n                  }\n\n                  // العناصر العادية بدون قوائم فرعية\n                  const linkProps = isExternal\n                    ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                    : { href: item.href }\n\n                  return (\n                    <Link\n                      key={item.id}\n                      {...linkProps}\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 nav-item-enter nav-stagger-${Math.min(index + 1, 6)} ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      {isExternal && (\n                        <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                      )}\n                      {isActive(item.href) && (\n                        <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                      )}\n                    </Link>\n                  )\n                })}\n              </nav>\n            )}\n          </div>\n\n          {/* Desktop Actions */}\n          {(loading || isTransitioning) ? (\n            <SideElementsSkeleton />\n          ) : (\n            <div className=\"hidden lg:flex items-center gap-2 xl:gap-3\">\n              {/* Wishlist */}\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative group touch-target\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group touch-target\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartCount > 99 ? '99+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n            </div>\n          )}\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-1 sm:gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative touch-target mobile-button\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartCount > 9 ? '9+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative touch-target mobile-button min-h-[48px] min-w-[48px]\"\n              onClick={handleMobileMenuToggle}\n              aria-label={isMobileMenuOpen ? 'إغلاق القائمة' : 'فتح القائمة'}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out mobile-scroll ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4 mobile-spacing\">\n            {(loading || isTransitioning) ? (\n              <NavigationSkeleton isMobile={true} />\n            ) : (\n              <nav className=\"flex flex-col gap-1 mb-6\">\n                {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.id} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.id}\n                              {...subLinkProps}\n                              onClick={closeMobileMenu}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.id}\n                    {...linkProps}\n                    onClick={closeMobileMenu}\n                    className={`flex items-center gap-3 px-4 py-4 mx-2 rounded-xl transition-all duration-300 font-medium touch-target mobile-button ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"mobile-text-base\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n              </nav>\n            )}\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700 mobile-spacing-sm\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative touch-target mobile-button\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAvBA;;;;;;;;;;;;;;;AA4DO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,oBAAoB;QACtB;+BAAG;QAAC;KAAS;IAEb,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,mBAAmB;gBACnB,MAAM,QAAQ;kDAAW;wBACvB,mBAAmB;oBACrB;iDAAG,KAAK,oCAAoC;;gBAC5C;4CAAO,IAAM,aAAa;;YAC5B;QACF;+BAAG;QAAC;QAAS,UAAU,MAAM;KAAC;IAE9B,yEAAyE;IACzE,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAChC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;gBACxC,OAAO,EAAE;YACX;YAEA,+DAA+D;YAC/D,MAAM,YAAY,UACf,MAAM;mEAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;kEAChD,IAAI;mEAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;kEAAE,yBAAyB;;YAE1E,OAAO,UAAU,GAAG;yDAAC,CAAA;oBACnB,mCAAmC;oBACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;oBACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;wBACpC,QAAQ,KAAK,QAAQ;oBACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;wBAC3C,QAAQ,KAAK,QAAQ;oBACvB;oBAEA,6BAA6B;oBAC7B,IAAI,OAAO,KAAK,YAAY;oBAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;wBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;oBACtC;oBAEA,+CAA+C;oBAC/C,MAAM,UAAU;wBACd,sBAAQ,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACxB,6BAAe,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtC,yBAAW,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC9B,wBAAU,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAC5B,sBAAQ,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACxB,uBAAS,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAC1B,yBAAW,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC9B,8BAAgB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACxC,0BAAY,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAChC,0BAAY,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAChC,yBAAW,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC9B,0BAAY,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAChC,8BAAgB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;oBAC1C;oBAEA,MAAM,OAAO,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,CAAyB,GAChE,OAAO,CAAC,KAAK,IAAI,CAAyB,iBAC1C,6LAAC,qMAAA,CAAA,OAAQ;wBAAC,WAAU;;;;;;oBAExB,kDAAkD;oBAClD,MAAM,WAAW,UACd,MAAM;0EAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS;yEACpE,IAAI;0EAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;yEAAE,wBAAwB;qBACtE,GAAG;0EAAC,CAAA;4BACH,IAAI,WAAW,QAAQ,QAAQ;4BAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;gCACvC,WAAW,QAAQ,QAAQ;4BAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;gCAC9C,WAAW,QAAQ,QAAQ;4BAC7B;4BAEA,IAAI,UAAU,QAAQ,YAAY;4BAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;gCAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;4BAC5C;4BAEA,OAAO;gCACL,IAAI,QAAQ,EAAE;gCACd,MAAM;gCACN,OAAO;gCACP,aAAa,QAAQ,WAAW;4BAClC;wBACF;;oBAEF,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX;wBACA;wBACA;wBACA,aAAa,KAAK,WAAW;wBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;oBAC7C;gBACF;;QACF;gDAAG;QAAC;QAAW;KAAO,EAAE,8BAA8B;;IAEtD,6DAA6D;IAC7D,MAAM,kBAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE,IAAM;gBAC/C;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO,EAAE;oBACT,oBAAM,6LAAC,sMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO,EAAE;oBACT,oBAAM,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO,EAAE,uBAAuB;oBAChC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO,EAAE,yBAAyB;oBAClC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,aAAa;gBACf;aACD;8CAAE;QAAC;KAAE,EAAE,4BAA4B;;IAEpC,4DAA4D;IAC5D,MAAM,cAAyB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YACrC,IAAI,SAAS;gBACX,sDAAsD;gBACtD,OAAO,EAAE;YACX,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;gBAC/B,kDAAkD;gBAClD,OAAO;YACT,OAAO;gBACL,qEAAqE;gBACrE,OAAO;YACT;QACF;0CAAG;QAAC;QAAS,UAAU,MAAM;QAAE;QAAmB;KAAgB,EAAE,6CAA6C;;IAEjH,6CAA6C;IAC7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAC5B,IAAI,SAAS,KAAK;gBAChB,OAAO,aAAa;YACtB;YACA,OAAO,SAAS,UAAU,CAAC;QAC7B;2CAAG;QAAC;KAAS;IAEb,iEAAiE;IACjE,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACzC;kEAAoB,CAAA,OAAQ,CAAC;;QAC/B;yDAAG,EAAE;IAEL,sDAAsD;IACtD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAClC,oBAAoB;QACtB;kDAAG,EAAE;IAEL,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA2E;;;;;;sDAG3F,6LAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,6LAAC;4BAAI,WAAU;sCACZ,AAAC,WAAW,gCACX,6LAAC,2IAAA,CAAA,qBAAkB;;;;qDAEnB,6LAAC;gCAAI,WAAU;gCAAoC,MAAK;gCAAa,cAAW;0CAC7E,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,6LAAC;4CAEC,WAAW,CAAC,0CAA0C,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI;;8DAEhF,6LAAC;oDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,cAAY,GAAG,KAAK,KAAK,CAAC,cAAc,CAAC;oDACzC,iBAAc;oDACd,iBAAc;;sEAEd,6LAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sEAChH,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,cAAY,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;8DAEzC,cAAA,6LAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;4DACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;4DAC9C,MAAM,eAAe,gBACjB;gEAAE,MAAM,QAAQ,IAAI;gEAAE,QAAQ;gEAAU,KAAK;4DAAsB,IACnE;gEAAE,MAAM,QAAQ,IAAI;4DAAC;4DAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;gEAEF,GAAG,YAAY;gEAChB,WAAU;;oEAET,QAAQ,KAAK;oEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;+DANrB,QAAQ,EAAE;;;;;wDAUrB;;;;;;;;;;;;2CA/CC,KAAK,EAAE;;;;;oCAoDlB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,WAAW,CAAC,iIAAiI,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,EACrK,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;0DAEF,6LAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;0DAChH,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAEzB,SAAS,KAAK,IAAI,mBACjB,6LAAC;gDAAI,WAAU;;;;;;;uCAlBZ,KAAK,EAAE;;;;;gCAsBlB;;;;;;;;;;;wBAMJ,WAAW,gCACX,6LAAC,2IAAA,CAAA,uBAAoB;;;;iDAErB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAA8B,OAAO;8CACjF,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAA8B,OAAO;8CAC/E,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOlC,6LAAC,8JAAA,CAAA,uBAAoB;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;sDACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;sCAKX,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAsC,OAAO;8CACvF,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOhC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,cAAY,mBAAmB,kBAAkB;8CAEjD,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBACC,WAAW,CAAC,gFAAgF,EAC1F,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;4BACX,WAAW,gCACX,6LAAC,2IAAA,CAAA,qBAAkB;gCAAC,UAAU;;;;;qDAE9B,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACxB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,6LAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS;4DACT,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,EAAE;;;;;oDAWrB;;;;;;;2CA7CM,KAAK,EAAE;;;;;oCAiDrB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS;wCACT,WAAW,CAAC,qHAAqH,EAC/H,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,6LAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,EAAE;;;;;gCA2BlB;;;;;;0CAKF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;0DACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAsC,OAAO;0DACvF,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GAjhBgB;;QACQ,iIAAA,CAAA,iBAAc;QACC,kIAAA,CAAA,UAAO;QACb,kIAAA,CAAA,UAAO;QACrB,qIAAA,CAAA,cAAW;;;KAJd;uCAmhBD", "debugId": null}}, {"offset": {"line": 2268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Footer.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport Link from 'next/link'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport {\n  GraduationCap,\n  Mail,\n  Phone,\n  MapPin,\n  Facebook,\n  Twitter,\n  Instagram,\n  Linkedin,\n  Heart\n} from 'lucide-react'\n\nexport function Footer() {\n  const { t } = useTranslation()\n\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    company: [\n      { href: '/about', label: 'من نحن' },\n      { href: '/contact', label: 'تواصل معنا' },\n      { href: '/support', label: 'الدعم الفني' },\n      { href: '/privacy', label: 'سياسة الخصوصية' }\n    ],\n    services: [\n      { href: '/catalog', label: 'الكتالوج' },\n      { href: '/customize', label: 'التخصيص' },\n      { href: '/track-order', label: 'تتبع الطلب' },\n      { href: '/size-guide', label: 'دليل المقاسات' }\n    ],\n    support: [\n      { href: '/faq', label: 'الأسئلة الشائعة' },\n      { href: '/terms-conditions', label: 'الشروط والأحكام' },\n      { href: '/privacy-policy', label: 'سياسة الخصوصية' },\n      { href: '/support', label: 'الدعم الفني' }\n    ]\n  }\n\n  const socialLinks = [\n    { href: '#', icon: Facebook, label: 'Facebook' },\n    { href: '#', icon: Twitter, label: 'Twitter' },\n    { href: '#', icon: Instagram, label: 'Instagram' },\n    { href: '#', icon: Linkedin, label: 'LinkedIn' }\n  ]\n\n  return (\n    <footer className=\"bg-gray-900 text-white mt-16\">\n      <div className=\"container mx-auto px-4 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2\">\n              <GraduationCap className=\"h-8 w-8 text-blue-400\" />\n              <span className=\"text-xl font-bold\">Graduation Toqs</span>\n            </div>\n            <p className=\"text-gray-300 arabic-text leading-relaxed\">\n              أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي\n            </p>\n            \n            {/* Contact Info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2 text-sm\">\n                <Mail className=\"h-4 w-4 text-blue-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm\">\n                <Phone className=\"h-4 w-4 text-blue-400\" />\n                <span>+212 6 12 34 56 78</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm\">\n                <MapPin className=\"h-4 w-4 text-blue-400\" />\n                <span className=\"arabic-text\">بني ملال، المغرب</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الشركة</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الخدمات</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.services.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الدعم</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Social Media & Copyright */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              <span className=\"text-gray-400 arabic-text\">تابعنا على:</span>\n              {socialLinks.map((social) => {\n                const Icon = social.icon\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-blue-400 transition-colors\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </a>\n                )\n              })}\n            </div>\n\n            {/* Copyright */}\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-gray-400 text-sm arabic-text\">\n                © {currentYear} Graduation Toqs. جميع الحقوق محفوظة\n              </p>\n              <p className=\"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1\">\n                <span className=\"arabic-text\">صُنع بـ</span>\n                <Heart className=\"h-3 w-3 text-red-500\" />\n                <span className=\"arabic-text\">في المغرب</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAgBO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAU,OAAO;YAAS;YAClC;gBAAE,MAAM;gBAAY,OAAO;YAAa;YACxC;gBAAE,MAAM;gBAAY,OAAO;YAAc;YACzC;gBAAE,MAAM;gBAAY,OAAO;YAAiB;SAC7C;QACD,UAAU;YACR;gBAAE,MAAM;gBAAY,OAAO;YAAW;YACtC;gBAAE,MAAM;gBAAc,OAAO;YAAU;YACvC;gBAAE,MAAM;gBAAgB,OAAO;YAAa;YAC5C;gBAAE,MAAM;gBAAe,OAAO;YAAgB;SAC/C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAQ,OAAO;YAAkB;YACzC;gBAAE,MAAM;gBAAqB,OAAO;YAAkB;YACtD;gBAAE,MAAM;gBAAmB,OAAO;YAAiB;YACnD;gBAAE,MAAM;gBAAY,OAAO;YAAc;SAC1C;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAK,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAK,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAU;QAC7C;YAAE,MAAM;YAAK,MAAM,+MAAA,CAAA,YAAS;YAAE,OAAO;QAAY;QACjD;YAAE,MAAM;YAAK,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;KAChD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA4C;;;;;;8CAKzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;oCAC3C,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,6LAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,KAAK;sDAExB,cAAA,6LAAC;gDAAK,WAAU;;;;;;2CALX,OAAO,KAAK;;;;;oCAQvB;;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAoC;4CAC5C;4CAAY;;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GA1JgB;;QACA,iIAAA,CAAA,iBAAc;;;KADd", "debugId": null}}, {"offset": {"line": 2753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/layouts/PageLayout.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { ReactNode } from 'react'\nimport { Navigation } from '@/components/Navigation'\nimport { Footer } from '@/components/Footer'\n\ninterface PageLayoutProps {\n  children: ReactNode\n  className?: string\n  showFooter?: boolean\n  containerClassName?: string\n}\n\nexport function PageLayout({ \n  children, \n  className = \"\", \n  showFooter = true,\n  containerClassName = \"container mx-auto px-4 py-8\"\n}: PageLayoutProps) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      {/* Header Navigation - موحد عبر جميع الصفحات */}\n      <Navigation />\n      \n      {/* Main Content */}\n      <main className={containerClassName}>\n        {children}\n      </main>\n      \n      {/* Footer - اختياري */}\n      {showFooter && <Footer />}\n    </div>\n  )\n}\n\n// Layout خاص بلوحات التحكم\nexport function DashboardLayout({ \n  children, \n  className = \"\",\n  title,\n  description \n}: PageLayoutProps & { title?: string; description?: string }) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      <Navigation />\n      \n      <main className=\"container mx-auto px-4 py-8\">\n        {(title || description) && (\n          <div className=\"mb-8\">\n            {title && (\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n                {title}\n              </h1>\n            )}\n            {description && (\n              <p className=\"text-gray-600 dark:text-gray-300 arabic-text\">\n                {description}\n              </p>\n            )}\n          </div>\n        )}\n        {children}\n      </main>\n    </div>\n  )\n}\n\n// Layout للصفحات الخاصة (مثل 404، خطأ)\nexport function ErrorLayout({ children, className = \"\" }: PageLayoutProps) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      <Navigation />\n      <main className=\"container mx-auto px-4 py-8 flex items-center justify-center min-h-[calc(100vh-200px)]\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAIA;AACA;AAJA;;;;AAaO,SAAS,WAAW,EACzB,QAAQ,EACR,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,qBAAqB,6BAA6B,EAClC;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAC,yHAAyH,EAAE,WAAW;;0BAErJ,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAK,WAAW;0BACd;;;;;;YAIF,4BAAc,6LAAC,+HAAA,CAAA,SAAM;;;;;;;;;;;AAG5B;KApBgB;AAuBT,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,KAAK,EACL,WAAW,EACgD;IAC3D,qBACE,6LAAC;QAAI,WAAW,CAAC,yHAAyH,EAAE,WAAW;;0BACrJ,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;oBACb,CAAC,SAAS,WAAW,mBACpB,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,6BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAKR;;;;;;;;;;;;;AAIT;MA7BgB;AAgCT,SAAS,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;IACvE,qBACE,6LAAC;QAAI,WAAW,CAAC,wHAAwH,EAAE,WAAW;;0BACpJ,6LAAC,mIAAA,CAAA,aAAU;;;;;0BACX,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;MATgB", "debugId": null}}, {"offset": {"line": 2885, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\n\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Progress } from '@/components/ui/progress'\nimport {\n  AlertCircle,\n  Upload,\n  Plus,\n  Save,\n  Eye,\n  Star,\n  Bell,\n  ArrowRight\n} from 'lucide-react'\n\nexport function HeroSection() {\n  const { t } = useTranslation()\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    setIsVisible(true)\n  }, [])\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\" />\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-20 left-4 sm:left-10 w-16 h-16 sm:w-20 sm:h-20 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-pulse\" />\n        <div className=\"absolute top-40 right-4 sm:right-20 w-12 h-12 sm:w-16 sm:h-16 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 animate-bounce\" />\n        <div className=\"absolute bottom-20 left-4 sm:left-20 w-20 h-20 sm:w-24 sm:h-24 bg-yellow-200 dark:bg-yellow-800 rounded-full opacity-20 animate-pulse\" />\n        <div className=\"absolute bottom-40 right-4 sm:right-10 w-10 h-10 sm:w-12 sm:h-12 bg-green-200 dark:bg-green-800 rounded-full opacity-20 animate-bounce\" />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 mobile-container py-12 sm:py-16 md:py-20\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n          {/* Badges */}\n          <div className={`flex flex-wrap justify-center gap-2 sm:gap-3 mb-6 sm:mb-8 transition-all duration-1000 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <Badge variant=\"secondary\" className=\"mobile-badge mobile-badge-primary px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium\">\n              <Users className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" />\n              {t('home.hero.badges.trusted')}\n            </Badge>\n            <Badge variant=\"secondary\" className=\"mobile-badge mobile-badge-success px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium\">\n              <School className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" />\n              {t('home.hero.badges.schools')}\n            </Badge>\n            <Badge variant=\"secondary\" className=\"mobile-badge mobile-badge-warning px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium\">\n              <Star className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" />\n              {t('home.hero.badges.satisfaction')}\n            </Badge>\n          </div>\n\n          {/* Main Title */}\n          <h1 className={`mobile-text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6 arabic-text leading-tight transition-all duration-1000 delay-200 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent\">\n              {t('home.hero.title')}\n            </span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className={`mobile-text-lg sm:text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-6 sm:mb-8 max-w-4xl mx-auto arabic-text leading-relaxed transition-all duration-1000 delay-400 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            {t('home.hero.subtitle')}\n          </p>\n\n          {/* Description */}\n          <p className={`mobile-text-base sm:text-lg text-gray-500 dark:text-gray-400 mb-8 sm:mb-12 max-w-3xl mx-auto arabic-text leading-relaxed transition-all duration-1000 delay-600 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            {t('home.hero.description')}\n          </p>\n\n          {/* CTA Buttons */}\n          <div className={`flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 transition-all duration-1000 delay-800 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <Button\n              size=\"lg\"\n              className=\"mobile-btn mobile-btn-primary w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 sm:px-8 py-3 sm:py-4 mobile-text-base sm:text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group\"\n              asChild\n            >\n              <a href=\"/customize\" className=\"flex items-center justify-center gap-2\">\n                <Sparkles className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n                {t('home.hero.cta.primary')}\n                <ArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform\" />\n              </a>\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"mobile-btn mobile-btn-secondary w-full sm:w-auto border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 px-6 sm:px-8 py-3 sm:py-4 mobile-text-base sm:text-lg font-semibold arabic-text transition-all duration-300\"\n              asChild\n            >\n              <a href=\"/catalog\" className=\"flex items-center justify-center gap-2\">\n                <GraduationCap className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n                {t('home.hero.cta.secondary')}\n              </a>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"lg\"\n              className=\"mobile-btn w-full sm:w-auto text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 px-6 sm:px-8 py-3 sm:py-4 mobile-text-base sm:text-lg font-semibold arabic-text transition-all duration-300 group\"\n            >\n              <Play className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform\" />\n              {t('home.hero.cta.watchDemo')}\n            </Button>\n          </div>\n\n          {/* Scroll Indicator */}\n          <div className={`animate-bounce transition-all duration-1000 delay-1000 ${\n            isVisible ? 'opacity-100' : 'opacity-0'\n          }`}>\n            <ChevronDown className=\"w-8 h-8 text-gray-400 mx-auto\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white dark:from-gray-900 to-transparent\" />\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAOA;AAAA;;;AAZA;;;;;;AAuBO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,CAAC,uFAAuF,EACtG,YAAY,8BAA8B,4BAC1C;;8CACA,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,6LAAC;4CAAM,WAAU;;;;;;wCAChB,EAAE;;;;;;;8CAEL,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,6LAAC;4CAAO,WAAU;;;;;;wCACjB,EAAE;;;;;;;8CAEL,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,EAAE;;;;;;;;;;;;;sCAKP,6LAAC;4BAAG,WAAW,CAAC,0KAA0K,EACxL,YAAY,8BAA8B,4BAC1C;sCACA,cAAA,6LAAC;gCAAK,WAAU;0CACb,EAAE;;;;;;;;;;;sCAKP,6LAAC;4BAAE,WAAW,CAAC,yKAAyK,EACtL,YAAY,8BAA8B,4BAC1C;sCACC,EAAE;;;;;;sCAIL,6LAAC;4BAAE,WAAW,CAAC,gKAAgK,EAC7K,YAAY,8BAA8B,4BAC1C;sCACC,EAAE;;;;;;sCAIL,6LAAC;4BAAI,WAAW,CAAC,2HAA2H,EAC1I,YAAY,8BAA8B,4BAC1C;;8CACA,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,6LAAC;wCAAE,MAAK;wCAAa,WAAU;;0DAC7B,6LAAC;gDAAS,WAAU;;;;;;4CACnB,EAAE;0DACH,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI1B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,6LAAC;wCAAE,MAAK;wCAAW,WAAU;;0DAC3B,6LAAC;gDAAc,WAAU;;;;;;4CACxB,EAAE;;;;;;;;;;;;8CAIP,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;;;;;;wCACf,EAAE;;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAW,CAAC,uDAAuD,EACtE,YAAY,gBAAgB,aAC5B;sCACA,cAAA,6LAAC;gCAAY,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GApHgB;;QACA,iIAAA,CAAA,iBAAc;;;KADd", "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useStats.ts"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect } from 'react'\n\ninterface Stats {\n  customers: number\n  schools: number\n  orders: number\n  satisfaction: number\n}\n\ninterface UseStatsReturn {\n  stats: Stats\n  loading: boolean\n  error: string | null\n  refetch: () => void\n}\n\nexport function useStats(): UseStatsReturn {\n  const [stats, setStats] = useState<Stats>({\n    customers: 1200,\n    schools: 50,\n    orders: 2500,\n    satisfaction: 98\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchStats = async () => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      // Simulate API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // Mock data - replace with actual API response\n      const mockStats: Stats = {\n        customers: 1200 + Math.floor(Math.random() * 100),\n        schools: 50 + Math.floor(Math.random() * 10),\n        orders: 2500 + Math.floor(Math.random() * 200),\n        satisfaction: 98\n      }\n      \n      setStats(mockStats)\n    } catch (err) {\n      setError('فشل في تحميل الإحصائيات')\n      console.error('Error fetching stats:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const refetch = () => {\n    fetchStats()\n  }\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  return {\n    stats,\n    loading,\n    error,\n    refetch\n  }\n}\n"], "names": [], "mappings": ";;;AAGA;;AAFA;;AAkBO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa;QACjB,WAAW;QACX,SAAS;QAET,IAAI;YACF,uDAAuD;YACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,+CAA+C;YAC/C,MAAM,YAAmB;gBACvB,WAAW,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC7C,SAAS,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACzC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC1C,cAAc;YAChB;YAEA,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GAjDgB", "debugId": null}}, {"offset": {"line": 3267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\n\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAIA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useStats } from '@/hooks/useStats'\nimport { Card, CardContent } from '@/components/ui/card'\nimport {\n  Users,\n  GraduationCap,\n  Award,\n  TrendingUp\n} from 'lucide-react'\n\ninterface StatItemProps {\n  icon: React.ReactNode\n  number: string\n  label: string\n  delay: number\n  color: string\n}\n\nfunction StatItem({ icon, number, label, delay, color }: StatItemProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [animatedNumber, setAnimatedNumber] = useState('0')\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n          // Animate number counting\n          const finalNumber = number.replace(/[^0-9]/g, '')\n          const numValue = parseInt(finalNumber)\n          if (!isNaN(numValue)) {\n            let current = 0\n            const increment = numValue / 50\n            const timer = setInterval(() => {\n              current += increment\n              if (current >= numValue) {\n                setAnimatedNumber(number)\n                clearInterval(timer)\n              } else {\n                setAnimatedNumber(Math.floor(current).toString() + (number.includes('+') ? '+' : '') + (number.includes('%') ? '%' : ''))\n              }\n            }, 30)\n          } else {\n            setAnimatedNumber(number)\n          }\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [number])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"mobile-card text-center hover:shadow-lg transition-all duration-300 border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\">\n        <CardContent className=\"mobile-spacing p-6 sm:p-8\">\n          <div className={`inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-full mb-3 sm:mb-4 ${color}`}>\n            {icon}\n          </div>\n          <div className=\"mobile-text-2xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-2 arabic-text\">\n            {animatedNumber}\n          </div>\n          <div className=\"mobile-text-sm sm:text-base text-gray-600 dark:text-gray-300 font-medium arabic-text\">\n            {label}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function StatsSection() {\n  const { t } = useTranslation()\n  const { stats: statsData, loading } = useStats()\n\n  const stats = [\n    {\n      icon: <Users className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.customers.toLocaleString()}+`,\n      label: t('home.stats.customers.label'),\n      color: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n      delay: 0\n    },\n    {\n      icon: <School className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.schools}+`,\n      label: t('home.stats.schools.label'),\n      color: \"bg-gradient-to-br from-green-500 to-green-600\",\n      delay: 200\n    },\n    {\n      icon: <ShoppingBag className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.orders.toLocaleString()}+`,\n      label: t('home.stats.orders.label'),\n      color: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n      delay: 400\n    },\n    {\n      icon: <Star className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.satisfaction}%`,\n      label: t('home.stats.satisfaction.label'),\n      color: \"bg-gradient-to-br from-yellow-500 to-yellow-600\",\n      delay: 600\n    }\n  ]\n\n  return (\n    <section className=\"py-12 sm:py-16 md:py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"mobile-container\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12 sm:mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-2 sm:px-4 sm:py-2 rounded-full mobile-text-sm font-medium mb-4\">\n            <TrendingUp className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n            إحصائيات المنصة\n          </div>\n          <h2 className=\"mobile-text-2xl sm:text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.stats.title')}\n          </h2>\n          <p className=\"mobile-text-base sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto arabic-text\">\n            {t('home.stats.subtitle')}\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"mobile-grid gap-4 sm:gap-6 md:gap-8\">\n          {stats.map((stat, index) => (\n            <StatItem\n              key={index}\n              icon={stat.icon}\n              number={stat.number}\n              label={stat.label}\n              delay={stat.delay}\n              color={stat.color}\n            />\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"text-center mt-12 sm:mt-16\">\n          <p className=\"mobile-text-sm text-gray-500 dark:text-gray-400 arabic-text\">\n            آخر تحديث: {new Date().toLocaleDateString('ar-MA')}\n          </p>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAqBA,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAiB;;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,WAAW,IAAI;sCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,0BAA0B;wBAC1B,MAAM,cAAc,OAAO,OAAO,CAAC,WAAW;wBAC9C,MAAM,WAAW,SAAS;wBAC1B,IAAI,CAAC,MAAM,WAAW;4BACpB,IAAI,UAAU;4BACd,MAAM,YAAY,WAAW;4BAC7B,MAAM,QAAQ;4DAAY;oCACxB,WAAW;oCACX,IAAI,WAAW,UAAU;wCACvB,kBAAkB;wCAClB,cAAc;oCAChB,OAAO;wCACL,kBAAkB,KAAK,KAAK,CAAC,SAAS,QAAQ,KAAK,CAAC,OAAO,QAAQ,CAAC,OAAO,MAAM,EAAE,IAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,MAAM,EAAE;oCACzH;gCACF;2DAAG;wBACL,OAAO;4BACL,kBAAkB;wBACpB;oBACF;gBACF;qCACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,OAAO,CAAC,IAAI,OAAO;YAC9B;YAEA;sCAAO,IAAM,SAAS,UAAU;;QAClC;6BAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAW,CAAC,4FAA4F,EAAE,OAAO;kCACnH;;;;;;kCAEH,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;GA/DS;KAAA;AAiEF,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,OAAO,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAE7C,MAAM,QAAQ;QACZ;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,QAAQ,UAAU,QAAQ,GAAG,UAAU,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;YACpE,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,6LAAC;gBAAO,WAAU;;;;;;YACxB,QAAQ,UAAU,QAAQ,GAAG,UAAU,OAAO,CAAC,CAAC,CAAC;YACjD,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,6LAAC;gBAAY,WAAU;;;;;;YAC7B,QAAQ,UAAU,QAAQ,GAAG,UAAU,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC;YACjE,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,6LAAC;gBAAK,WAAU;;;;;;YACtB,QAAQ,UAAU,QAAQ,GAAG,UAAU,YAAY,CAAC,CAAC,CAAC;YACtD,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;sCAGlD,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4BAEC,MAAM,KAAK,IAAI;4BACf,QAAQ,KAAK,MAAM;4BACnB,OAAO,KAAK,KAAK;4BACjB,OAAO,KAAK,KAAK;4BACjB,OAAO,KAAK,KAAK;2BALZ;;;;;;;;;;8BAWX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA8D;4BAC7D,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAMtD;IA3EgB;;QACA,iIAAA,CAAA,iBAAc;QACU,2HAAA,CAAA,WAAQ;;;MAFhC", "debugId": null}}, {"offset": {"line": 3666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  ArrowRight,\n  CheckCircle,\n  Star,\n  Zap\n} from 'lucide-react'\n\ninterface FeatureCardProps {\n  icon: React.ReactNode\n  title: string\n  description: string\n  color: string\n  delay: number\n  features?: string[]\n}\n\nfunction FeatureCard({ icon, title, description, color, delay, features }: FeatureCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2\">\n        <CardHeader className=\"text-center pb-4\">\n          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${color} group-hover:scale-110 transition-transform duration-300`}>\n            {icon}\n          </div>\n          <CardTitle className=\"text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n            {title}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"text-center\">\n          <CardDescription className=\"text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4\">\n            {description}\n          </CardDescription>\n          \n          {features && (\n            <div className=\"space-y-2 mb-4\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 flex-shrink-0\" />\n                  <span className=\"arabic-text\">{feature}</span>\n                </div>\n              ))}\n            </div>\n          )}\n          \n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn\"\n          >\n            اعرف المزيد\n            <ArrowRight className=\"w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform\" />\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function FeaturesShowcase() {\n  const { t } = useTranslation()\n\n  const features = [\n    {\n      icon: <Palette className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.customization.title'),\n      description: t('home.features.customization.description'),\n      color: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n      delay: 0,\n      features: [\n        \"معاينة فورية ثلاثية الأبعاد\",\n        \"مكتبة ألوان واسعة\",\n        \"إكسسوارات متنوعة\"\n      ]\n    },\n    {\n      icon: <Sparkles className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.ai.title'),\n      description: t('home.features.ai.description'),\n      color: \"bg-gradient-to-br from-yellow-500 to-yellow-600\",\n      delay: 200,\n      features: [\n        \"اقتراحات ذكية مخصصة\",\n        \"تحليل الأسلوب الشخصي\",\n        \"مساعد افتراضي متقدم\"\n      ]\n    },\n    {\n      icon: <Users className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.roles.title'),\n      description: t('home.features.roles.description'),\n      color: \"bg-gradient-to-br from-green-500 to-green-600\",\n      delay: 400,\n      features: [\n        \"لوحة تحكم للطلاب\",\n        \"إدارة المدارس\",\n        \"نظام شركاء التوصيل\"\n      ]\n    },\n    {\n      icon: <GraduationCap className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.tracking.title'),\n      description: t('home.features.tracking.description'),\n      color: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n      delay: 600,\n      features: [\n        \"تتبع لحظي للطلبات\",\n        \"إشعارات فورية\",\n        \"تحديثات مستمرة\"\n      ]\n    },\n    {\n      icon: <Shield className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.quality.title'),\n      description: t('home.features.quality.description'),\n      color: \"bg-gradient-to-br from-red-500 to-red-600\",\n      delay: 800,\n      features: [\n        \"أقمشة فاخرة مختارة\",\n        \"تصنيع احترافي\",\n        \"ضمان الجودة\"\n      ]\n    },\n    {\n      icon: <Headphones className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.support.title'),\n      description: t('home.features.support.description'),\n      color: \"bg-gradient-to-br from-indigo-500 to-indigo-600\",\n      delay: 1000,\n      features: [\n        \"دعم على مدار الساعة\",\n        \"فريق متخصص\",\n        \"استجابة سريعة\"\n      ]\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <Sparkles className=\"w-4 h-4\" />\n            ميزاتنا المتقدمة\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.features.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text\">\n            {t('home.features.subtitle')}\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <FeatureCard\n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              color={feature.color}\n              delay={feature.delay}\n              features={feature.features}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAsBA,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAoB;;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,IAAI;yCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;wCACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,OAAO,CAAC,IAAI,OAAO;YAC9B;YAEA;yCAAO,IAAM,SAAS,UAAU;;QAClC;gCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAW,CAAC,4EAA4E,EAAE,MAAM,wDAAwD,CAAC;sCAC3J;;;;;;sCAEH,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;;;;;;;;;;;;8BAGL,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB;;;;;;wBAGF,0BACC,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;mCAFvB;;;;;;;;;;sCAQhB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;gCACX;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAlES;KAAA;AAoEF,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC;gBAAQ,WAAU;;;;;;YACzB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,6LAAC;gBAAS,WAAU;;;;;;YAC1B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,6LAAC;gBAAM,WAAU;;;;;;YACvB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,6LAAC;gBAAc,WAAU;;;;;;YAC/B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,6LAAC;gBAAO,WAAU;;;;;;YACxB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,6LAAC;gBAAW,WAAU;;;;;;YAC5B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAS,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,KAAK;4BACpB,aAAa,QAAQ,WAAW;4BAChC,OAAO,QAAQ,KAAK;4BACpB,OAAO,QAAQ,KAAK;4BACpB,UAAU,QAAQ,QAAQ;2BANrB;;;;;;;;;;;;;;;;;;;;;AAanB;IAhHgB;;QACA,iIAAA,CAAA,iBAAc;;;MADd", "debugId": null}}, {"offset": {"line": 4049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Progress } from '@/components/ui/progress'\nimport {\n  ArrowRight,\n  Brain,\n  Camera,\n  Lightbulb,\n  Wand2,\n  Cpu\n} from 'lucide-react'\n\ninterface AIFeatureProps {\n  icon: React.ReactNode\n  title: string\n  description: string\n  color: string\n  delay: number\n  isActive?: boolean\n}\n\nfunction AIFeatureCard({ icon, title, description, color, delay, isActive }: AIFeatureProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className={`h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2 ${\n        isActive ? 'ring-2 ring-blue-500 shadow-lg' : ''\n      }`}>\n        <CardHeader className=\"text-center pb-4\">\n          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${color} group-hover:scale-110 transition-transform duration-300 relative`}>\n            {icon}\n            {isActive && (\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\">\n                <div className=\"w-full h-full bg-green-500 rounded-full animate-ping\"></div>\n              </div>\n            )}\n          </div>\n          <CardTitle className=\"text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n            {title}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"text-center\">\n          <p className=\"text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4\">\n            {description}\n          </p>\n\n          <Button\n            variant=\"ghost\" \n            size=\"sm\" \n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn\"\n          >\n            جرب الآن\n            <ArrowRight className=\"w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform\" />\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nfunction AIModelCard({ name, provider, status, description, delay }: {\n  name: string\n  provider: string\n  status: 'active' | 'inactive'\n  description: string\n  delay: number\n}) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center gap-2\">\n              <Cpu className=\"w-5 h-5 text-blue-600\" />\n              <span className=\"font-semibold text-gray-900 dark:text-white\">{name}</span>\n            </div>\n            <Badge variant={status === 'active' ? 'default' : 'secondary'}\n              className={status === 'active' ? 'bg-green-500' : ''}\n            >\n              {status === 'active' ? 'نشط' : 'غير نشط'}\n            </Badge>\n          </div>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">{provider}</p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 arabic-text\">{description}</p>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function AIFeaturesSection() {\n  const { t } = useTranslation()\n\n  const aiFeatures = [\n    {\n      icon: <Brain className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.assistant.title'),\n      description: t('home.ai.features.assistant.description'),\n      color: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n      delay: 0,\n      isActive: true\n    },\n    {\n      icon: <Camera className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.visualization.title'),\n      description: t('home.ai.features.visualization.description'),\n      color: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n      delay: 200,\n      isActive: true\n    },\n    {\n      icon: <Lightbulb className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.recommendations.title'),\n      description: t('home.ai.features.recommendations.description'),\n      color: \"bg-gradient-to-br from-yellow-500 to-yellow-600\",\n      delay: 400,\n      isActive: true\n    },\n    {\n      icon: <Wand2 className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.chat.title'),\n      description: t('home.ai.features.chat.description'),\n      color: \"bg-gradient-to-br from-green-500 to-green-600\",\n      delay: 600,\n      isActive: true\n    }\n  ]\n\n  const aiModels = [\n    {\n      name: 'GPT-4',\n      provider: 'OpenAI',\n      status: 'active' as const,\n      description: 'نموذج متقدم للمحادثة والتوصيات الذكية',\n      delay: 0\n    },\n    {\n      name: 'Claude 3',\n      provider: 'Anthropic',\n      status: 'active' as const,\n      description: 'مساعد ذكي للتحليل والاقتراحات المخصصة',\n      delay: 200\n    },\n    {\n      name: 'Gemini Pro',\n      provider: 'Google',\n      status: 'active' as const,\n      description: 'نموذج متعدد الوسائط للمعاينة والتصميم',\n      delay: 400\n    },\n    {\n      name: 'Mistral AI',\n      provider: 'Mistral',\n      status: 'inactive' as const,\n      description: 'نموذج سريع للاستجابات الفورية',\n      delay: 600\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <Brain className=\"w-4 h-4\" />\n            الذكاء الاصطناعي\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.ai.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text\">\n            {t('home.ai.subtitle')}\n          </p>\n        </div>\n\n        {/* AI Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {aiFeatures.map((feature, index) => (\n            <AIFeatureCard\n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              color={feature.color}\n              delay={feature.delay}\n              isActive={feature.isActive}\n            />\n          ))}\n        </div>\n\n        {/* AI Models Section */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text\">\n              نماذج الذكاء الاصطناعي المتاحة\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300 arabic-text\">\n              نستخدم أحدث نماذج الذكاء الاصطناعي لتقديم أفضل تجربة\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {aiModels.map((model, index) => (\n              <AIModelCard\n                key={index}\n                name={model.name}\n                provider={model.provider}\n                status={model.status}\n                description={model.description}\n                delay={index * 100}\n              />\n            ))}\n          </div>\n\n          {/* AI Demo Section */}\n          <div className=\"text-center\">\n            <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 mb-6\">\n              <div className=\"flex items-center justify-center gap-2 mb-4\">\n                <Brain className=\"w-6 h-6 text-blue-600\" />\n                <span className=\"text-lg font-semibold text-gray-900 dark:text-white arabic-text\">\n                  جرب المساعد الذكي الآن\n                </span>\n              </div>\n              <p className=\"text-gray-600 dark:text-gray-300 arabic-text mb-4\">\n                اطلب من مساعدنا الذكي مساعدتك في اختيار ثوب التخرج المثالي\n              </p>\n              <div className=\"flex flex-wrap justify-center gap-4\">\n                <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white\">\n                  <Wand2 className=\"w-4 h-4 mr-2\" />\n                  ابدأ المحادثة\n                </Button>\n                <Button variant=\"outline\">\n                  <Camera className=\"w-4 h-4 mr-2\" />\n                  معاينة ثلاثية الأبعاد\n                </Button>\n                <Button variant=\"outline\">\n                  <Cpu className=\"w-4 h-4 mr-2\" />\n                  تخصيص ذكي\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;AA8BA,SAAS,cAAc,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAkB;;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,WAAW,IAAI;2CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;0CACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,OAAO,CAAC,IAAI,OAAO;YAC9B;YAEA;2CAAO,IAAM,SAAS,UAAU;;QAClC;kCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW,CAAC,iHAAiH,EACjI,WAAW,mCAAmC,IAC9C;;8BACA,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAW,CAAC,4EAA4E,EAAE,MAAM,iEAAiE,CAAC;;gCACpK;gCACA,0BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAIrB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;;;;;;;;;;;;8BAGL,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAE,WAAU;sCACV;;;;;;sCAGH,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;gCACX;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GA9DS;KAAA;AAgET,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAMhE;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,IAAI;yCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;wCACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,OAAO,CAAC,IAAI,OAAO;YAC9B;YAEA;yCAAO,IAAM,SAAS,UAAU;;QAClC;gCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAEjE,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAS,WAAW,WAAW,YAAY;gCAChD,WAAW,WAAW,WAAW,iBAAiB;0CAEjD,WAAW,WAAW,QAAQ;;;;;;;;;;;;kCAGnC,6LAAC;wBAAE,WAAU;kCAAiD;;;;;;kCAC9D,6LAAC;wBAAE,WAAU;kCAAwD;;;;;;;;;;;;;;;;;;;;;;AAK/E;IAtDS;MAAA;AAwDF,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA;YACE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA;YACE,oBAAM,6LAAC,kNAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,WAAW;QACf;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,SAAS,sBACxB,6LAAC;4BAEC,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,KAAK;4BACpB,aAAa,QAAQ,WAAW;4BAChC,OAAO,QAAQ,KAAK;4BACpB,OAAO,QAAQ,KAAK;4BACpB,UAAU,QAAQ,QAAQ;2BANrB;;;;;;;;;;8BAYX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAK9D,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,6LAAC;oCAEC,MAAM,MAAM,IAAI;oCAChB,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,MAAM;oCACpB,aAAa,MAAM,WAAW;oCAC9B,OAAO,QAAQ;mCALV;;;;;;;;;;sCAWX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAkE;;;;;;;;;;;;kDAIpF,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,kNAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD;IA7JgB;;QACA,iIAAA,CAAA,iBAAc;;;MADd", "debugId": null}}, {"offset": {"line": 4655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  AlertCircle,\n  Upload,\n  Plus,\n  Save,\n  Eye,\n  Star,\n  Bell,\n  ArrowRight\n} from 'lucide-react'\n\nexport function CTASection() {\n  const { t } = useTranslation()\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <section \n      ref={ref}\n      className=\"py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-20 h-20 border border-white rounded-full animate-pulse\" />\n        <div className=\"absolute top-32 right-20 w-16 h-16 border border-white rounded-full animate-bounce\" />\n        <div className=\"absolute bottom-20 left-32 w-24 h-24 border border-white rounded-full animate-pulse\" />\n        <div className=\"absolute bottom-32 right-10 w-12 h-12 border border-white rounded-full animate-bounce\" />\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          {/* Main Content */}\n          <div className={`transition-all duration-1000 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            {/* Icon */}\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6\">\n              <GraduationCap className=\"w-10 h-10 text-white\" />\n            </div>\n\n            {/* Title */}\n            <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 arabic-text leading-tight\">\n              {t('home.cta.title')}\n            </h2>\n\n            {/* Subtitle */}\n            <p className=\"text-xl md:text-2xl text-blue-100 mb-4 arabic-text\">\n              {t('home.cta.subtitle')}\n            </p>\n\n            {/* Description */}\n            <p className=\"text-lg text-blue-200 mb-12 max-w-2xl mx-auto arabic-text leading-relaxed\">\n              {t('home.cta.description')}\n            </p>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center gap-2 mb-2\">\n                  <Users className=\"w-6 h-6 text-blue-200\" />\n                  <span className=\"text-3xl font-bold text-white\">1,200+</span>\n                </div>\n                <p className=\"text-blue-200 arabic-text\">طالب راضٍ</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center gap-2 mb-2\">\n                  <Star className=\"w-6 h-6 text-yellow-300\" />\n                  <span className=\"text-3xl font-bold text-white\">4.9</span>\n                </div>\n                <p className=\"text-blue-200 arabic-text\">تقييم العملاء</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center gap-2 mb-2\">\n                  <GraduationCap className=\"w-6 h-6 text-blue-200\" />\n                  <span className=\"text-3xl font-bold text-white\">50+</span>\n                </div>\n                <p className=\"text-blue-200 arabic-text\">مدرسة وجامعة</p>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Button \n                size=\"lg\" \n                className=\"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group\"\n                asChild\n              >\n                <a href=\"/customize\" className=\"flex items-center gap-2\">\n                  <Sparkles className=\"w-5 h-5\" />\n                  {t('home.cta.button')}\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </a>\n              </Button>\n              \n              <Button \n                variant=\"outline\" \n                size=\"lg\" \n                className=\"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold arabic-text transition-all duration-300\"\n                asChild\n              >\n                <a href=\"/contact\" className=\"flex items-center gap-2\">\n                  <MessageSquare className=\"w-5 h-5\" />\n                  {t('home.cta.contact')}\n                </a>\n              </Button>\n            </div>\n          </div>\n\n          {/* Additional Info */}\n          <div className={`mt-16 transition-all duration-1000 delay-500 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n                <div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">✅</div>\n                  <p className=\"text-blue-100 arabic-text\">ضمان الجودة</p>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">🚚</div>\n                  <p className=\"text-blue-100 arabic-text\">توصيل مجاني</p>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">🔄</div>\n                  <p className=\"text-blue-100 arabic-text\">إرجاع مجاني</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-50 dark:from-gray-900 to-transparent\" />\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAgBO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,WAAW,IAAI;wCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;uCACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,OAAO,CAAC,IAAI,OAAO;YAC9B;YAEA;wCAAO,IAAM,SAAS,UAAU;;QAClC;+BAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,CAAC,6BAA6B,EAC5C,YAAY,8BAA8B,4BAC1C;;8CAEA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAc,WAAU;;;;;;;;;;;8CAI3B,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAIL,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAc,WAAU;;;;;;sEACzB,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;8CAK7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC;gDAAE,MAAK;gDAAa,WAAU;;kEAC7B,6LAAC;wDAAS,WAAU;;;;;;oDACnB,EAAE;kEACH,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAI1B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC;gDAAE,MAAK;gDAAW,WAAU;;kEAC3B,6LAAC;wDAAc,WAAU;;;;;;oDACxB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAOX,6LAAC;4BAAI,WAAW,CAAC,6CAA6C,EAC5D,YAAY,8BAA8B,4BAC1C;sCACA,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GA9IgB;;QACA,iIAAA,CAAA,iBAAc;;;KADd", "debugId": null}}, {"offset": {"line": 5128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/page.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { PageLayout } from \"@/components/layouts/PageLayout\"\nimport { HeroSection } from \"@/components/home/<USER>\"\nimport { StatsSection } from \"@/components/home/<USER>\"\nimport { FeaturesShowcase } from \"@/components/home/<USER>\"\nimport { ProductsPreview } from \"@/components/home/<USER>\"\nimport { TestimonialsSection } from \"@/components/home/<USER>\"\nimport { AIFeaturesSection } from \"@/components/home/<USER>\"\nimport { CTASection } from \"@/components/home/<USER>\"\n\nexport default function Home() {\n  return (\n    <PageLayout containerClassName=\"\" showFooter={true}>\n      {/* Hero Section - القسم الرئيسي */}\n      <HeroSection />\n\n      {/* Stats Section - قسم الإحصائيات */}\n      <StatsSection />\n\n      {/* Features Showcase - عرض الميزات */}\n      <FeaturesShowcase />\n\n      {/* Products Preview - معاينة المنتجات */}\n      <ProductsPreview />\n\n      {/* AI Features Section - قسم الذكاء الاصطناعي */}\n      <AIFeaturesSection />\n\n      {/* Testimonials Section - قسم آراء العملاء */}\n      <TestimonialsSection />\n\n      {/* Call to Action Section - قسم الدعوة للعمل */}\n      <CTASection />\n    </PageLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,qBACE,6LAAC,8IAAA,CAAA,aAAU;QAAC,oBAAmB;QAAG,YAAY;;0BAE5C,6LAAC,4IAAA,CAAA,cAAW;;;;;0BAGZ,6LAAC,6IAAA,CAAA,eAAY;;;;;0BAGb,6LAAC,iJAAA,CAAA,mBAAgB;;;;;0BAGjB,6LAAC,gJAAA,CAAA,kBAAe;;;;;0BAGhB,6LAAC,kJAAA,CAAA,oBAAiB;;;;;0BAGlB,6LAAC,oJAAA,CAAA,sBAAmB;;;;;0BAGpB,6LAAC,2IAAA,CAAA,aAAU;;;;;;;;;;;AAGjB;KAzBwB", "debugId": null}}]}