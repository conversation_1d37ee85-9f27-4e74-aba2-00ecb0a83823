(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let a=t[0],n=r[0];if(Array.isArray(a)&&Array.isArray(n)){if(a[0]!==n[0]||a[2]!==n[2])return!0}else if(a!==n)return!0;if(t[4])return!r[4];if(r[4])return!0;let l=Object.values(t[1])[0],o=Object.values(r[1])[0];return!l||!o||e(l,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return n}});let a=r(19169);function n(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,a.parsePath)(e);return r===t||r.startsWith(t+"/")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let a=r(51550),n=r(59656);var l=n._("_maxConcurrency"),o=n._("_runningCount"),s=n._("_queue"),i=n._("_processNext");class c{enqueue(e){let t,r,n=new Promise((e,a)=>{t=e,r=a}),l=async()=>{try{a._(this,o)[o]++;let r=await e();t(r)}catch(e){r(e)}finally{a._(this,o)[o]--,a._(this,i)[i]()}};return a._(this,s)[s].push({promiseFn:n,task:l}),a._(this,i)[i](),n}bump(e){let t=a._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=a._(this,s)[s].splice(t,1)[0];a._(this,s)[s].unshift(e),a._(this,i)[i](!0)}}constructor(e=5){Object.defineProperty(this,i,{value:u}),Object.defineProperty(this,l,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),a._(this,l)[l]=e,a._(this,o)[o]=0,a._(this,s)[s]=[]}}function u(e){if(void 0===e&&(e=!1),(a._(this,o)[o]<a._(this,l)[l]||e)&&a._(this,s)[s].length>0){var t;null==(t=a._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return d}});let a=r(59008),n=r(59154),l=r(75076);function o(e,t,r){let a=e.pathname;return(t&&(a+=e.search),r)?""+r+"%"+a:a}function s(e,t,r){return o(e,t===n.PrefetchKind.FULL,r)}function i(e){let{url:t,nextUrl:r,tree:a,prefetchCache:l,kind:s,allowAliasing:i=!0}=e,c=function(e,t,r,a,l){for(let s of(void 0===t&&(t=n.PrefetchKind.TEMPORARY),[r,null])){let r=o(e,!0,s),i=o(e,!1,s),c=e.search?r:i,u=a.get(c);if(u&&l){if(u.url.pathname===e.pathname&&u.url.search!==e.search)return{...u,aliased:!0};return u}let d=a.get(i);if(l&&e.search&&t!==n.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==n.PrefetchKind.FULL&&l){for(let t of a.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,r,l,i);return c?(c.status=h(c),c.kind!==n.PrefetchKind.FULL&&s===n.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return u({tree:a,url:t,nextUrl:r,prefetchCache:l,kind:null!=s?s:n.PrefetchKind.TEMPORARY})}),s&&c.kind===n.PrefetchKind.TEMPORARY&&(c.kind=s),c):u({tree:a,url:t,nextUrl:r,prefetchCache:l,kind:s||n.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:r,prefetchCache:a,url:l,data:o,kind:i}=e,c=o.couldBeIntercepted?s(l,i,t):s(l,i),u={treeAtTimeOfPrefetch:r,data:Promise.resolve(o),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:o.staleTime,key:c,status:n.PrefetchCacheEntryStatus.fresh,url:l};return a.set(c,u),u}function u(e){let{url:t,kind:r,tree:o,nextUrl:i,prefetchCache:c}=e,u=s(t,r),d=l.prefetchQueue.enqueue(()=>(0,a.fetchServerResponse)(t,{flightRouterState:o,nextUrl:i,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:a,existingCacheKey:n}=e,l=a.get(n);if(!l)return;let o=s(t,l.kind,r);return a.set(o,{...l,key:o}),a.delete(n),o}({url:t,existingCacheKey:u,nextUrl:i,prefetchCache:c})),e.prerendered){let t=c.get(null!=r?r:u);t&&(t.kind=n.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:o,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:u,status:n.PrefetchCacheEntryStatus.fresh,url:t};return c.set(u,f),f}function d(e){for(let[t,r]of e)h(r)===n.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:a,staleTime:l}=e;return -1!==l?Date.now()<r+l?n.PrefetchCacheEntryStatus.fresh:n.PrefetchCacheEntryStatus.stale:Date.now()<(null!=a?a:r)+f?a?n.PrefetchCacheEntryStatus.reusable:n.PrefetchCacheEntryStatus.fresh:t===n.PrefetchKind.AUTO&&Date.now()<r+p?n.PrefetchCacheEntryStatus.stale:t===n.PrefetchKind.FULL&&Date.now()<r+p?n.PrefetchCacheEntryStatus.reusable:n.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return n}});let a=r(96127);function n(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,a.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tM});var a={};r.r(a);var n={};r.r(n),r.d(n,{Z:()=>td});var l=r(60687),o=r(43210),s=r(85814),i=r.n(s),c=r(65773);let u=JSON.parse('{"common":{"loading":"جاري التحميل...","error":"حدث خطأ","success":"تم بنجاح","cancel":"إلغاء","confirm":"تأكيد","save":"حفظ","edit":"تعديل","delete":"حذف","search":"بحث","filter":"تصفية","sort":"ترتيب","next":"التالي","previous":"السابق","close":"إغلاق"},"navigation":{"home":"الرئيسية","catalog":"الكتالوج","customize":"التخصيص","orders":"الطلبات","profile":"الملف الشخصي","dashboard":"لوحة التحكم","login":"تسجيل الدخول","register":"إنشاء حساب","logout":"تسجيل الخروج","trackOrder":"تتبع الطلب","about":"من نحن","contact":"تواصل معنا","cart":"السلة","wishlist":"المفضلة"},"home":{"title":"أول منصة مغربية لأزياء التخرج","subtitle":"منصة ذكية متعددة اللغات لتأجير وبيع أزياء التخرج مع إمكانيات التخصيص والذكاء الاصطناعي","startCustomizing":"ابدأ التخصيص الآن","browseCatalog":"تصفح الكتالوج","hero":{"title":"\uD83C\uDF93 أول منصة مغربية ذكية لأزياء التخرج","subtitle":"اكتشف مجموعة واسعة من أزياء التخرج الأنيقة مع إمكانيات التخصيص المتقدمة والذكاء الاصطناعي","description":"منصة متكاملة تجمع بين التقنيات الحديثة والتصاميم العصرية لتوفير تجربة فريدة في عالم أزياء التخرج","cta":{"primary":"ابدأ رحلتك الآن","secondary":"اكتشف المجموعة","watchDemo":"شاهد العرض التوضيحي"},"badges":{"trusted":"موثوق من قبل +1000 طالب","schools":"+50 مدرسة وجامعة","satisfaction":"رضا العملاء 98%"}},"stats":{"title":"أرقام تتحدث عن نفسها","subtitle":"إنجازاتنا في أرقام","customers":{"number":"1,200+","label":"عميل راضٍ"},"schools":{"number":"50+","label":"مدرسة وجامعة"},"orders":{"number":"2,500+","label":"طلب مكتمل"},"satisfaction":{"number":"98%","label":"معدل الرضا"}},"features":{"title":"ميزات تجعلنا الأفضل","subtitle":"اكتشف ما يميزنا عن الآخرين","customization":{"title":"تخصيص متقدم","description":"اختر الألوان والأنماط والإكسسوارات حسب ذوقك مع معاينة فورية ثلاثية الأبعاد"},"ai":{"title":"ذكاء اصطناعي","description":"مساعد ذكي يقدم اقتراحات مخصصة ويساعدك في اختيار التصميم المثالي"},"roles":{"title":"أدوار متعددة","description":"لوحات تحكم متخصصة للطلاب والمدارس وشركاء التوصيل والإدارة"},"tracking":{"title":"تتبع متقدم","description":"تتبع طلبك بالتفصيل من لحظة الطلب حتى التسليم مع إشعارات فورية"},"quality":{"title":"جودة عالية","description":"أقمشة فاخرة وتصنيع احترافي يضمن مظهراً أنيقاً في يومك المميز"},"support":{"title":"دعم 24/7","description":"فريق دعم متخصص متاح على مدار الساعة لمساعدتك في أي استفسار"}},"products":{"title":"مجموعة مختارة من أفضل التصاميم","subtitle":"اكتشف أحدث صيحات أزياء التخرج","viewAll":"عرض جميع المنتجات","featured":"منتجات مميزة","newArrivals":"وصل حديثاً","popular":"الأكثر طلباً"},"testimonials":{"title":"ماذا يقول عملاؤنا","subtitle":"تجارب حقيقية من طلاب تخرجوا معنا","viewAll":"عرض جميع التقييمات"},"ai":{"title":"قوة الذكاء الاصطناعي في خدمتك","subtitle":"تقنيات متقدمة لتجربة تخصيص فريدة","features":{"assistant":{"title":"مساعد ذكي","description":"احصل على اقتراحات مخصصة بناءً على تفضيلاتك ومناسبتك"},"visualization":{"title":"معاينة ثلاثية الأبعاد","description":"شاهد تصميمك بتقنية ثلاثية الأبعاد قبل الطلب"},"recommendations":{"title":"توصيات ذكية","description":"اقتراحات مبنية على الذكاء الاصطناعي لأفضل التصاميم"},"chat":{"title":"دردشة ذكية","description":"تحدث مع مساعدنا الذكي للحصول على المساعدة الفورية"}}},"cta":{"title":"جاهز لبدء رحلتك؟","subtitle":"انضم إلى آلاف الطلاب الذين اختاروا التميز","description":"احصل على زي التخرج المثالي مع خدماتنا المتميزة","button":"ابدأ الآن","contact":"تحدث معنا"},"languageSupport":"دعم متعدد اللغات","footer":"\xa9 2024 Graduation Toqs - أول منصة مغربية لأزياء التخرج"},"products":{"gown":"ثوب التخرج","cap":"قبعة التخرج","tassel":"شرابة","stole":"وشاح","hood":"غطاء الرأس","colors":"الألوان","sizes":"المقاسات","price":"السعر","rent":"إيجار","buy":"شراء","addToCart":"إضافة للسلة"},"auth":{"login":"تسجيل الدخول","register":"إنشاء حساب جديد","email":"البريد الإلكتروني","password":"كلمة المرور","confirmPassword":"تأكيد كلمة المرور","firstName":"الاسم الأول","lastName":"اسم العائلة","phone":"رقم الهاتف","forgotPassword":"نسيت كلمة المرور؟","resetPassword":"إعادة تعيين كلمة المرور","newPassword":"كلمة المرور الجديدة","rememberMe":"تذكرني","alreadyHaveAccount":"لديك حساب بالفعل؟","dontHaveAccount":"ليس لديك حساب؟","sendResetLink":"إرسال رابط إعادة التعيين","resetLinkSent":"تم إرسال رابط إعادة التعيين","backToLogin":"العودة لتسجيل الدخول","logout":"تسجيل الخروج"}}'),d={ar:u,fr:JSON.parse('{"common":{"loading":"Chargement...","error":"Une erreur s\'est produite","success":"Succ\xe8s","cancel":"Annuler","confirm":"Confirmer","save":"Enregistrer","edit":"Modifier","delete":"Supprimer","search":"Rechercher","filter":"Filtrer","sort":"Trier","next":"Suivant","previous":"Pr\xe9c\xe9dent","close":"Fermer"},"navigation":{"home":"Accueil","catalog":"Catalogue","customize":"Personnaliser","orders":"Commandes","profile":"Profil","dashboard":"Tableau de bord","login":"Connexion","register":"S\'inscrire","logout":"D\xe9connexion","trackOrder":"Suivre la commande","about":"\xc0 propos","contact":"Contactez-nous","cart":"Panier","wishlist":"Liste de souhaits"},"home":{"title":"Premi\xe8re plateforme marocaine de tenues de remise de dipl\xf4mes","subtitle":"Plateforme intelligente multilingue pour la location et la vente de tenues de remise de dipl\xf4mes avec personnalisation et IA","startCustomizing":"Commencer la personnalisation","browseCatalog":"Parcourir le catalogue","features":{"customization":{"title":"Personnalisation des tenues","description":"Choisissez les couleurs, styles et accessoires selon vos go\xfbts"},"ai":{"title":"Intelligence artificielle","description":"Assistant intelligent et suggestions personnalis\xe9es"},"roles":{"title":"R\xf4les multiples","description":"Tableaux de bord pour \xe9tudiants, \xe9coles et administration"},"tracking":{"title":"Suivi des commandes","description":"Suivez votre commande de la commande \xe0 la livraison"}},"languageSupport":"Support multilingue","footer":"\xa9 2024 Graduation Toqs - Premi\xe8re plateforme marocaine de tenues de remise de dipl\xf4mes"},"products":{"gown":"Toge de remise de dipl\xf4mes","cap":"Toque","tassel":"Gland","stole":"\xc9tole","hood":"Capuche","colors":"Couleurs","sizes":"Tailles","price":"Prix","rent":"Louer","buy":"Acheter","addToCart":"Ajouter au panier"},"auth":{"login":"Connexion","register":"Cr\xe9er un compte","email":"Email","password":"Mot de passe","confirmPassword":"Confirmer le mot de passe","firstName":"Pr\xe9nom","lastName":"Nom de famille","phone":"Num\xe9ro de t\xe9l\xe9phone","forgotPassword":"Mot de passe oubli\xe9 ?","resetPassword":"R\xe9initialiser le mot de passe","newPassword":"Nouveau mot de passe","rememberMe":"Se souvenir de moi","alreadyHaveAccount":"Vous avez d\xe9j\xe0 un compte ?","dontHaveAccount":"Vous n\'avez pas de compte ?","sendResetLink":"Envoyer le lien de r\xe9initialisation","resetLinkSent":"Lien de r\xe9initialisation envoy\xe9","backToLogin":"Retour \xe0 la connexion","logout":"Se d\xe9connecter"}}'),en:JSON.parse('{"common":{"loading":"Loading...","error":"An error occurred","success":"Success","cancel":"Cancel","confirm":"Confirm","save":"Save","edit":"Edit","delete":"Delete","search":"Search","filter":"Filter","sort":"Sort","next":"Next","previous":"Previous","close":"Close"},"navigation":{"home":"Home","catalog":"Catalog","customize":"Customize","orders":"Orders","profile":"Profile","dashboard":"Dashboard","login":"Login","register":"Register","logout":"Logout","trackOrder":"Track Order","about":"About Us","contact":"Contact Us","cart":"Cart","wishlist":"Wishlist"},"home":{"title":"First Moroccan Graduation Attire Platform","subtitle":"Smart multilingual platform for graduation attire rental and sales with customization and AI features","startCustomizing":"Start Customizing Now","browseCatalog":"Browse Catalog","features":{"customization":{"title":"Attire Customization","description":"Choose colors, styles, and accessories according to your taste"},"ai":{"title":"Artificial Intelligence","description":"Smart assistant and personalized suggestions"},"roles":{"title":"Multiple Roles","description":"Dashboards for students, schools, and administration"},"tracking":{"title":"Order Tracking","description":"Track your order from placement to delivery"}},"languageSupport":"Multilingual Support","footer":"\xa9 2024 Graduation Toqs - First Moroccan Graduation Attire Platform"},"products":{"gown":"Graduation Gown","cap":"Graduation Cap","tassel":"Tassel","stole":"Stole","hood":"Hood","colors":"Colors","sizes":"Sizes","price":"Price","rent":"Rent","buy":"Buy","addToCart":"Add to Cart"},"auth":{"login":"Login","register":"Create Account","email":"Email","password":"Password","confirmPassword":"Confirm Password","firstName":"First Name","lastName":"Last Name","phone":"Phone Number","forgotPassword":"Forgot Password?","resetPassword":"Reset Password","newPassword":"New Password","rememberMe":"Remember Me","alreadyHaveAccount":"Already have an account?","dontHaveAccount":"Don\'t have an account?","sendResetLink":"Send Reset Link","resetLinkSent":"Reset Link Sent","backToLogin":"Back to Login","logout":"Logout"}}')};function f(){let[e,t]=(0,o.useState)("ar");return{locale:e,changeLocale:e=>{t(e),localStorage.setItem("locale",e),document.documentElement.lang=e,document.documentElement.dir="ar"===e?"rtl":"ltr"},t:t=>{let r=t.split("."),a=d[e];for(let e of r)a=a?.[e];return a||t}}}var p=r(28253),h=r(86748);function m({isMobile:e=!1}){let t=Array.from({length:5},(e,t)=>t),r=["75%","85%","65%","80%","70%"],a=["60px","75px","55px","70px","65px"];return e?(0,l.jsx)("div",{className:"flex flex-col gap-1 mb-6",children:t.map(e=>(0,l.jsxs)("div",{className:"flex items-center gap-3 px-4 py-3 mx-2 rounded-xl animate-pulse",style:{animationDelay:`${100*e}ms`},children:[(0,l.jsx)("div",{className:"w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer"}),(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsx)("div",{className:"h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer",style:{width:r[e]}})})]},e))}):(0,l.jsx)("nav",{className:"hidden lg:flex items-center gap-1",children:t.map(e=>(0,l.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2.5 rounded-xl animate-pulse",style:{animationDelay:`${100*e}ms`},children:[(0,l.jsx)("div",{className:"w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer"}),(0,l.jsx)("div",{className:"h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer",style:{width:a[e]}})]},e))})}function b(){return(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse"}),(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse",style:{animationDelay:"100ms"}}),(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse",style:{animationDelay:"200ms"}}),(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse",style:{animationDelay:"300ms"}}),(0,l.jsx)("div",{className:"w-16 h-8 bg-gray-300 dark:bg-gray-600 rounded shimmer animate-pulse",style:{animationDelay:"400ms"}}),(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse",style:{animationDelay:"500ms"}})]})}function g(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var x=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...a}=e;if(o.isValidElement(r)){var n;let e,l,s=(n=r,(l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(l=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),i=function(e,t){let r={...t};for(let a in t){let n=e[a],l=t[a];/^on[A-Z]/.test(a)?n&&l?r[a]=(...e)=>{let t=l(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...l}:"className"===a&&(r[a]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==o.Fragment&&(i.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=g(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():g(e[t],null)}}}}(t,s):s),o.cloneElement(r,i)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:a,...n}=e,s=o.Children.toArray(a),i=s.find(v);if(i){let e=i.props.children,a=s.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...n,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,a):null})}return(0,l.jsx)(t,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}("Slot"),y=Symbol("radix.slottable");function v(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}function w(){for(var e,t,r=0,a="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,a,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(a=e(t[r]))&&(n&&(n+=" "),n+=a)}else for(a in t)t[a]&&(n&&(n+=" "),n+=a);return n}(e))&&(a&&(a+=" "),a+=t);return a}let j=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,k=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return w(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:l}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let o=j(t)||j(a);return n[e][o]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return w(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...s}[t]):({...l,...s})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)},_=e=>{let t=M(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),N(r,t)||R(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&a[e]?[...n,...a[e]]:n}}},N=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],a=t.nextPart.get(r),n=a?N(e.slice(1),a):void 0;if(n)return n;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},P=/^\[(.+)\]$/,R=e=>{if(P.test(e)){let t=P.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},M=e=>{let{theme:t,classGroups:r}=e,a={nextPart:new Map,validators:[]};for(let e in r)E(r[e],a,e,t);return a},E=(e,t,r,a)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:T(t,e)).classGroupId=r;return}if("function"==typeof e)return O(e)?void E(e(a),t,r,a):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{E(n,T(t,e),r,a)})})},T=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},O=e=>e.isThemeGetter,C=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,a=new Map,n=(n,l)=>{r.set(n,l),++t>e&&(t=0,a=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=a.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},S=e=>{let{prefix:t,experimentalParseClassName:r}=e,a=e=>{let t,r=[],a=0,n=0,l=0;for(let o=0;o<e.length;o++){let s=e[o];if(0===a&&0===n){if(":"===s){r.push(e.slice(l,o)),l=o+1;continue}if("/"===s){t=o;continue}}"["===s?a++:"]"===s?a--:"("===s?n++:")"===s&&n--}let o=0===r.length?e:e.substring(l),s=A(o);return{modifiers:r,hasImportantModifier:s!==o,baseClassName:s,maybePostfixModifierPosition:t&&t>l?t-l:void 0}};if(t){let e=t+":",r=a;a=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=a;a=t=>r({className:t,parseClassName:e})}return a},A=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,L=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],a=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...a.sort(),e),a=[]):a.push(e)}),r.push(...a.sort()),r}},z=e=>({cache:C(e.cacheSize),parseClassName:S(e),sortModifiers:L(e),..._(e)}),U=/\s+/,D=(e,t)=>{let{parseClassName:r,getClassGroupId:a,getConflictingClassGroupIds:n,sortModifiers:l}=t,o=[],s=e.trim().split(U),i="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(c){i=t+(i.length>0?" "+i:i);continue}let h=!!p,m=a(h?f.substring(0,p):f);if(!m){if(!h||!(m=a(f))){i=t+(i.length>0?" "+i:i);continue}h=!1}let b=l(u).join(":"),g=d?b+"!":b,x=g+m;if(o.includes(x))continue;o.push(x);let y=n(m,h);for(let e=0;e<y.length;++e){let t=y[e];o.push(g+t)}i=t+(i.length>0?" "+i:i)}return i};function I(){let e,t,r=0,a="";for(;r<arguments.length;)(e=arguments[r++])&&(t=H(e))&&(a&&(a+=" "),a+=t);return a}let H=e=>{let t;if("string"==typeof e)return e;let r="";for(let a=0;a<e.length;a++)e[a]&&(t=H(e[a]))&&(r&&(r+=" "),r+=t);return r},F=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},q=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,G=/^\((?:(\w[\w-]*):)?(.+)\)$/i,K=/^\d+\/\d+$/,B=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,V=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,$=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,W=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Y=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,X=e=>K.test(e),J=e=>!!e&&!Number.isNaN(Number(e)),Q=e=>!!e&&Number.isInteger(Number(e)),Z=e=>e.endsWith("%")&&J(e.slice(0,-1)),ee=e=>B.test(e),et=()=>!0,er=e=>V.test(e)&&!$.test(e),ea=()=>!1,en=e=>W.test(e),el=e=>Y.test(e),eo=e=>!ei(e)&&!eh(e),es=e=>ew(e,eN,ea),ei=e=>q.test(e),ec=e=>ew(e,eP,er),eu=e=>ew(e,eR,J),ed=e=>ew(e,ek,ea),ef=e=>ew(e,e_,el),ep=e=>ew(e,eE,en),eh=e=>G.test(e),em=e=>ej(e,eP),eb=e=>ej(e,eM),eg=e=>ej(e,ek),ex=e=>ej(e,eN),ey=e=>ej(e,e_),ev=e=>ej(e,eE,!0),ew=(e,t,r)=>{let a=q.exec(e);return!!a&&(a[1]?t(a[1]):r(a[2]))},ej=(e,t,r=!1)=>{let a=G.exec(e);return!!a&&(a[1]?t(a[1]):r)},ek=e=>"position"===e||"percentage"===e,e_=e=>"image"===e||"url"===e,eN=e=>"length"===e||"size"===e||"bg-size"===e,eP=e=>"length"===e,eR=e=>"number"===e,eM=e=>"family-name"===e,eE=e=>"shadow"===e;Symbol.toStringTag;let eT=function(e,...t){let r,a,n,l=function(s){return a=(r=z(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,l=o,o(s)};function o(e){let t=a(e);if(t)return t;let l=D(e,r);return n(e,l),l}return function(){return l(I.apply(null,arguments))}}(()=>{let e=F("color"),t=F("font"),r=F("text"),a=F("font-weight"),n=F("tracking"),l=F("leading"),o=F("breakpoint"),s=F("container"),i=F("spacing"),c=F("radius"),u=F("shadow"),d=F("inset-shadow"),f=F("text-shadow"),p=F("drop-shadow"),h=F("blur"),m=F("perspective"),b=F("aspect"),g=F("ease"),x=F("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...v(),eh,ei],j=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],_=()=>[eh,ei,i],N=()=>[X,"full","auto",..._()],P=()=>[Q,"none","subgrid",eh,ei],R=()=>["auto",{span:["full",Q,eh,ei]},Q,eh,ei],M=()=>[Q,"auto",eh,ei],E=()=>["auto","min","max","fr",eh,ei],T=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],C=()=>["auto",..._()],S=()=>[X,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",..._()],A=()=>[e,eh,ei],L=()=>[...v(),eg,ed,{position:[eh,ei]}],z=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",ex,es,{size:[eh,ei]}],D=()=>[Z,em,ec],I=()=>["","none","full",c,eh,ei],H=()=>["",J,em,ec],q=()=>["solid","dashed","dotted","double"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],K=()=>[J,Z,eg,ed],B=()=>["","none",h,eh,ei],V=()=>["none",J,eh,ei],$=()=>["none",J,eh,ei],W=()=>[J,eh,ei],Y=()=>[X,"full",..._()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ee],breakpoint:[ee],color:[et],container:[ee],"drop-shadow":[ee],ease:["in","out","in-out"],font:[eo],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ee],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ee],shadow:[ee],spacing:["px",J],text:[ee],"text-shadow":[ee],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",X,ei,eh,b]}],container:["container"],columns:[{columns:[J,ei,eh,s]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:N()}],"inset-x":[{"inset-x":N()}],"inset-y":[{"inset-y":N()}],start:[{start:N()}],end:[{end:N()}],top:[{top:N()}],right:[{right:N()}],bottom:[{bottom:N()}],left:[{left:N()}],visibility:["visible","invisible","collapse"],z:[{z:[Q,"auto",eh,ei]}],basis:[{basis:[X,"full","auto",s,..._()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[J,X,"auto","initial","none",ei]}],grow:[{grow:["",J,eh,ei]}],shrink:[{shrink:["",J,eh,ei]}],order:[{order:[Q,"first","last","none",eh,ei]}],"grid-cols":[{"grid-cols":P()}],"col-start-end":[{col:R()}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":P()}],"row-start-end":[{row:R()}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:_()}],"gap-x":[{"gap-x":_()}],"gap-y":[{"gap-y":_()}],"justify-content":[{justify:[...T(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...T()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":T()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:_()}],px:[{px:_()}],py:[{py:_()}],ps:[{ps:_()}],pe:[{pe:_()}],pt:[{pt:_()}],pr:[{pr:_()}],pb:[{pb:_()}],pl:[{pl:_()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":_()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":_()}],"space-y-reverse":["space-y-reverse"],size:[{size:S()}],w:[{w:[s,"screen",...S()]}],"min-w":[{"min-w":[s,"screen","none",...S()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[o]},...S()]}],h:[{h:["screen","lh",...S()]}],"min-h":[{"min-h":["screen","lh","none",...S()]}],"max-h":[{"max-h":["screen","lh",...S()]}],"font-size":[{text:["base",r,em,ec]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[a,eh,eu]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Z,ei]}],"font-family":[{font:[eb,ei,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,eh,ei]}],"line-clamp":[{"line-clamp":[J,"none",eh,eu]}],leading:[{leading:[l,..._()]}],"list-image":[{"list-image":["none",eh,ei]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",eh,ei]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:A()}],"text-color":[{text:A()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[J,"from-font","auto",eh,ec]}],"text-decoration-color":[{decoration:A()}],"underline-offset":[{"underline-offset":[J,"auto",eh,ei]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",eh,ei]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",eh,ei]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:L()}],"bg-repeat":[{bg:z()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Q,eh,ei],radial:["",eh,ei],conic:[Q,eh,ei]},ey,ef]}],"bg-color":[{bg:A()}],"gradient-from-pos":[{from:D()}],"gradient-via-pos":[{via:D()}],"gradient-to-pos":[{to:D()}],"gradient-from":[{from:A()}],"gradient-via":[{via:A()}],"gradient-to":[{to:A()}],rounded:[{rounded:I()}],"rounded-s":[{"rounded-s":I()}],"rounded-e":[{"rounded-e":I()}],"rounded-t":[{"rounded-t":I()}],"rounded-r":[{"rounded-r":I()}],"rounded-b":[{"rounded-b":I()}],"rounded-l":[{"rounded-l":I()}],"rounded-ss":[{"rounded-ss":I()}],"rounded-se":[{"rounded-se":I()}],"rounded-ee":[{"rounded-ee":I()}],"rounded-es":[{"rounded-es":I()}],"rounded-tl":[{"rounded-tl":I()}],"rounded-tr":[{"rounded-tr":I()}],"rounded-br":[{"rounded-br":I()}],"rounded-bl":[{"rounded-bl":I()}],"border-w":[{border:H()}],"border-w-x":[{"border-x":H()}],"border-w-y":[{"border-y":H()}],"border-w-s":[{"border-s":H()}],"border-w-e":[{"border-e":H()}],"border-w-t":[{"border-t":H()}],"border-w-r":[{"border-r":H()}],"border-w-b":[{"border-b":H()}],"border-w-l":[{"border-l":H()}],"divide-x":[{"divide-x":H()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":H()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:A()}],"border-color-x":[{"border-x":A()}],"border-color-y":[{"border-y":A()}],"border-color-s":[{"border-s":A()}],"border-color-e":[{"border-e":A()}],"border-color-t":[{"border-t":A()}],"border-color-r":[{"border-r":A()}],"border-color-b":[{"border-b":A()}],"border-color-l":[{"border-l":A()}],"divide-color":[{divide:A()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[J,eh,ei]}],"outline-w":[{outline:["",J,em,ec]}],"outline-color":[{outline:A()}],shadow:[{shadow:["","none",u,ev,ep]}],"shadow-color":[{shadow:A()}],"inset-shadow":[{"inset-shadow":["none",d,ev,ep]}],"inset-shadow-color":[{"inset-shadow":A()}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:A()}],"ring-offset-w":[{"ring-offset":[J,ec]}],"ring-offset-color":[{"ring-offset":A()}],"inset-ring-w":[{"inset-ring":H()}],"inset-ring-color":[{"inset-ring":A()}],"text-shadow":[{"text-shadow":["none",f,ev,ep]}],"text-shadow-color":[{"text-shadow":A()}],opacity:[{opacity:[J,eh,ei]}],"mix-blend":[{"mix-blend":[...G(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":G()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[J]}],"mask-image-linear-from-pos":[{"mask-linear-from":K()}],"mask-image-linear-to-pos":[{"mask-linear-to":K()}],"mask-image-linear-from-color":[{"mask-linear-from":A()}],"mask-image-linear-to-color":[{"mask-linear-to":A()}],"mask-image-t-from-pos":[{"mask-t-from":K()}],"mask-image-t-to-pos":[{"mask-t-to":K()}],"mask-image-t-from-color":[{"mask-t-from":A()}],"mask-image-t-to-color":[{"mask-t-to":A()}],"mask-image-r-from-pos":[{"mask-r-from":K()}],"mask-image-r-to-pos":[{"mask-r-to":K()}],"mask-image-r-from-color":[{"mask-r-from":A()}],"mask-image-r-to-color":[{"mask-r-to":A()}],"mask-image-b-from-pos":[{"mask-b-from":K()}],"mask-image-b-to-pos":[{"mask-b-to":K()}],"mask-image-b-from-color":[{"mask-b-from":A()}],"mask-image-b-to-color":[{"mask-b-to":A()}],"mask-image-l-from-pos":[{"mask-l-from":K()}],"mask-image-l-to-pos":[{"mask-l-to":K()}],"mask-image-l-from-color":[{"mask-l-from":A()}],"mask-image-l-to-color":[{"mask-l-to":A()}],"mask-image-x-from-pos":[{"mask-x-from":K()}],"mask-image-x-to-pos":[{"mask-x-to":K()}],"mask-image-x-from-color":[{"mask-x-from":A()}],"mask-image-x-to-color":[{"mask-x-to":A()}],"mask-image-y-from-pos":[{"mask-y-from":K()}],"mask-image-y-to-pos":[{"mask-y-to":K()}],"mask-image-y-from-color":[{"mask-y-from":A()}],"mask-image-y-to-color":[{"mask-y-to":A()}],"mask-image-radial":[{"mask-radial":[eh,ei]}],"mask-image-radial-from-pos":[{"mask-radial-from":K()}],"mask-image-radial-to-pos":[{"mask-radial-to":K()}],"mask-image-radial-from-color":[{"mask-radial-from":A()}],"mask-image-radial-to-color":[{"mask-radial-to":A()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":v()}],"mask-image-conic-pos":[{"mask-conic":[J]}],"mask-image-conic-from-pos":[{"mask-conic-from":K()}],"mask-image-conic-to-pos":[{"mask-conic-to":K()}],"mask-image-conic-from-color":[{"mask-conic-from":A()}],"mask-image-conic-to-color":[{"mask-conic-to":A()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:L()}],"mask-repeat":[{mask:z()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",eh,ei]}],filter:[{filter:["","none",eh,ei]}],blur:[{blur:B()}],brightness:[{brightness:[J,eh,ei]}],contrast:[{contrast:[J,eh,ei]}],"drop-shadow":[{"drop-shadow":["","none",p,ev,ep]}],"drop-shadow-color":[{"drop-shadow":A()}],grayscale:[{grayscale:["",J,eh,ei]}],"hue-rotate":[{"hue-rotate":[J,eh,ei]}],invert:[{invert:["",J,eh,ei]}],saturate:[{saturate:[J,eh,ei]}],sepia:[{sepia:["",J,eh,ei]}],"backdrop-filter":[{"backdrop-filter":["","none",eh,ei]}],"backdrop-blur":[{"backdrop-blur":B()}],"backdrop-brightness":[{"backdrop-brightness":[J,eh,ei]}],"backdrop-contrast":[{"backdrop-contrast":[J,eh,ei]}],"backdrop-grayscale":[{"backdrop-grayscale":["",J,eh,ei]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[J,eh,ei]}],"backdrop-invert":[{"backdrop-invert":["",J,eh,ei]}],"backdrop-opacity":[{"backdrop-opacity":[J,eh,ei]}],"backdrop-saturate":[{"backdrop-saturate":[J,eh,ei]}],"backdrop-sepia":[{"backdrop-sepia":["",J,eh,ei]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":_()}],"border-spacing-x":[{"border-spacing-x":_()}],"border-spacing-y":[{"border-spacing-y":_()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",eh,ei]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[J,"initial",eh,ei]}],ease:[{ease:["linear","initial",g,eh,ei]}],delay:[{delay:[J,eh,ei]}],animate:[{animate:["none",x,eh,ei]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,eh,ei]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:V()}],"rotate-x":[{"rotate-x":V()}],"rotate-y":[{"rotate-y":V()}],"rotate-z":[{"rotate-z":V()}],scale:[{scale:$()}],"scale-x":[{"scale-x":$()}],"scale-y":[{"scale-y":$()}],"scale-z":[{"scale-z":$()}],"scale-3d":["scale-3d"],skew:[{skew:W()}],"skew-x":[{"skew-x":W()}],"skew-y":[{"skew-y":W()}],transform:[{transform:[eh,ei,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Y()}],"translate-x":[{"translate-x":Y()}],"translate-y":[{"translate-y":Y()}],"translate-z":[{"translate-z":Y()}],"translate-none":["translate-none"],accent:[{accent:A()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:A()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",eh,ei]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",eh,ei]}],fill:[{fill:["none",...A()]}],"stroke-w":[{stroke:[J,em,ec,eu]}],stroke:[{stroke:["none",...A()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eO(...e){return eT(w(e))}let eC=k("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function eS({className:e,variant:t,size:r,asChild:a=!1,...n}){return(0,l.jsx)(a?x:"button",{"data-slot":"button",className:eO(eC({variant:t,size:r,className:e})),...n})}var eA=r(43984),eL=r(30917);r(63213),r(53881);var ez=r(30481);let eU=k("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function eD({className:e,variant:t,asChild:r=!1,...a}){return(0,l.jsx)(r?x:"span",{"data-slot":"badge",className:eO(eU({variant:t}),e),...a})}let eI=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eH=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),eF=e=>{let t=eH(e);return t.charAt(0).toUpperCase()+t.slice(1)},eq=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),eG=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var eK={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let eB=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:l,iconNode:s,...i},c)=>(0,o.createElement)("svg",{ref:c,...eK,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:eq("lucide",n),...!l&&!eG(i)&&{"aria-hidden":"true"},...i},[...s.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(l)?l:[l]])),eV=(e,t)=>{let r=(0,o.forwardRef)(({className:r,...a},n)=>(0,o.createElement)(eB,{ref:n,iconNode:t,className:eq(`lucide-${eI(eF(e))}`,`lucide-${e}`,r),...a}));return r.displayName=eF(e),r},e$=eV("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),eW=eV("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]]),eY=eV("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),eX=eV("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),eJ=eV("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),eQ=eV("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),eZ=eV("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),e0=eV("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),e1=eV("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),e2=eV("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),e3=eV("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]),e4=eV("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),e5=eV("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),e6=eV("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),e9=eV("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),e7=eV("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),e8=eV("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);function te(){let{t:e,locale:t}=f(),{cartCount:r,wishlistCount:n}=(0,p._)(),{menuItems:s,loading:u}=(0,h.b)(),d=(0,c.usePathname)(),[g,x]=(0,o.useState)(!1),[y,v]=(0,o.useState)(!1),w=(0,o.useMemo)(()=>s&&0!==s.length?s.filter(e=>!e.parent_id&&e.is_active).sort((e,t)=>e.order_index-t.order_index).map(e=>{let r=e.title_ar;"en"===t&&e.title_en?r=e.title_en:"fr"===t&&e.title_fr&&(r=e.title_fr);let a=e.target_value;"page"===e.target_type&&(a=`/pages/${e.target_value}`);let n={Home:(0,l.jsx)(e$,{className:"h-4 w-4"}),ShoppingBag:(0,l.jsx)(eW,{className:"h-4 w-4"}),Palette:(0,l.jsx)(eY,{className:"h-4 w-4"}),Search:(0,l.jsx)(eX,{className:"h-4 w-4"}),Info:(0,l.jsx)(eJ,{className:"h-4 w-4"}),Phone:(0,l.jsx)(eQ,{className:"h-4 w-4"}),Grid3X3:(0,l.jsx)(eZ,{className:"h-4 w-4"}),ExternalLink:(0,l.jsx)(e0,{className:"h-4 w-4"}),FileText:(0,l.jsx)(e1,{className:"h-4 w-4"}),Settings:(0,l.jsx)(e2,{className:"h-4 w-4"}),Package:(0,l.jsx)(e3,{className:"h-4 w-4"}),Calendar:(0,l.jsx)(e4,{className:"h-4 w-4"}),ShoppingCart:(0,l.jsx)(e5,{className:"h-4 w-4"})},o=e.icon&&n[e.icon]?n[e.icon]:(0,l.jsx)(e6,{className:"h-4 w-4"}),i=s.filter(t=>t.parent_id===e.id&&t.is_active).sort((e,t)=>e.order_index-t.order_index).map(e=>{let r=e.title_ar;"en"===t&&e.title_en?r=e.title_en:"fr"===t&&e.title_fr&&(r=e.title_fr);let a=e.target_value;return"page"===e.target_type&&(a=`/pages/${e.target_value}`),{id:e.id,href:a,label:r,target_type:e.target_type}});return{id:e.id,href:a,label:r,icon:o,target_type:e.target_type,subItems:i.length>0?i:void 0}}):[],[s,t]),j=(0,o.useMemo)(()=>[{id:"default-home",href:"/",label:e("navigation.home"),icon:(0,l.jsx)(e$,{className:"h-4 w-4"}),target_type:"internal"},{id:"default-catalog",href:"/catalog",label:e("navigation.catalog"),icon:(0,l.jsx)(eW,{className:"h-4 w-4"}),target_type:"internal"},{id:"default-about",href:"/about",label:e("navigation.about")||"من نحن",icon:(0,l.jsx)(eJ,{className:"h-4 w-4"}),target_type:"internal"},{id:"default-contact",href:"/contact",label:e("navigation.contact")||"تواصل معنا",icon:(0,l.jsx)(eQ,{className:"h-4 w-4"}),target_type:"internal"}],[e]),k=(0,o.useMemo)(()=>u?[]:s.length>0?w:j,[u,s.length,w,j]),_=(0,o.useCallback)(e=>"/"===e?"/"===d:d.startsWith(e),[d]),N=(0,o.useCallback)(()=>{x(e=>!e)},[]),P=(0,o.useCallback)(()=>{x(!1)},[]);return(0,l.jsx)("header",{className:"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300",children:(0,l.jsxs)("div",{className:"mobile-container",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center py-3 min-h-[60px]",children:[(0,l.jsxs)(i(),{href:"/",className:"flex items-center gap-2 sm:gap-3 hover:opacity-80 transition-all duration-300 group touch-target",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(e9,{className:"h-8 w-8 sm:h-9 sm:w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"}),(0,l.jsx)("div",{className:"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"text-lg sm:text-xl font-bold text-gray-900 dark:text-white leading-tight",children:"Graduation Toqs"}),(0,l.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium hidden sm:block",children:"ar"===t?"منصة أزياء التخرج":"fr"===t?"Plateforme de Remise des Dipl\xf4mes":"Graduation Platform"})]})]}),(0,l.jsx)("div",{className:"nav-container",children:u||y?(0,l.jsx)(m,{}):(0,l.jsx)("nav",{className:"hidden lg:flex items-center gap-1",role:"navigation","aria-label":"القائمة الرئيسية",children:k.map((e,t)=>{let r="external"===e.target_type;if(e.subItems&&e.subItems.length>0)return(0,l.jsxs)("div",{className:`relative group nav-item-enter nav-stagger-${Math.min(t+1,6)}`,children:[(0,l.jsxs)("button",{className:`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${_(e.href)?"bg-blue-600 text-white shadow-lg shadow-blue-600/25":"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"}`,"aria-label":`${e.label} - قائمة فرعية`,"aria-haspopup":"true","aria-expanded":"false",children:[(0,l.jsx)("span",{className:`transition-transform duration-300 ${_(e.href)?"scale-110":"group-hover:scale-110"}`,children:e.icon}),(0,l.jsx)("span",{className:"text-sm font-medium",children:e.label}),(0,l.jsx)(e7,{className:"h-3 w-3 transition-transform group-hover:rotate-180"})]}),(0,l.jsx)("div",{className:"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",role:"menu","aria-label":`قائمة ${e.label} الفرعية`,children:(0,l.jsx)("div",{className:"py-2",children:e.subItems?.map(e=>{let t="external"===e.target_type,r=t?{href:e.href,target:"_blank",rel:"noopener noreferrer"}:{href:e.href};return(0,l.jsxs)(i(),{...r,className:"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:[e.label,t&&(0,l.jsx)(e0,{className:"h-3 w-3 opacity-60"})]},e.id)})})})]},e.id);let a=r?{href:e.href,target:"_blank",rel:"noopener noreferrer"}:{href:e.href};return(0,l.jsxs)(i(),{...a,className:`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 nav-item-enter nav-stagger-${Math.min(t+1,6)} ${_(e.href)?"bg-blue-600 text-white shadow-lg shadow-blue-600/25":"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"}`,children:[(0,l.jsx)("span",{className:`transition-transform duration-300 ${_(e.href)?"scale-110":"group-hover:scale-110"}`,children:e.icon}),(0,l.jsx)("span",{className:"text-sm font-medium",children:e.label}),r&&(0,l.jsx)(e0,{className:"h-3 w-3 opacity-60"}),_(e.href)&&(0,l.jsx)("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"})]},e.id)})})}),u||y?(0,l.jsx)(b,{}):(0,l.jsxs)("div",{className:"hidden lg:flex items-center gap-2 xl:gap-3",children:[(0,l.jsx)(eS,{variant:"ghost",size:"sm",className:"relative group touch-target",asChild:!0,children:(0,l.jsxs)(i(),{href:"/wishlist",children:[(0,l.jsx)(e8,{className:"h-5 w-5 transition-colors group-hover:text-red-500"}),n>0&&(0,l.jsx)(eD,{variant:"destructive",className:"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse",children:n>99?"99+":n})]})}),(0,l.jsx)(eS,{variant:"ghost",size:"sm",className:"relative group touch-target",asChild:!0,children:(0,l.jsxs)(i(),{href:"/cart",children:[(0,l.jsx)(eW,{className:"h-5 w-5 transition-colors group-hover:text-blue-600"}),r>0&&(0,l.jsx)(eD,{className:"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse",children:r>99?"99+":r})]})}),(0,l.jsx)(ez.NotificationDropdown,{}),(0,l.jsx)("div",{className:"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(eL.J,{}),(0,l.jsx)(eA.ThemeToggle,{})]}),(0,l.jsx)(a.UserMenu,{})]}),(0,l.jsxs)("div",{className:"flex lg:hidden items-center gap-1 sm:gap-2",children:[(0,l.jsx)(eS,{variant:"ghost",size:"sm",className:"relative touch-target mobile-button",asChild:!0,children:(0,l.jsxs)(i(),{href:"/cart",children:[(0,l.jsx)(eW,{className:"h-5 w-5"}),r>0&&(0,l.jsx)(eD,{className:"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600",children:r>9?"9+":r})]})}),(0,l.jsx)(eS,{variant:"ghost",size:"icon",className:"relative touch-target mobile-button min-h-[48px] min-w-[48px]",onClick:N,"aria-label":g?"إغلاق القائمة":"فتح القائمة",children:(0,l.jsxs)("div",{className:"relative w-6 h-6 flex items-center justify-center",children:[(0,l.jsx)("span",{className:`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${g?"rotate-45":"-translate-y-1.5"}`}),(0,l.jsx)("span",{className:`absolute h-0.5 w-6 bg-current transition-all duration-300 ${g?"opacity-0":"opacity-100"}`}),(0,l.jsx)("span",{className:`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${g?"-rotate-45":"translate-y-1.5"}`})]})})]})]}),(0,l.jsx)("div",{className:`lg:hidden overflow-hidden transition-all duration-300 ease-in-out mobile-scroll ${g?"max-h-screen opacity-100":"max-h-0 opacity-0"}`,children:(0,l.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 py-4 mobile-spacing",children:[u||y?(0,l.jsx)(m,{isMobile:!0}):(0,l.jsx)("nav",{className:"flex flex-col gap-1 mb-6",children:k.map((e,t)=>{let r="external"===e.target_type;if(e.subItems&&e.subItems.length>0)return(0,l.jsxs)("div",{className:"mx-2",children:[(0,l.jsxs)("div",{className:`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${_(e.href)?"bg-blue-600 text-white shadow-lg shadow-blue-600/25":"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"}`,style:{animationName:g?"slideInFromRight":"none",animationDuration:"0.3s",animationTimingFunction:"ease-out",animationFillMode:"forwards",animationDelay:`${50*t}ms`},children:[(0,l.jsx)("span",{className:`transition-transform duration-300 ${_(e.href)?"scale-110":""}`,children:e.icon}),(0,l.jsx)("span",{className:"text-sm flex-1",children:e.label}),(0,l.jsx)(e7,{className:"h-4 w-4"})]}),(0,l.jsx)("div",{className:"ml-6 mt-2 space-y-1",children:e.subItems?.map(e=>{let t="external"===e.target_type,r=t?{href:e.href,target:"_blank",rel:"noopener noreferrer"}:{href:e.href};return(0,l.jsxs)(i(),{...r,onClick:P,className:"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors",children:[e.label,t&&(0,l.jsx)(e0,{className:"h-3 w-3 opacity-60"})]},e.id)})})]},e.id);let a=r?{href:e.href,target:"_blank",rel:"noopener noreferrer"}:{href:e.href};return(0,l.jsxs)(i(),{...a,onClick:P,className:`flex items-center gap-3 px-4 py-4 mx-2 rounded-xl transition-all duration-300 font-medium touch-target mobile-button ${_(e.href)?"bg-blue-600 text-white shadow-lg shadow-blue-600/25":"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"}`,style:{animationName:g?"slideInFromRight":"none",animationDuration:"0.3s",animationTimingFunction:"ease-out",animationFillMode:"forwards",animationDelay:`${50*t}ms`},children:[(0,l.jsx)("span",{className:`transition-transform duration-300 ${_(e.href)?"scale-110":""}`,children:e.icon}),(0,l.jsx)("span",{className:"mobile-text-base",children:e.label}),r&&(0,l.jsx)(e0,{className:"h-3 w-3 opacity-60"})]},e.id)})}),(0,l.jsxs)("div",{className:"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700 mobile-spacing-sm",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(eL.J,{}),(0,l.jsx)(eA.ThemeToggle,{})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(eS,{variant:"ghost",size:"sm",className:"relative touch-target mobile-button",asChild:!0,children:(0,l.jsxs)(i(),{href:"/wishlist",children:[(0,l.jsx)(e8,{className:"h-5 w-5"}),n>0&&(0,l.jsx)(eD,{variant:"destructive",className:"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs",children:n>9?"9+":n})]})}),(0,l.jsx)(a.UserMenu,{})]})]})]})})]})})}let tt=eV("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),tr=eV("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),ta=eV("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),tn=eV("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),tl=eV("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),to=eV("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);function ts(){let{t:e}=f(),t=new Date().getFullYear();return(0,l.jsx)("footer",{className:"bg-gray-900 text-white mt-16",children:(0,l.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(e9,{className:"h-8 w-8 text-blue-400"}),(0,l.jsx)("span",{className:"text-xl font-bold",children:"Graduation Toqs"})]}),(0,l.jsx)("p",{className:"text-gray-300 arabic-text leading-relaxed",children:"أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,l.jsx)(tl,{className:"h-4 w-4 text-blue-400"}),(0,l.jsx)("span",{children:"<EMAIL>"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,l.jsx)(eQ,{className:"h-4 w-4 text-blue-400"}),(0,l.jsx)("span",{children:"+212 6 12 34 56 78"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,l.jsx)(to,{className:"h-4 w-4 text-blue-400"}),(0,l.jsx)("span",{className:"arabic-text",children:"بني ملال، المغرب"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الشركة"}),(0,l.jsx)("ul",{className:"space-y-2",children:[{href:"/about",label:"من نحن"},{href:"/contact",label:"تواصل معنا"},{href:"/support",label:"الدعم الفني"},{href:"/privacy",label:"سياسة الخصوصية"}].map(e=>(0,l.jsx)("li",{children:(0,l.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الخدمات"}),(0,l.jsx)("ul",{className:"space-y-2",children:[{href:"/catalog",label:"الكتالوج"},{href:"/customize",label:"التخصيص"},{href:"/track-order",label:"تتبع الطلب"},{href:"/size-guide",label:"دليل المقاسات"}].map(e=>(0,l.jsx)("li",{children:(0,l.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الدعم"}),(0,l.jsx)("ul",{className:"space-y-2",children:[{href:"/faq",label:"الأسئلة الشائعة"},{href:"/terms-conditions",label:"الشروط والأحكام"},{href:"/privacy-policy",label:"سياسة الخصوصية"},{href:"/support",label:"الدعم الفني"}].map(e=>(0,l.jsx)("li",{children:(0,l.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8",children:(0,l.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("span",{className:"text-gray-400 arabic-text",children:"تابعنا على:"}),[{href:"#",icon:tt,label:"Facebook"},{href:"#",icon:tr,label:"Twitter"},{href:"#",icon:ta,label:"Instagram"},{href:"#",icon:tn,label:"LinkedIn"}].map(e=>{let t=e.icon;return(0,l.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-blue-400 transition-colors","aria-label":e.label,children:(0,l.jsx)(t,{className:"h-5 w-5"})},e.label)})]}),(0,l.jsxs)("div",{className:"text-center md:text-right",children:[(0,l.jsxs)("p",{className:"text-gray-400 text-sm arabic-text",children:["\xa9 ",t," Graduation Toqs. جميع الحقوق محفوظة"]}),(0,l.jsxs)("p",{className:"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1",children:[(0,l.jsx)("span",{className:"arabic-text",children:"صُنع بـ"}),(0,l.jsx)(e8,{className:"h-3 w-3 text-red-500"}),(0,l.jsx)("span",{className:"arabic-text",children:"في المغرب"})]})]})]})})]})})}function ti({children:e,className:t="",showFooter:r=!0,containerClassName:a="container mx-auto px-4 py-8"}){return(0,l.jsxs)("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${t}`,children:[(0,l.jsx)(te,{}),(0,l.jsx)("main",{className:a,children:e}),r&&(0,l.jsx)(ts,{})]})}var tc=r(28421),tu=r(60404);function td({className:e,...t}){return(0,l.jsx)("div",{"data-slot":"card",className:eO("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}let tf=eV("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),tp=eV("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function th({icon:e,title:t,description:r,color:a,delay:s,features:i}){let[c,u]=(0,o.useState)(!1),d=(0,o.useRef)(null);return(0,l.jsx)("div",{ref:d,className:`transition-all duration-1000 ${c?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${s}ms`},children:(0,l.jsxs)(td,{className:"h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2",children:[(0,l.jsxs)(n.CardHeader,{className:"text-center pb-4",children:[(0,l.jsx)("div",{className:`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${a} group-hover:scale-110 transition-transform duration-300`,children:e}),(0,l.jsx)(n.CardTitle,{className:"text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2",children:t})]}),(0,l.jsxs)(n.CardContent,{className:"text-center",children:[(0,l.jsx)(n.CardDescription,{className:"text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4",children:r}),i&&(0,l.jsx)("div",{className:"space-y-2 mb-4",children:i.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400",children:[(0,l.jsx)(tf,{className:"w-4 h-4 text-green-500 flex-shrink-0"}),(0,l.jsx)("span",{className:"arabic-text",children:e})]},t))}),(0,l.jsxs)(eS,{variant:"ghost",size:"sm",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn",children:["اعرف المزيد",(0,l.jsx)(tp,{className:"w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform"})]})]})]})})}function tm(){let{t:e}=f(),t=[{icon:(0,l.jsx)(Palette,{className:"w-8 h-8 text-white"}),title:e("home.features.customization.title"),description:e("home.features.customization.description"),color:"bg-gradient-to-br from-purple-500 to-purple-600",delay:0,features:["معاينة فورية ثلاثية الأبعاد","مكتبة ألوان واسعة","إكسسوارات متنوعة"]},{icon:(0,l.jsx)(Sparkles,{className:"w-8 h-8 text-white"}),title:e("home.features.ai.title"),description:e("home.features.ai.description"),color:"bg-gradient-to-br from-yellow-500 to-yellow-600",delay:200,features:["اقتراحات ذكية مخصصة","تحليل الأسلوب الشخصي","مساعد افتراضي متقدم"]},{icon:(0,l.jsx)(Users,{className:"w-8 h-8 text-white"}),title:e("home.features.roles.title"),description:e("home.features.roles.description"),color:"bg-gradient-to-br from-green-500 to-green-600",delay:400,features:["لوحة تحكم للطلاب","إدارة المدارس","نظام شركاء التوصيل"]},{icon:(0,l.jsx)(GraduationCap,{className:"w-8 h-8 text-white"}),title:e("home.features.tracking.title"),description:e("home.features.tracking.description"),color:"bg-gradient-to-br from-blue-500 to-blue-600",delay:600,features:["تتبع لحظي للطلبات","إشعارات فورية","تحديثات مستمرة"]},{icon:(0,l.jsx)(Shield,{className:"w-8 h-8 text-white"}),title:e("home.features.quality.title"),description:e("home.features.quality.description"),color:"bg-gradient-to-br from-red-500 to-red-600",delay:800,features:["أقمشة فاخرة مختارة","تصنيع احترافي","ضمان الجودة"]},{icon:(0,l.jsx)(Headphones,{className:"w-8 h-8 text-white"}),title:e("home.features.support.title"),description:e("home.features.support.description"),color:"bg-gradient-to-br from-indigo-500 to-indigo-600",delay:1e3,features:["دعم على مدار الساعة","فريق متخصص","استجابة سريعة"]}];return(0,l.jsx)("section",{className:"py-20 bg-white dark:bg-gray-900",children:(0,l.jsxs)("div",{className:"container mx-auto px-4",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsxs)("div",{className:"inline-flex items-center gap-2 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-4 py-2 rounded-full text-sm font-medium mb-4",children:[(0,l.jsx)(Sparkles,{className:"w-4 h-4"}),"ميزاتنا المتقدمة"]}),(0,l.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:e("home.features.title")}),(0,l.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text",children:e("home.features.subtitle")})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map((e,t)=>(0,l.jsx)(th,{icon:e.icon,title:e.title,description:e.description,color:e.color,delay:e.delay,features:e.features},t))})]})})}var tb=r(41992),tg=r(88269);let tx=eV("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),ty=eV("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),tv=eV("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),tw=eV("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),tj=eV("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);function tk({icon:e,title:t,description:r,color:a,delay:s,isActive:i}){let[c,u]=(0,o.useState)(!1),d=(0,o.useRef)(null);return(0,l.jsx)("div",{ref:d,className:`transition-all duration-1000 ${c?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${s}ms`},children:(0,l.jsxs)(td,{className:`h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2 ${i?"ring-2 ring-blue-500 shadow-lg":""}`,children:[(0,l.jsxs)(n.CardHeader,{className:"text-center pb-4",children:[(0,l.jsxs)("div",{className:`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${a} group-hover:scale-110 transition-transform duration-300 relative`,children:[e,i&&(0,l.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white",children:(0,l.jsx)("div",{className:"w-full h-full bg-green-500 rounded-full animate-ping"})})]}),(0,l.jsx)(n.CardTitle,{className:"text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2",children:t})]}),(0,l.jsxs)(n.CardContent,{className:"text-center",children:[(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4",children:r}),(0,l.jsxs)(eS,{variant:"ghost",size:"sm",className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn",children:["جرب الآن",(0,l.jsx)(tp,{className:"w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform"})]})]})]})})}function t_({name:e,provider:t,status:r,description:a,delay:s}){let[i,c]=(0,o.useState)(!1),u=(0,o.useRef)(null);return(0,l.jsx)("div",{ref:u,className:`transition-all duration-1000 ${i?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${s}ms`},children:(0,l.jsx)(td,{className:"hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700",children:(0,l.jsxs)(n.CardContent,{className:"p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(tx,{className:"w-5 h-5 text-blue-600"}),(0,l.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:e})]}),(0,l.jsx)(eD,{variant:"active"===r?"default":"secondary",className:"active"===r?"bg-green-500":"",children:"active"===r?"نشط":"غير نشط"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-2",children:t}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 arabic-text",children:a})]})})})}function tN(){let{t:e}=f(),t=[{icon:(0,l.jsx)(ty,{className:"w-8 h-8 text-white"}),title:e("home.ai.features.assistant.title"),description:e("home.ai.features.assistant.description"),color:"bg-gradient-to-br from-blue-500 to-blue-600",delay:0,isActive:!0},{icon:(0,l.jsx)(tv,{className:"w-8 h-8 text-white"}),title:e("home.ai.features.visualization.title"),description:e("home.ai.features.visualization.description"),color:"bg-gradient-to-br from-purple-500 to-purple-600",delay:200,isActive:!0},{icon:(0,l.jsx)(tw,{className:"w-8 h-8 text-white"}),title:e("home.ai.features.recommendations.title"),description:e("home.ai.features.recommendations.description"),color:"bg-gradient-to-br from-yellow-500 to-yellow-600",delay:400,isActive:!0},{icon:(0,l.jsx)(tj,{className:"w-8 h-8 text-white"}),title:e("home.ai.features.chat.title"),description:e("home.ai.features.chat.description"),color:"bg-gradient-to-br from-green-500 to-green-600",delay:600,isActive:!0}];return(0,l.jsx)("section",{className:"py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:(0,l.jsxs)("div",{className:"container mx-auto px-4",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsxs)("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4",children:[(0,l.jsx)(ty,{className:"w-4 h-4"}),"الذكاء الاصطناعي"]}),(0,l.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:e("home.ai.title")}),(0,l.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text",children:e("home.ai.subtitle")})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:t.map((e,t)=>(0,l.jsx)(tk,{icon:e.icon,title:e.title,description:e.description,color:e.color,delay:e.delay,isActive:e.isActive},t))}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg",children:[(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"نماذج الذكاء الاصطناعي المتاحة"}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text",children:"نستخدم أحدث نماذج الذكاء الاصطناعي لتقديم أفضل تجربة"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[{name:"GPT-4",provider:"OpenAI",status:"active",description:"نموذج متقدم للمحادثة والتوصيات الذكية",delay:0},{name:"Claude 3",provider:"Anthropic",status:"active",description:"مساعد ذكي للتحليل والاقتراحات المخصصة",delay:200},{name:"Gemini Pro",provider:"Google",status:"active",description:"نموذج متعدد الوسائط للمعاينة والتصميم",delay:400},{name:"Mistral AI",provider:"Mistral",status:"inactive",description:"نموذج سريع للاستجابات الفورية",delay:600}].map((e,t)=>(0,l.jsx)(t_,{name:e.name,provider:e.provider,status:e.status,description:e.description,delay:100*t},t))}),(0,l.jsx)("div",{className:"text-center",children:(0,l.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,l.jsx)(ty,{className:"w-6 h-6 text-blue-600"}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white arabic-text",children:"جرب المساعد الذكي الآن"})]}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text mb-4",children:"اطلب من مساعدنا الذكي مساعدتك في اختيار ثوب التخرج المثالي"}),(0,l.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,l.jsxs)(eS,{className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white",children:[(0,l.jsx)(tj,{className:"w-4 h-4 mr-2"}),"ابدأ المحادثة"]}),(0,l.jsxs)(eS,{variant:"outline",children:[(0,l.jsx)(tv,{className:"w-4 h-4 mr-2"}),"معاينة ثلاثية الأبعاد"]}),(0,l.jsxs)(eS,{variant:"outline",children:[(0,l.jsx)(tx,{className:"w-4 h-4 mr-2"}),"تخصيص ذكي"]})]})]})})]})]})})}let tP=eV("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);function tR(){let{t:e}=f(),[t,r]=(0,o.useState)(!1),a=(0,o.useRef)(null);return(0,l.jsxs)("section",{ref:a,className:"py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden",children:[(0,l.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,l.jsx)("div",{className:"absolute top-10 left-10 w-20 h-20 border border-white rounded-full animate-pulse"}),(0,l.jsx)("div",{className:"absolute top-32 right-20 w-16 h-16 border border-white rounded-full animate-bounce"}),(0,l.jsx)("div",{className:"absolute bottom-20 left-32 w-24 h-24 border border-white rounded-full animate-pulse"}),(0,l.jsx)("div",{className:"absolute bottom-32 right-10 w-12 h-12 border border-white rounded-full animate-bounce"})]}),(0,l.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,l.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,l.jsxs)("div",{className:`transition-all duration-1000 ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,l.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6",children:(0,l.jsx)(GraduationCap,{className:"w-10 h-10 text-white"})}),(0,l.jsx)("h2",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 arabic-text leading-tight",children:e("home.cta.title")}),(0,l.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 mb-4 arabic-text",children:e("home.cta.subtitle")}),(0,l.jsx)("p",{className:"text-lg text-blue-200 mb-12 max-w-2xl mx-auto arabic-text leading-relaxed",children:e("home.cta.description")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,l.jsx)(Users,{className:"w-6 h-6 text-blue-200"}),(0,l.jsx)("span",{className:"text-3xl font-bold text-white",children:"1,200+"})]}),(0,l.jsx)("p",{className:"text-blue-200 arabic-text",children:"طالب راضٍ"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,l.jsx)(tP,{className:"w-6 h-6 text-yellow-300"}),(0,l.jsx)("span",{className:"text-3xl font-bold text-white",children:"4.9"})]}),(0,l.jsx)("p",{className:"text-blue-200 arabic-text",children:"تقييم العملاء"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,l.jsx)(GraduationCap,{className:"w-6 h-6 text-blue-200"}),(0,l.jsx)("span",{className:"text-3xl font-bold text-white",children:"50+"})]}),(0,l.jsx)("p",{className:"text-blue-200 arabic-text",children:"مدرسة وجامعة"})]})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,l.jsx)(eS,{size:"lg",className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group",asChild:!0,children:(0,l.jsxs)("a",{href:"/customize",className:"flex items-center gap-2",children:[(0,l.jsx)(Sparkles,{className:"w-5 h-5"}),e("home.cta.button"),(0,l.jsx)(tp,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})}),(0,l.jsx)(eS,{variant:"outline",size:"lg",className:"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold arabic-text transition-all duration-300",asChild:!0,children:(0,l.jsxs)("a",{href:"/contact",className:"flex items-center gap-2",children:[(0,l.jsx)(MessageSquare,{className:"w-5 h-5"}),e("home.cta.contact")]})})]})]}),(0,l.jsx)("div",{className:`mt-16 transition-all duration-1000 delay-500 ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:(0,l.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"✅"}),(0,l.jsx)("p",{className:"text-blue-100 arabic-text",children:"ضمان الجودة"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"\uD83D\uDE9A"}),(0,l.jsx)("p",{className:"text-blue-100 arabic-text",children:"توصيل مجاني"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"\uD83D\uDD04"}),(0,l.jsx)("p",{className:"text-blue-100 arabic-text",children:"إرجاع مجاني"})]})]})})})]})}),(0,l.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-50 dark:from-gray-900 to-transparent"})]})}function tM(){return(0,l.jsxs)(ti,{containerClassName:"",showFooter:!0,children:[(0,l.jsx)(tc.K,{}),(0,l.jsx)(tu.StatsSection,{}),(0,l.jsx)(tm,{}),(0,l.jsx)(tb.ProductsPreview,{}),(0,l.jsx)(tN,{}),(0,l.jsx)(tg.TestimonialsSection,{}),(0,l.jsx)(tR,{})]})}},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return a}}),r(59154),r(25232),r(29651),r(28627),r(78866),r(75076),r(97936),r(35429);let a=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return u}});let a=r(83913),n=r(89752),l=r(86770),o=r(57391),s=r(33123),i=r(33898),c=r(59435);function u(e,t,r,u,f){let p,h=t.tree,m=t.cache,b=(0,o.createHrefFromUrl)(u);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=d(r,Object.fromEntries(u.searchParams));let{seedData:o,isRootRender:c,pathToSegment:f}=t,g=["",...f];r=d(r,Object.fromEntries(u.searchParams));let x=(0,l.applyRouterStatePatchToTree)(g,h,r,b),y=(0,n.createEmptyCacheNode)();if(c&&o){let t=o[1];y.loading=o[3],y.rsc=t,function e(t,r,n,l,o){if(0!==Object.keys(l[1]).length)for(let i in l[1]){let c,u=l[1][i],d=u[0],f=(0,s.createRouterCacheKey)(d),p=null!==o&&void 0!==o[2][i]?o[2][i]:null;if(null!==p){let e=p[1],r=p[3];c={lazyData:null,rsc:d.includes(a.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=r.parallelRoutes.get(i);h?h.set(f,c):r.parallelRoutes.set(i,new Map([[f,c]])),e(t,c,n,u,p)}}(e,y,m,r,o)}else y.rsc=m.rsc,y.prefetchRsc=m.prefetchRsc,y.loading=m.loading,y.parallelRoutes=new Map(m.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(e,y,m,t);x&&(h=x,m=y,p=!0)}return!!p&&(f.patchedTree=h,f.cache=m,f.canonicalUrl=b,f.hashFragment=u.hash,(0,c.handleMutable)(t,f))}function d(e,t){let[r,n,...l]=e;if(r.includes(a.PAGE_SEGMENT_KEY))return[(0,a.addSearchParamsIfPageSegment)(r,t),n,...l];let o={};for(let[e,r]of Object.entries(n))o[e]=d(r,t);return[r,o,...l]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,l){let o=l.length<=2,[s,i]=l,c=(0,a.createRouterCacheKey)(i),u=r.parallelRoutes.get(s);if(!u)return;let d=t.parallelRoutes.get(s);if(d&&d!==u||(d=new Map(u),t.parallelRoutes.set(s,d)),o)return void d.delete(c);let f=u.get(c),p=d.get(c);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(c,p)),e(p,f,(0,n.getNextFlightSegmentPath)(l)))}}});let a=r(33123),n=r(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),a=r>-1&&(t<0||r<t);return a||t>-1?{pathname:e.substring(0,a?r:t),query:a?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page.tsx","default")},22308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[a,n,,o]=t;for(let s in a.includes(l.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=r,t[3]="refresh"),n)e(n[s],r)}},refreshInactiveParallelSegments:function(){return o}});let a=r(56928),n=r(59008),l=r(83913);async function o(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:r,updatedTree:l,updatedCache:o,includeNextUrl:i,fetchedSegments:c,rootTree:u=l,canonicalUrl:d}=e,[,f,p,h]=l,m=[];if(p&&p!==d&&"refresh"===h&&!c.has(p)){c.add(p);let e=(0,n.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[u[0],u[1],u[2],"refetch"],nextUrl:i?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,a.applyFlightData)(t,o,o,e)});m.push(e)}for(let e in f){let a=s({navigatedAt:t,state:r,updatedTree:f[e],updatedCache:o,includeNextUrl:i,fetchedSegments:c,rootTree:u,canonicalUrl:d});m.push(a)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,a=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;a[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:a,hasRestArgs:1==(1&t)}}function a(e,t){let r=Array(e.length);for(let a=0;a<e.length;a++)(a<6&&t.usedArgs[a]||a>=6&&t.hasRestArgs)&&(r[a]=e[a]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return a}})},25232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return y},navigateReducer:function(){return function e(t,r){let{url:w,isExternalUrl:j,navigateType:k,shouldScroll:_,allowAliasing:N}=r,P={},{hash:R}=w,M=(0,n.createHrefFromUrl)(w),E="push"===k;if((0,b.prunePrefetchCache)(t.prefetchCache),P.preserveCustomHistoryState=!1,P.pendingPush=E,j)return y(t,P,w.toString(),E);if(document.getElementById("__next-page-redirect"))return y(t,P,M,E);let T=(0,b.getOrCreatePrefetchCacheEntry)({url:w,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:N}),{treeAtTimeOfPrefetch:O,data:C}=T;return f.prefetchQueue.bump(C),C.then(f=>{let{flightData:b,canonicalUrl:j,postponed:k}=f,N=Date.now(),C=!1;if(T.lastUsedTime||(T.lastUsedTime=N,C=!0),T.aliased){let a=(0,x.handleAliasedPrefetchEntry)(N,t,b,w,P);return!1===a?e(t,{...r,allowAliasing:!1}):a}if("string"==typeof b)return y(t,P,b,E);let S=j?(0,n.createHrefFromUrl)(j):M;if(R&&t.canonicalUrl.split("#",1)[0]===S.split("#",1)[0])return P.onlyHashChange=!0,P.canonicalUrl=S,P.shouldScroll=_,P.hashFragment=R,P.scrollableSegments=[],(0,u.handleMutable)(t,P);let A=t.tree,L=t.cache,z=[];for(let e of b){let{pathToSegment:r,seedData:n,head:u,isHeadPartial:f,isRootRender:b}=e,x=e.tree,j=["",...r],_=(0,o.applyRouterStatePatchToTree)(j,A,x,M);if(null===_&&(_=(0,o.applyRouterStatePatchToTree)(j,O,x,M)),null!==_){if(n&&b&&k){let e=(0,m.startPPRNavigation)(N,L,A,x,n,u,f,!1,z);if(null!==e){if(null===e.route)return y(t,P,M,E);_=e.route;let r=e.node;null!==r&&(P.cache=r);let n=e.dynamicRequestTree;if(null!==n){let r=(0,a.fetchServerResponse)(w,{flightRouterState:n,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else _=x}else{if((0,i.isNavigatingToNewRootLayout)(A,_))return y(t,P,M,E);let a=(0,p.createEmptyCacheNode)(),n=!1;for(let t of(T.status!==c.PrefetchCacheEntryStatus.stale||C?n=(0,d.applyFlightData)(N,L,a,e,T):(n=function(e,t,r,a){let n=!1;for(let l of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),v(a).map(e=>[...r,...e])))(0,g.clearCacheNodeDataForSegmentPath)(e,t,l),n=!0;return n}(a,L,r,x),T.lastUsedTime=N),(0,s.shouldHardNavigate)(j,A)?(a.rsc=L.rsc,a.prefetchRsc=L.prefetchRsc,(0,l.invalidateCacheBelowFlightSegmentPath)(a,L,r),P.cache=a):n&&(P.cache=a,L=a),v(x))){let e=[...r,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&z.push(e)}}A=_}}return P.patchedTree=A,P.canonicalUrl=S,P.scrollableSegments=z,P.hashFragment=R,P.shouldScroll=_,(0,u.handleMutable)(t,P)},()=>t)}}});let a=r(59008),n=r(57391),l=r(18468),o=r(86770),s=r(65951),i=r(2030),c=r(59154),u=r(59435),d=r(56928),f=r(75076),p=r(89752),h=r(83913),m=r(65956),b=r(5334),g=r(97464),x=r(9707);function y(e,t,r,a){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=a,t.scrollableSegments=void 0,(0,u.handleMutable)(e,t)}function v(e){let t=[],[r,a]=e;if(0===Object.keys(a).length)return[[r]];for(let[e,n]of Object.entries(a))for(let a of v(n))""===r?t.push([e,...a]):t.push([r,e,...a]);return t}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,r)=>{"use strict";function a(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return a}}),r(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return n}});let a=r(2255);function n(e){return(0,a.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28421:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'function'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\home\\HeroSection.tsx\x1b[0m:16:1]\n \x1b[2m13\x1b[0m │ import { Progress } from '@/components/ui/progress'\n \x1b[2m14\x1b[0m │ import {\n \x1b[2m15\x1b[0m │ \n \x1b[2m16\x1b[0m │ export function HeroSection() {\n    \xb7 \x1b[35;1m       ────────\x1b[0m\n \x1b[2m17\x1b[0m │   const { t } = useTranslation()\n \x1b[2m18\x1b[0m │   const [isVisible, setIsVisible] = useState(false)\n    ╰────\n\n\nCaused by:\n    Syntax Error")},28627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return l}});let a=r(57391),n=r(70642);function l(e,t){var r;let{url:l,tree:o}=t,s=(0,a.createHrefFromUrl)(l),i=o||e.tree,c=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:i,nextUrl:null!=(r=(0,n.extractPathFromFlightRouterState)(i))?r:l.pathname}}r(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return u}});let a=r(57391),n=r(86770),l=r(2030),o=r(25232),s=r(56928),i=r(59435),c=r(89752);function u(e,t){let{serverResponse:{flightData:r,canonicalUrl:u},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof r)return(0,o.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of r){let{segmentPath:r,tree:i}=t,m=(0,n.applyRouterStatePatchToTree)(["",...r],p,i,e.canonicalUrl);if(null===m)return e;if((0,l.isNavigatingToNewRootLayout)(p,m))return(0,o.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let b=u?(0,a.createHrefFromUrl)(u):void 0;b&&(f.canonicalUrl=b);let g=(0,c.createEmptyCacheNode)();(0,s.applyFlightData)(d,h,g,t),f.patchedTree=m,f.cache=g,h=g,p=m}return(0,i.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return l},formatWithValidation:function(){return s},urlObjectKeys:function(){return o}});let a=r(40740)._(r(76715)),n=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:r}=e,l=e.protocol||"",o=e.pathname||"",s=e.hash||"",i=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(a.urlQueryToSearchParams(i)));let u=e.search||i&&"?"+i||"";return l&&!l.endsWith(":")&&(l+=":"),e.slashes||(!l||n.test(l))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),s&&"#"!==s[0]&&(s="#"+s),u&&"?"!==u[0]&&(u="?"+u),""+l+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+s}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return l(e)}},30481:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\notifications\\NotificationDropdown.tsx\x1b[0m:11:1]\n \x1b[2m 8\x1b[0m │ import { ScrollArea } from '@/components/ui/scroll-area'\n \x1b[2m 9\x1b[0m │ \n \x1b[2m10\x1b[0m │ import {\n \x1b[2m11\x1b[0m │   import {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m12\x1b[0m │   Bell,\n \x1b[2m13\x1b[0m │   X,\n \x1b[2m14\x1b[0m │   Bell\n    ╰────\n\n\nCaused by:\n    Syntax Error")},30917:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'function'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\language-toggle.tsx\x1b[0m:12:1]\n \x1b[2m 9\x1b[0m │ import { Button } from \"@/components/ui/button\"\n \x1b[2m10\x1b[0m │ import {\n \x1b[2m11\x1b[0m │ \n \x1b[2m12\x1b[0m │ export function LanguageToggle() {\n    \xb7 \x1b[35;1m       ────────\x1b[0m\n \x1b[2m13\x1b[0m │   const { locale, changeLocale } = useTranslation()\n \x1b[2m14\x1b[0m │ \n \x1b[2m15\x1b[0m │   const getCurrentLanguage = () => {\n    ╰────\n\n\nCaused by:\n    Syntax Error")},32708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},33274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c});var a=r(65239),n=r(48088),l=r(88170),o=r.n(l),s=r(30893),i={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>s[e]);r.d(t,i);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.t.bind(r,54431,23)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},33898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let a=r(34400),n=r(41500),l=r(33123),o=r(83913);function s(e,t,r,s,i,c){let{segmentPath:u,seedData:d,tree:f,head:p}=s,h=t,m=r;for(let t=0;t<u.length;t+=2){let r=u[t],s=u[t+1],b=t===u.length-2,g=(0,l.createRouterCacheKey)(s),x=m.parallelRoutes.get(r);if(!x)continue;let y=h.parallelRoutes.get(r);y&&y!==x||(y=new Map(x),h.parallelRoutes.set(r,y));let v=x.get(g),w=y.get(g);if(b){if(d&&(!w||!w.lazyData||w===v)){let t=d[0],r=d[1],l=d[3];w={lazyData:null,rsc:c||t!==o.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:l,parallelRoutes:c&&v?new Map(v.parallelRoutes):new Map,navigatedAt:e},v&&c&&(0,a.invalidateCacheByRouterState)(w,v,f),c&&(0,n.fillLazyItemsTillLeafWithHead)(e,w,v,f,d,p,i),y.set(g,w)}continue}w&&v&&(w===v&&(w={lazyData:w.lazyData,rsc:w.rsc,prefetchRsc:w.prefetchRsc,head:w.head,prefetchHead:w.prefetchHead,parallelRoutes:new Map(w.parallelRoutes),loading:w.loading},y.set(g,w)),h=w,m=v)}}function i(e,t,r,a,n){s(e,t,r,a,n,!0)}function c(e,t,r,a,n){s(e,t,r,a,n,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return n}});let a=r(33123);function n(e,t,r){for(let n in r[1]){let l=r[1][n][0],o=(0,a.createRouterCacheKey)(l),s=t.parallelRoutes.get(n);if(s){let t=new Map(s);t.delete(o),e.parallelRoutes.set(n,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return a.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return l},getBotType:function(){return i},isBot:function(){return s}});let a=r(95796),n=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,l=a.HTML_LIMITED_BOT_UA_RE.source;function o(e){return a.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return n.test(e)||o(e)}function i(e){return n.test(e)?"dom":o(e)?"html":void 0}},35429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return E}});let a=r(11264),n=r(11448),l=r(91563),o=r(59154),s=r(6361),i=r(57391),c=r(25232),u=r(86770),d=r(2030),f=r(59435),p=r(41500),h=r(89752),m=r(68214),b=r(96493),g=r(22308),x=r(74007),y=r(36875),v=r(97860),w=r(5334),j=r(25942),k=r(26736),_=r(24642);r(50593);let{createFromFetch:N,createTemporaryReferenceSet:P,encodeReply:R}=r(19357);async function M(e,t,r){let o,i,{actionId:c,actionArgs:u}=r,d=P(),f=(0,_.extractInfoFromServerReferenceId)(c),p="use-cache"===f.type?(0,_.omitUnusedArgs)(u,f):u,h=await R(p,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:l.RSC_CONTENT_TYPE_HEADER,[l.ACTION_HEADER]:c,[l.NEXT_ROUTER_STATE_TREE_HEADER]:(0,x.prepareFlightRouterStateForRequest)(e.tree),...{},...t?{[l.NEXT_URL]:t}:{}},body:h}),b=m.headers.get("x-action-redirect"),[g,y]=(null==b?void 0:b.split(";"))||[];switch(y){case"push":o=v.RedirectType.push;break;case"replace":o=v.RedirectType.replace;break;default:o=void 0}let w=!!m.headers.get(l.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");i={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){i={paths:[],tag:!1,cookie:!1}}let j=g?(0,s.assignLocation)(g,new URL(e.canonicalUrl,window.location.href)):void 0,k=m.headers.get("content-type");if(null==k?void 0:k.startsWith(l.RSC_CONTENT_TYPE_HEADER)){let e=await N(Promise.resolve(m),{callServer:a.callServer,findSourceMapURL:n.findSourceMapURL,temporaryReferences:d});return g?{actionFlightData:(0,x.normalizeFlightData)(e.f),redirectLocation:j,redirectType:o,revalidatedParts:i,isPrerender:w}:{actionResult:e.a,actionFlightData:(0,x.normalizeFlightData)(e.f),redirectLocation:j,redirectType:o,revalidatedParts:i,isPrerender:w}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===k?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:j,redirectType:o,revalidatedParts:i,isPrerender:w}}function E(e,t){let{resolve:r,reject:a}=t,n={},l=e.tree;n.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,x=Date.now();return M(e,s,t).then(async m=>{let _,{actionResult:N,actionFlightData:P,redirectLocation:R,redirectType:M,isPrerender:E,revalidatedParts:T}=m;if(R&&(M===v.RedirectType.replace?(e.pushRef.pendingPush=!1,n.pendingPush=!1):(e.pushRef.pendingPush=!0,n.pendingPush=!0),n.canonicalUrl=_=(0,i.createHrefFromUrl)(R,!1)),!P)return(r(N),R)?(0,c.handleExternalUrl)(e,n,R.href,e.pushRef.pendingPush):e;if("string"==typeof P)return r(N),(0,c.handleExternalUrl)(e,n,P,e.pushRef.pendingPush);let O=T.paths.length>0||T.tag||T.cookie;for(let a of P){let{tree:o,seedData:i,head:f,isRootRender:m}=a;if(!m)return r(N),e;let y=(0,u.applyRouterStatePatchToTree)([""],l,o,_||e.canonicalUrl);if(null===y)return r(N),(0,b.handleSegmentMismatch)(e,t,o);if((0,d.isNavigatingToNewRootLayout)(l,y))return r(N),(0,c.handleExternalUrl)(e,n,_||e.canonicalUrl,e.pushRef.pendingPush);if(null!==i){let t=i[1],r=(0,h.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=i[3],(0,p.fillLazyItemsTillLeafWithHead)(x,r,void 0,o,i,f,void 0),n.cache=r,n.prefetchCache=new Map,O&&await (0,g.refreshInactiveParallelSegments)({navigatedAt:x,state:e,updatedTree:y,updatedCache:r,includeNextUrl:!!s,canonicalUrl:n.canonicalUrl||e.canonicalUrl})}n.patchedTree=y,l=y}return R&&_?(O||((0,w.createSeededPrefetchCacheEntry)({url:R,data:{flightData:P,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:E?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),n.prefetchCache=e.prefetchCache),a((0,y.getRedirectError)((0,k.hasBasePath)(_)?(0,j.removeBasePath)(_):_,M||v.RedirectType.push))):r(N),(0,f.handleMutable)(e,n)},t=>(a(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,l,o,s,i,c){if(0===Object.keys(o[1]).length){r.head=i;return}for(let u in o[1]){let d,f=o[1][u],p=f[0],h=(0,a.createRouterCacheKey)(p),m=null!==s&&void 0!==s[2][u]?s[2][u]:null;if(l){let a=l.parallelRoutes.get(u);if(a){let l,o=(null==c?void 0:c.kind)==="auto"&&c.status===n.PrefetchCacheEntryStatus.reusable,s=new Map(a),d=s.get(h);l=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:o&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},s.set(h,l),e(t,l,d,f,m||null,i,c),r.parallelRoutes.set(u,s);continue}}if(null!==m){let e=m[1],r=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let b=r.parallelRoutes.get(u);b?b.set(h,d):r.parallelRoutes.set(u,new Map([[h,d]])),e(t,d,void 0,f,m,i,c)}}}});let a=r(33123),n=r(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41992:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\home\\ProductsPreview.tsx\x1b[0m:12:1]\n \x1b[2m 9\x1b[0m │ \n \x1b[2m10\x1b[0m │ \n \x1b[2m11\x1b[0m │ import {\n \x1b[2m12\x1b[0m │   import {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m13\x1b[0m │ import { Label } from '@/components/ui/label'\n \x1b[2m14\x1b[0m │ import { Input } from '@/components/ui/input'\n \x1b[2m15\x1b[0m │ import { Textarea } from '@/components/ui/textarea'\n    ╰────\n\n\nCaused by:\n    Syntax Error")},43984:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'import'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-toggle.tsx\x1b[0m:8:1]\n \x1b[2m 5\x1b[0m │ import {\n \x1b[2m 6\x1b[0m │   useTheme\n \x1b[2m 7\x1b[0m │ \n \x1b[2m 8\x1b[0m │ import {\n    \xb7 \x1b[35;1m──────\x1b[0m\n \x1b[2m 9\x1b[0m │ \n \x1b[2m10\x1b[0m │ import {\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return n}});let a=r(33123);function n(e,t){return function e(t,r,n){if(0===Object.keys(r).length)return[t,n];let l=Object.keys(r).filter(e=>"children"!==e);for(let o of("children"in r&&l.unshift("children"),l)){let[l,s]=r[o],i=t.parallelRoutes.get(o);if(!i)continue;let c=(0,a.createRouterCacheKey)(l),u=i.get(c);if(!u)continue;let d=e(u,s,n+"/"+c);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return i},createCacheKey:function(){return u},getCurrentCacheVersion:function(){return o},navigate:function(){return n},prefetch:function(){return a},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return l},schedulePrefetchTask:function(){return s}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},a=r,n=r,l=r,o=r,s=r,i=r,c=r,u=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,r)=>{"use strict";function a(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>a})},52325:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=r(43210);function n(e,t){let r=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=l(e,a)),t&&(n.current=l(t,a))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return l}});let a=r(84949),n=r(19169),l=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:l}=(0,n.parsePath)(e);return""+(0,a.removeTrailingSlash)(t)+r+l};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return l}});let a=r(41500),n=r(33898);function l(e,t,r,l,o){let{tree:s,seedData:i,head:c,isRootRender:u}=l;if(null===i)return!1;if(u){let n=i[1];r.loading=i[3],r.rsc=n,r.prefetchRsc=null,(0,a.fillLazyItemsTillLeafWithHead)(e,r,t,s,i,c,o)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,n.fillCacheWithNewSubTreeData)(e,r,t,l,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return l}});let a=r(70642);function n(e){return void 0!==e}function l(e,t){var r,l;let o=null==(r=t.shouldScroll)||r,s=e.nextUrl;if(n(t.patchedTree)){let r=(0,a.computeChangedPath)(e.tree,t.patchedTree);r?s=r:s||(s=e.canonicalUrl)}return{canonicalUrl:n(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:n(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:n(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:n(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!n(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(l=null==t?void 0:t.scrollableSegments)?l:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:n(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>n});var a=0;function n(e){return"__private_"+a+++"_"+e}},60404:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\home\\StatsSection.tsx\x1b[0m:9:1]\n \x1b[2m 6\x1b[0m │ import { useStats } from \"@/hooks/useStats\"\n \x1b[2m 7\x1b[0m │ import { Card, CardContent } from \"@/components/ui/card\"\n \x1b[2m 8\x1b[0m │ import {\n \x1b[2m 9\x1b[0m │   import {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m10\x1b[0m │ import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'\n \x1b[2m11\x1b[0m │   Star,\n \x1b[2m12\x1b[0m │   Star\n    ╰────\n\n\nCaused by:\n    Syntax Error")},61794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return l}});let a=r(79289),n=r(26736);function l(e){if(!(0,a.isAbsoluteUrl)(e))return!0;try{let t=(0,a.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,n.hasBasePath)(r.pathname)}catch(e){return!1}}},62597:(e,t,r)=>{Promise.resolve().then(r.bind(r,8261))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return g},dispatchTraverseAction:function(){return x},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return y}});let a=r(59154),n=r(8830),l=r(43210),o=r(91992);r(50593);let s=r(19129),i=r(96127),c=r(89752),u=r(75076),d=r(73406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:a.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:r,setState:a}=e,n=t.state;t.pending=r;let l=r.payload,s=t.action(n,l);function i(e){r.discarded||(t.state=e,f(t,a),r.resolve(e))}(0,o.isThenable)(s)?s.then(i,e=>{f(t,a),r.reject(e)}):i(s)}function h(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let n={resolve:r,reject:()=>{}};if(t.type!==a.ACTION_RESTORE){let e=new Promise((e,t)=>{n={resolve:e,reject:t}});(0,l.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:n.resolve,reject:n.reject};null===e.pending?(e.last=o,p({actionQueue:e,action:o,setState:r})):t.type===a.ACTION_NAVIGATE||t.type===a.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===a.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(r,e,t),action:async(e,t)=>(0,n.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function m(){return null}function b(){return null}function g(e,t,r,n){let l=new URL((0,i.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(n);(0,s.dispatchAppRouterAction)({type:a.ACTION_NAVIGATE,url:l,isExternalUrl:(0,c.isExternalURL)(l),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function x(e,t){(0,s.dispatchAppRouterAction)({type:a.ACTION_RESTORE,url:new URL(e),tree:t})}let y={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),n=(0,c.createPrefetchURL)(e);if(null!==n){var l;(0,u.prefetchReducer)(r.state,{type:a.ACTION_PREFETCH,url:n,kind:null!=(l=null==t?void 0:t.kind)?l:a.PrefetchKind.FULL})}},replace:(e,t)=>{(0,l.startTransition)(()=>{var r;g(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,l.startTransition)(()=>{var r;g(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,l.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:a.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[l,o]=r,[s,i]=t;return(0,n.matchSegment)(s,l)?!(t.length<=2)&&e((0,a.getNextFlightSegmentPath)(t),o[i]):!!Array.isArray(s)}}});let a=r(74007),n=r(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let a=r[1],n=t.parallelRoutes,o=new Map(n);for(let t in a){let r=a[t],s=r[0],i=(0,l.createRouterCacheKey)(s),c=n.get(t);if(void 0!==c){let a=c.get(i);if(void 0!==a){let n=e(a,r),l=new Map(c);l.set(i,n),o.set(t,l)}}}let s=t.rsc,i=g(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:i?t.prefetchHead:[null,null],prefetchRsc:i?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let a=r(83913),n=r(14077),l=r(33123),o=r(2030),s=r(5334),i={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,r,o,s,c,f,p,h){return function e(t,r,o,s,c,f,p,h,m,b,g){let x=o[1],y=s[1],v=null!==f?f[2]:null;c||!0===s[4]&&(c=!0);let w=r.parallelRoutes,j=new Map(w),k={},_=null,N=!1,P={};for(let r in y){let o,s=y[r],d=x[r],f=w.get(r),R=null!==v?v[r]:null,M=s[0],E=b.concat([r,M]),T=(0,l.createRouterCacheKey)(M),O=void 0!==d?d[0]:void 0,C=void 0!==f?f.get(T):void 0;if(null!==(o=M===a.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:u(t,d,s,C,c,void 0!==R?R:null,p,h,E,g):m&&0===Object.keys(s[1]).length?u(t,d,s,C,c,void 0!==R?R:null,p,h,E,g):void 0!==d&&void 0!==O&&(0,n.matchSegment)(M,O)&&void 0!==C&&void 0!==d?e(t,C,d,s,c,R,p,h,m,E,g):u(t,d,s,C,c,void 0!==R?R:null,p,h,E,g))){if(null===o.route)return i;null===_&&(_=new Map),_.set(r,o);let e=o.node;if(null!==e){let t=new Map(f);t.set(T,e),j.set(r,t)}let t=o.route;k[r]=t;let a=o.dynamicRequestTree;null!==a?(N=!0,P[r]=a):P[r]=t}else k[r]=s,P[r]=s}if(null===_)return null;let R={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:j,navigatedAt:t};return{route:d(s,k),node:R,dynamicRequestTree:N?d(s,P):null,children:_}}(e,t,r,o,!1,s,c,f,p,[],h)}function u(e,t,r,a,n,c,u,p,h,m){return!n&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,r))?i:function e(t,r,a,n,o,i,c,u){let p,h,m,b,g=r[1],x=0===Object.keys(g).length;if(void 0!==a&&a.navigatedAt+s.DYNAMIC_STALETIME_MS>t)p=a.rsc,h=a.loading,m=a.head,b=a.navigatedAt;else if(null===n)return f(t,r,null,o,i,c,u);else if(p=n[1],h=n[3],m=x?o:null,b=t,n[4]||i&&x)return f(t,r,n,o,i,c,u);let y=null!==n?n[2]:null,v=new Map,w=void 0!==a?a.parallelRoutes:null,j=new Map(w),k={},_=!1;if(x)u.push(c);else for(let r in g){let a=g[r],n=null!==y?y[r]:null,s=null!==w?w.get(r):void 0,d=a[0],f=c.concat([r,d]),p=(0,l.createRouterCacheKey)(d),h=e(t,a,void 0!==s?s.get(p):void 0,n,o,i,f,u);v.set(r,h);let m=h.dynamicRequestTree;null!==m?(_=!0,k[r]=m):k[r]=a;let b=h.node;if(null!==b){let e=new Map;e.set(p,b),j.set(r,e)}}return{route:r,node:{lazyData:null,rsc:p,prefetchRsc:null,head:m,prefetchHead:null,loading:h,parallelRoutes:j,navigatedAt:b},dynamicRequestTree:_?d(r,k):null,children:v}}(e,r,a,c,u,p,h,m)}function d(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function f(e,t,r,a,n,o,s){let i=d(t,t[1]);return i[3]="refetch",{route:t,node:function e(t,r,a,n,o,s,i){let c=r[1],u=null!==a?a[2]:null,d=new Map;for(let r in c){let a=c[r],f=null!==u?u[r]:null,p=a[0],h=s.concat([r,p]),m=(0,l.createRouterCacheKey)(p),b=e(t,a,void 0===f?null:f,n,o,h,i),g=new Map;g.set(m,b),d.set(r,g)}let f=0===d.size;f&&i.push(s);let p=null!==a?a[1]:null,h=null!==a?a[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:f?n:[null,null],loading:void 0!==h?h:null,rsc:x(),head:f?x():null,navigatedAt:t}}(e,t,r,a,n,o,s),dynamicRequestTree:i,children:null}}function p(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:a,seedData:o,head:s}=t;o&&function(e,t,r,a,o){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],a=t[e+1],l=s.children;if(null!==l){let e=l.get(r);if(void 0!==e){let t=e.route[0];if((0,n.matchSegment)(a,t)){s=e;continue}}}return}!function e(t,r,a,o){if(null===t.dynamicRequestTree)return;let s=t.children,i=t.node;if(null===s){null!==i&&(function e(t,r,a,o,s){let i=r[1],c=a[1],u=o[2],d=t.parallelRoutes;for(let t in i){let r=i[t],a=c[t],o=u[t],f=d.get(t),p=r[0],h=(0,l.createRouterCacheKey)(p),b=void 0!==f?f.get(h):void 0;void 0!==b&&(void 0!==a&&(0,n.matchSegment)(p,a[0])&&null!=o?e(b,r,a,o,s):m(r,b,null))}let f=t.rsc,p=o[1];null===f?t.rsc=p:g(f)&&f.resolve(p);let h=t.head;g(h)&&h.resolve(s)}(i,t.route,r,a,o),t.dynamicRequestTree=null);return}let c=r[1],u=a[2];for(let t in r){let r=c[t],a=u[t],l=s.get(t);if(void 0!==l){let t=l.route[0];if((0,n.matchSegment)(r[0],t)&&null!=a)return e(l,r,a,o)}}}(s,r,a,o)}(e,r,a,o,s)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let r=e.node;if(null===r)return;let a=e.children;if(null===a)m(e.route,r,t);else for(let e of a.values())h(e,t);e.dynamicRequestTree=null}function m(e,t,r){let a=e[1],n=t.parallelRoutes;for(let e in a){let t=a[e],o=n.get(e);if(void 0===o)continue;let s=t[0],i=(0,l.createRouterCacheKey)(s),c=o.get(i);void 0!==c&&m(t,c,r)}let o=t.rsc;g(o)&&(null===r?o.resolve(null):o.reject(r));let s=t.head;g(s)&&s.resolve(null)}let b=Symbol();function g(e){return e&&e.tag===b}function x(){let e,t,r=new Promise((r,a)=>{e=r,t=a});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=b,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return u},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,r){for(let a of(void 0===r&&(r={}),Object.values(t[1]))){let t=a[0],l=Array.isArray(t),o=l?t[1]:t;!o||o.startsWith(n.PAGE_SEGMENT_KEY)||(l&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):l&&(r[t[0]]=t[1]),r=e(a,r))}return r}}});let a=r(72859),n=r(83913),l=r(14077),o=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function i(e){return e.reduce((e,t)=>""===(t=o(t))||(0,n.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===n.DEFAULT_SEGMENT_KEY||a.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(n.PAGE_SEGMENT_KEY))return"";let l=[s(r)],o=null!=(t=e[1])?t:{},u=o.children?c(o.children):void 0;if(void 0!==u)l.push(u);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let r=c(t);void 0!==r&&l.push(r)}return i(l)}function u(e,t){let r=function e(t,r){let[n,o]=t,[i,u]=r,d=s(n),f=s(i);if(a.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,l.matchSegment)(n,i)){var p;return null!=(p=c(r))?p:""}for(let t in o)if(u[t]){let r=e(o[t],u[t]);if(null!==r)return s(i)+"/"+r}return null}(e,t);return null==r||"/"===r?r:i(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return i},mountFormInstance:function(){return x},mountLinkInstance:function(){return g},onLinkVisibilityChanged:function(){return v},onNavigationIntent:function(){return w},pingVisibleLinks:function(){return k},setLinkForCurrentNavigation:function(){return u},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return y}}),r(63690);let a=r(89752),n=r(59154),l=r(50593),o=r(43210),s=null,i={pending:!0},c={pending:!1};function u(e){(0,o.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(i),s=e})}function d(e){s===e&&(s=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;v(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==f.get(e)&&y(e),f.set(e,t),null!==h&&h.observe(e)}function b(e){try{return(0,a.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function g(e,t,r,a,n,l){if(n){let n=b(t);if(null!==n){let t={router:r,kind:a,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:n.href,setOptimisticLinkStatus:l};return m(e,t),t}}return{router:r,kind:a,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:l}}function x(e,t,r,a){let n=b(t);null!==n&&m(e,{router:r,kind:a,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:n.href,setOptimisticLinkStatus:null})}function y(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let r=t.prefetchTask;null!==r&&(0,l.cancelPrefetchTask)(r)}null!==h&&h.unobserve(e)}function v(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?p.add(r):p.delete(r),j(r))}function w(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,j(r))}function j(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,l.cancelPrefetchTask)(t);return}}function k(e,t){let r=(0,l.getCurrentCacheVersion)();for(let a of p){let o=a.prefetchTask;if(null!==o&&a.cacheVersion===r&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,l.cancelPrefetchTask)(o);let s=(0,l.createCacheKey)(a.prefetchHref,e),i=a.wasHoveredOrTouched?l.PrefetchPriority.Intent:l.PrefetchPriority.Default;a.prefetchTask=(0,l.schedulePrefetchTask)(s,t,a.kind===n.PrefetchKind.FULL,i),a.cacheVersion=(0,l.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return l},prefetchReducer:function(){return o}});let a=r(5144),n=r(5334),l=new a.PromiseQueue(5),o=function(e,t){(0,n.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,n.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,a]of e.entries()){let e=t[r];void 0===e?t[r]=a:Array.isArray(e)?e.push(a):t[r]=[e,a]}return t}function a(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,a(e));else t.set(r,a(n));return t}function l(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,a]of t.entries())e.append(r,a)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},77022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let a=r(43210),n=r(51215),l="next-route-announcer";function o(e){let{tree:t}=e,[r,o]=(0,a.useState)(null);(0,a.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(l)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(l);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(l)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,i]=(0,a.useState)(""),c=(0,a.useRef)(void 0);return(0,a.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&i(e),c.current=e},[t]),r?(0,n.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let a=r(59008),n=r(57391),l=r(86770),o=r(2030),s=r(25232),i=r(59435),c=r(41500),u=r(89752),d=r(96493),f=r(68214),p=r(22308);function h(e,t){let{origin:r}=t,h={},m=e.canonicalUrl,b=e.tree;h.preserveCustomHistoryState=!1;let g=(0,u.createEmptyCacheNode)(),x=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);g.lazyData=(0,a.fetchServerResponse)(new URL(m,r),{flightRouterState:[b[0],b[1],b[2],"refetch"],nextUrl:x?e.nextUrl:null});let y=Date.now();return g.lazyData.then(async r=>{let{flightData:a,canonicalUrl:u}=r;if("string"==typeof a)return(0,s.handleExternalUrl)(e,h,a,e.pushRef.pendingPush);for(let r of(g.lazyData=null,a)){let{tree:a,seedData:i,head:f,isRootRender:v}=r;if(!v)return e;let w=(0,l.applyRouterStatePatchToTree)([""],b,a,e.canonicalUrl);if(null===w)return(0,d.handleSegmentMismatch)(e,t,a);if((0,o.isNavigatingToNewRootLayout)(b,w))return(0,s.handleExternalUrl)(e,h,m,e.pushRef.pendingPush);let j=u?(0,n.createHrefFromUrl)(u):void 0;if(u&&(h.canonicalUrl=j),null!==i){let e=i[1],t=i[3];g.rsc=e,g.prefetchRsc=null,g.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(y,g,void 0,a,i,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:y,state:e,updatedTree:w,updatedCache:g,includeNextUrl:x,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=g,h.patchedTree=w,b=w}return(0,i.handleMutable)(e,h)},()=>e)}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return b},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return a},getDisplayName:function(){return i},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function a(e){let t,r=!1;return function(){for(var a=arguments.length,n=Array(a),l=0;l<a;l++)n[l]=arguments[l];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>n.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let a=await e.getInitialProps(t);if(r&&c(r))return a;if(!a)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+a+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},79551:e=>{"use strict";e.exports=require("url")},84949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},85814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},useLinkStatus:function(){return x}});let a=r(40740),n=r(60687),l=a._(r(43210)),o=r(30195),s=r(22142),i=r(59154),c=r(53038),u=r(79289),d=r(96127);r(50148);let f=r(73406),p=r(61794),h=r(63690);function m(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function b(e){let t,r,a,[o,b]=(0,l.useOptimistic)(f.IDLE_LINK_STATUS),x=(0,l.useRef)(null),{href:y,as:v,children:w,prefetch:j=null,passHref:k,replace:_,shallow:N,scroll:P,onClick:R,onMouseEnter:M,onTouchStart:E,legacyBehavior:T=!1,onNavigate:O,ref:C,unstable_dynamicOnHover:S,...A}=e;t=w,T&&("string"==typeof t||"number"==typeof t)&&(t=(0,n.jsx)("a",{children:t}));let L=l.default.useContext(s.AppRouterContext),z=!1!==j,U=null===j?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:D,as:I}=l.default.useMemo(()=>{let e=m(y);return{href:e,as:v?m(v):e}},[y,v]);T&&(r=l.default.Children.only(t));let H=T?r&&"object"==typeof r&&r.ref:C,F=l.default.useCallback(e=>(null!==L&&(x.current=(0,f.mountLinkInstance)(e,D,L,U,z,b)),()=>{x.current&&((0,f.unmountLinkForCurrentNavigation)(x.current),x.current=null),(0,f.unmountPrefetchableInstance)(e)}),[z,D,L,U,b]),q={ref:(0,c.useMergedRef)(F,H),onClick(e){T||"function"!=typeof R||R(e),T&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,a,n,o,s){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),l.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(r||t,n?"replace":"push",null==o||o,a.current)})}}(e,D,I,x,_,P,O))},onMouseEnter(e){T||"function"!=typeof M||M(e),T&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&z&&(0,f.onNavigationIntent)(e.currentTarget,!0===S)},onTouchStart:function(e){T||"function"!=typeof E||E(e),T&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&z&&(0,f.onNavigationIntent)(e.currentTarget,!0===S)}};return(0,u.isAbsoluteUrl)(I)?q.href=I:T&&!k&&("a"!==r.type||"href"in r.props)||(q.href=(0,d.addBasePath)(I)),a=T?l.default.cloneElement(r,q):(0,n.jsx)("a",{...A,...q,children:t}),(0,n.jsx)(g.Provider,{value:o,children:a})}r(32708);let g=(0,l.createContext)(f.IDLE_LINK_STATUS),x=()=>(0,l.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,a,i){let c,[u,d,f,p,h]=r;if(1===t.length){let e=s(r,a);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,i),e}let[m,b]=t;if(!(0,l.matchSegment)(m,u))return null;if(2===t.length)c=s(d[b],a);else if(null===(c=e((0,n.getNextFlightSegmentPath)(t),d[b],a,i)))return null;let g=[t[0],{...d,[b]:c},f,p];return h&&(g[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(g,i),g}}});let a=r(83913),n=r(74007),l=r(14077),o=r(22308);function s(e,t){let[r,n]=e,[o,i]=t;if(o===a.DEFAULT_SEGMENT_KEY&&r!==a.DEFAULT_SEGMENT_KEY)return e;if((0,l.matchSegment)(r,o)){let t={};for(let e in n)void 0!==i[e]?t[e]=s(n[e],i[e]):t[e]=n[e];for(let e in i)t[e]||(t[e]=i[e]);let a=[r,t];return e[2]&&(a[2]=e[2]),e[3]&&(a[3]=e[3]),e[4]&&(a[4]=e[4]),a}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88269:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\home\\TestimonialsSection.tsx\x1b[0m:12:1]\n \x1b[2m 9\x1b[0m │ import {  Fallback, Image } from \"@/components/ui/avatar\"\n \x1b[2m10\x1b[0m │ import {\n \x1b[2m11\x1b[0m │   Quote,\n \x1b[2m12\x1b[0m │   import {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m13\x1b[0m │   ArrowLeft,\n \x1b[2m14\x1b[0m │   ArrowRight,\n \x1b[2m15\x1b[0m │   ArrowLeft\n    ╰────\n\n\nCaused by:\n    Syntax Error")},89752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return E},createPrefetchURL:function(){return R},default:function(){return S},isExternalURL:function(){return P}});let a=r(40740),n=r(60687),l=a._(r(43210)),o=r(22142),s=r(59154),i=r(57391),c=r(10449),u=r(19129),d=a._(r(35656)),f=r(35416),p=r(96127),h=r(77022),m=r(67086),b=r(44397),g=r(89330),x=r(25942),y=r(26736),v=r(70642),w=r(12776),j=r(63690),k=r(36875),_=r(97860);r(73406);let N={};function P(e){return e.origin!==window.location.origin}function R(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return P(t)?null:t}function M(e){let{appRouterState:t}=e;return(0,l.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:a}=t,n={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,i.createHrefFromUrl)(new URL(window.location.href))!==a?(r.pendingPush=!1,window.history.pushState(n,"",a)):window.history.replaceState(n,"",a)},[t]),(0,l.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function E(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function T(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let a=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return a&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=a),e}function O(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,a=null!==t?t.prefetchHead:null,n=null!==a?a:r;return(0,l.useDeferredValue)(r,n)}function C(e){let t,{actionQueue:r,assetPrefix:a,globalError:i}=e,f=(0,u.useActionQueue)(r),{canonicalUrl:p}=f,{searchParams:w,pathname:P}=(0,l.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,y.hasBasePath)(e.pathname)?(0,x.removeBasePath)(e.pathname):e.pathname}},[p]);(0,l.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(N.pendingMpaPath=void 0,(0,u.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,l.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,_.isRedirectError)(t)){e.preventDefault();let r=(0,k.getURLFromRedirectError)(t);(0,k.getRedirectTypeFromError)(t)===_.RedirectType.push?j.publicAppRouterInstance.push(r,{}):j.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:R}=f;if(R.mpaNavigation){if(N.pendingMpaPath!==p){let e=window.location;R.pendingPush?e.assign(p):e.replace(p),N.pendingMpaPath=p}(0,l.use)(g.unresolvedThenable)}(0,l.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,a=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,l.startTransition)(()=>{(0,u.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:a})})};window.history.pushState=function(t,a,n){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=T(t),n&&r(n)),e(t,a,n)},window.history.replaceState=function(e,a,n){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=T(e),n&&r(n)),t(e,a,n)};let a=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,l.startTransition)(()=>{(0,j.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",a),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",a)}},[]);let{cache:E,tree:C,nextUrl:S,focusAndScrollRef:A}=f,L=(0,l.useMemo)(()=>(0,b.findHeadInCache)(E,C[1]),[E,C]),U=(0,l.useMemo)(()=>(0,v.getSelectedParams)(C),[C]),D=(0,l.useMemo)(()=>({parentTree:C,parentCacheNode:E,parentSegmentPath:null,url:p}),[C,E,p]),I=(0,l.useMemo)(()=>({tree:C,focusAndScrollRef:A,nextUrl:S}),[C,A,S]);if(null!==L){let[e,r]=L;t=(0,n.jsx)(O,{headCacheNode:e},r)}else t=null;let H=(0,n.jsxs)(m.RedirectBoundary,{children:[t,E.rsc,(0,n.jsx)(h.AppRouterAnnouncer,{tree:C})]});return H=(0,n.jsx)(d.ErrorBoundary,{errorComponent:i[0],errorStyles:i[1],children:H}),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(M,{appRouterState:f}),(0,n.jsx)(z,{}),(0,n.jsx)(c.PathParamsContext.Provider,{value:U,children:(0,n.jsx)(c.PathnameContext.Provider,{value:P,children:(0,n.jsx)(c.SearchParamsContext.Provider,{value:w,children:(0,n.jsx)(o.GlobalLayoutRouterContext.Provider,{value:I,children:(0,n.jsx)(o.AppRouterContext.Provider,{value:j.publicAppRouterInstance,children:(0,n.jsx)(o.LayoutRouterContext.Provider,{value:D,children:H})})})})})})]})}function S(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,a],assetPrefix:l}=e;return(0,w.useNavFailureHandler)(),(0,n.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,n.jsx)(C,{actionQueue:t,assetPrefix:l,globalError:[r,a]})})}let A=new Set,L=new Set;function z(){let[,e]=l.default.useState(0),t=A.size;return(0,l.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==A.size&&r(),()=>{L.delete(r)}},[t,e]),[...A].map((e,t)=>(0,n.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=A.size;return A.add(e),A.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return l}});let a=r(98834),n=r(54674);function l(e,t){return(0,n.normalizePathTrailingSlash)((0,a.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return n}});let a=r(25232);function n(e,t,r){return(0,a.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,l){let o=l.length<=2,[s,i]=l,c=(0,n.createRouterCacheKey)(i),u=r.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==u||(d=new Map(u),t.parallelRoutes.set(s,d));let f=null==u?void 0:u.get(c),p=d.get(c);if(o){p&&p.lazyData&&p!==f||d.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!f){p||d.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(c,p)),e(p,f,(0,a.getNextFlightSegmentPath)(l))}}});let a=r(74007),n=r(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return a}}),r(59008),r(57391),r(86770),r(2030),r(25232),r(59435),r(56928),r(89752),r(96493),r(68214);let a=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return n}});let a=r(19169);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:l}=(0,a.parsePath)(e);return""+t+r+n+l}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,3206,2558],()=>r(33274));module.exports=a})();