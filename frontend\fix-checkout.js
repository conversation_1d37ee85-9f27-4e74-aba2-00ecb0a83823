const fs = require('fs');

// ✅ إصلاح حرج: Script لإصلاح checkout page

function fixCheckoutPage() {
  const filePath = 'src/app/checkout/page.tsx';
  console.log(`Fixing checkout page...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // إصلاح checkout references
  content = content.replace(/checkout \./g, '(checkoutData as any).');
  
  // إصلاح broken JSX tags
  content = content.replace(/< className="arabic-text">/g, '<CardDescription className="arabic-text">');
  content = content.replace(/<\/ >/g, '</CardDescription>');
  content = content.replace(/< htmlFor="([^"]+)" className="arabic-text">([^<]+)<\/ >/g, '<label htmlFor="$1" className="arabic-text">$2</label>');
  content = content.replace(/< id="([^"]+)"/g, '<Textarea id="$1"');
  content = content.replace(/< box/g, '<Checkbox');
  content = content.replace(/on edChange=/g, 'onCheckedChange=');

  // إصلاح specific broken patterns
  content = content.replace(/<\/ >\s*<\/div>/g, '</CardDescription>\n                            </div>');
  content = content.replace(/<\/ >\s*<\/label>/g, '</label>');
  content = content.replace(/< htmlFor=/g, '<label htmlFor=');
  content = content.replace(/< id=/g, '<Textarea id=');
  
  // إصلاح broken icons
  content = content.replace(/< className="h-4 w-4"/g, '<Check className="h-4 w-4"');
  content = content.replace(/< className="h-5 w-5"/g, '<User className="h-5 w-5"');
  content = content.replace(/case ' ': return < className="h-4 w-4" \/>/g, 'case "User": return <User className="h-4 w-4" />');
  content = content.replace(/case ' ': return < className="h-4 w-4" \/>/g, 'case "Check": return <Check className="h-4 w-4" />');
  content = content.replace(/default: return < className="h-4 w-4" \/>/g, 'default: return <User className="h-4 w-4" />');
  
  // إصلاح step icons
  content = content.replace(/{ id: 1, name: 'معلومات العميل', icon: ' ' }/g, '{ id: 1, name: "معلومات العميل", icon: "User" }');
  content = content.replace(/{ id: 4, name: 'مراجعة الطلب', icon: ' ' }/g, '{ id: 4, name: "مراجعة الطلب", icon: "Check" }');
  
  // إصلاح imports
  if (!content.includes('import { Textarea }')) {
    content = content.replace(
      'import { RadioGroup, RadioGroupItem } from \'@/components/ui/radio-group\'',
      'import { RadioGroup, RadioGroupItem } from \'@/components/ui/radio-group\'\nimport { Textarea } from \'@/components/ui/textarea\'\nimport { Checkbox } from \'@/components/ui/checkbox\'\nimport { CardDescription } from \'@/components/ui/card\''
    );
  }
  
  if (!content.includes('import { Check, User }')) {
    content = content.replace(
      'import {\n  GraduationCap,\n  Search,\n  Heart,\n  ShoppingCart,\n  Star,\n  Eye\n} from \'lucide-react\'',
      'import {\n  GraduationCap,\n  Search,\n  Heart,\n  ShoppingCart,\n  Star,\n  Eye,\n  Check,\n  User,\n  Truck,\n  CreditCard,\n  ArrowRight\n} from \'lucide-react\''
    );
  }
  
  // إصلاح broken tags
  content = content.replace(/<CardDescription className="arabic-text">/g, '<CardDescription className="arabic-text">');
  content = content.replace(/<label htmlFor=/g, '<label htmlFor=');
  content = content.replace(/<Textarea id=/g, '<Textarea id=');
  content = content.replace(/<Checkbox/g, '<Checkbox');
  
  // إصلاح any types
  content = content.replace(/value: any/g, 'value: unknown');
  content = content.replace(/Record<string, any>/g, 'Record<string, unknown>');
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`Fixed checkout page`);
}

try {
  fixCheckoutPage();
  console.log('✅ Checkout page fixes completed!');
} catch (error) {
  console.error('Error fixing checkout page:', error.message);
}
