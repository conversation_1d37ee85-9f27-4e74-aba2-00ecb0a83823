
"use client"

import * as React from "react"
import { useTheme } from "next-themes"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  import {
  Moon,
  Sun,
  Moon
} from 'lucide-react'

export function ThemeToggle() {
  const { setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
