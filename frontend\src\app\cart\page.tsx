
"use client"

import { useState } from 'react'
import { useCart } from '@/contexts/CartContext'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { PageLayout } from '@/components/layouts/PageLayout'
import { EnhancedImage } from '@/components/ui/enhanced-image'

import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  Plus,
  ShoppingCart,
  ArrowRight,
  Plus
} from 'lucide-react'

export default function CartPage() {
  const { 
    cartItems, 
    cartCount, 
    removeFromCart, 
    updateQuantity, 
    clearCart, 
    getCartTotal 
  } = useCart()

  const [isLoading, setIsLoading] = useState(false)

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(productId)
    } else {
      updateQuantity(productId, newQuantity)
    }
  }

  const handleRemoveItem = (productId: string) => {
    removeFromCart(productId)
  }

  const handleClearCart = () => {
    clearCart()
  }

  const handleCheckout = () => {
    setIsLoading(true)
    // التوجيه المباشر لصفحة الدفع
    setTimeout(() => {
      setIsLoading(false)
      window.location.href = '/checkout'
    }, 500)
  }

  if (cartCount === 0) {
    return (
      <PageLayout containerClassName="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <ShoppingCart className="h-24 w-24 text-gray-400 mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            السلة فارغة
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-8 arabic-text">
            لم تقم بإضافة أي منتجات للسلة بعد
          </p>
          <Button asChild>
            <a href="/catalog">
              <ShoppingBag className="h-4 w-4 mr-2" />
              تصفح المنتجات
            </a>
          </Button>
        </div>
      </PageLayout>
    )
  }

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8">
        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            🛒 سلة التسوق
          </h1>
          <p className="text-gray-600 dark:text-gray-400 arabic-text">
            لديك {cartCount} منتج في السلة
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cartItems.map((item) => (
              <Card key={`${item.id}-${item.type}`} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                      <EnhancedImage
                        src={item.image}
                        alt={item.name}
                        width={120}
                        height={120}
                        className="w-20 h-20 md:w-24 md:h-24 object-cover rounded-lg border"
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-gray-900 dark:text-white arabic-text line-clamp-2">
                          {item.name}
                        </h3>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleRemoveItem(item.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="flex items-center gap-2 mb-3">
                        <Badge variant={item.type === 'rental' ? 'secondary' : 'default'}>
                          {item.type === 'rental' ? 'إيجار' : 'شراء'}
                        </Badge>
                        <span className="text-lg font-bold text-blue-600">
                          {item.price} Dhs
                        </span>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                          الكمية:
                        </span>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center font-medium">
                            {item.quantity}
                          </span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        <span className="text-sm text-gray-500 mr-auto">
                          المجموع: {(item.price * item.quantity).toFixed(2)} Dhs
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Clear Cart Button */}
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={handleClearCart}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                إفراغ السلة
              </Button>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="arabic-text">ملخص الطلب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="arabic-text">عدد المنتجات:</span>
                    <span>{cartCount}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="arabic-text">المجموع الفرعي:</span>
                    <span>{getCartTotal().toFixed(2)} Dhs</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="arabic-text">الشحن:</span>
                    <span className="text-green-600">مجاني</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold text-lg">
                      <span className="arabic-text">المجموع الكلي:</span>
                      <span className="text-blue-600">{getCartTotal().toFixed(2)} Dhs</span>
                    </div>
                  </div>
                </div>

                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={handleCheckout}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    'جاري المعالجة...'
                  ) : (
                    <>
                      <ArrowRight className="h-4 w-4 mr-2" />
                      متابعة للدفع
                    </>
                  )}
                </Button>

                <Button variant="outline" className="w-full" asChild>
                  <a href="/catalog">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    متابعة التسوق
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageLayout>
  )
}
