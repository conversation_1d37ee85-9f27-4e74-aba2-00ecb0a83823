exports.id=8698,exports.ids=[8698],exports.modules={8827:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},26056:(e,t,r)=>{Promise.resolve().then(r.bind(r,54413))},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>s,_:()=>a});var o=r(60687),n=r(43210);r(37024);let i=(0,n.createContext)(void 0);function s({children:e}){let[t,r]=(0,n.useState)([]),[s,a]=(0,n.useState)([]),l=e=>{r(t=>t.filter(t=>t.id!==e))},d={cartItems:t,cartCount:t.reduce((e,t)=>e+t.quantity,0),addToCart:(e,t,o="purchase")=>{r(r=>r.find(t=>t.id===e&&t.type===o)?r.map(t=>t.id===e&&t.type===o?{...t,quantity:t.quantity+1}:t):[...r,{id:e,name:t.name,price:"rental"===o&&t.rental_price||t.price,image:t.images?.[0]||t.image||"/images/products/placeholder.jpg",quantity:1,type:o,rental_price:t.rental_price}])},removeFromCart:l,updateQuantity:(e,t)=>{if(t<=0)return void l(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},getCartTotal:()=>t.reduce((e,t)=>e+t.price*t.quantity,0),wishlistItems:s,wishlistCount:s.length,addToWishlist:(e,t)=>{a(r=>r.find(t=>t.id===e)?r:[...r,{id:e,name:t.name,price:t.price,image:t.images?.[0]||t.image||"/images/products/placeholder.jpg",rental_price:t.rental_price}])},removeFromWishlist:e=>{a(t=>t.filter(t=>t.id!==e))},isInWishlist:e=>s.some(t=>t.id===e),clearWishlist:()=>{a([])}};return(0,o.jsx)(i.Provider,{value:d,children:e})}function a(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},37024:(e,t,r)=>{"use strict";r.d(t,{ZG:()=>a});let o="AES-GCM";async function n(e,t){let r=new TextEncoder,n=await crypto.subtle.importKey("raw",r.encode(e),"PBKDF2",!1,["deriveKey"]);return crypto.subtle.deriveKey({name:"PBKDF2",salt:t,iterations:1e5,hash:"SHA-256"},n,{name:o,length:256},!1,["encrypt","decrypt"])}async function i(e){try{let t=new TextEncoder,r=JSON.stringify(e),i=t.encode(r),s=crypto.getRandomValues(new Uint8Array(16)),a=crypto.getRandomValues(new Uint8Array(12)),l=process.env.NEXT_PUBLIC_ENCRYPTION_KEY||"graduation-toqs-2024-secure-key",d=await n(l,s),c=await crypto.subtle.encrypt({name:o,iv:a},d,i),u=new Uint8Array(s.length+a.length+c.byteLength);return u.set(s,0),u.set(a,s.length),u.set(new Uint8Array(c),s.length+a.length),btoa(String.fromCharCode(...u))}catch(e){throw Error("Failed to encrypt data")}}async function s(e){try{let t=new Uint8Array(atob(e).split("").map(e=>e.charCodeAt(0))),r=t.slice(0,16),i=t.slice(16,28),s=t.slice(28),a=process.env.NEXT_PUBLIC_ENCRYPTION_KEY||"graduation-toqs-2024-secure-key",l=await n(a,r),d=await crypto.subtle.decrypt({name:o,iv:i},l,s),c=new TextDecoder().decode(d);return JSON.parse(c)}catch(e){return null}}let a={async setItem(e,t){try{let r=await i(t);localStorage.setItem(e,r)}catch(o){let r=function(e){try{let t=JSON.stringify(e);return btoa(encodeURIComponent(t))}catch(e){return""}}(t);r&&localStorage.setItem(e,r)}},async getItem(e){try{let t=localStorage.getItem(e);if(!t)return null;let r=await s(t);if(null!==r)return r;try{let e=decodeURIComponent(atob(t));return JSON.parse(e)}catch(e){return null}}catch(e){return null}},removeItem(e){localStorage.removeItem(e)},clear(){localStorage.clear()}}},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\CartContext.tsx","CartProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\CartContext.tsx","useCart")},42629:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx\x1b[0m:12:1]\n \x1b[2m 9\x1b[0m │ import { ScrollArea } from '@/components/ui/scroll-area'\n \x1b[2m10\x1b[0m │ import {\n \x1b[2m11\x1b[0m │   Send,\n \x1b[2m12\x1b[0m │   import {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m13\x1b[0m │ import { Label } from '@/components/ui/label'\n \x1b[2m14\x1b[0m │ import { Input } from '@/components/ui/input'\n \x1b[2m15\x1b[0m │ import { Textarea } from '@/components/ui/textarea'\n    ╰────\n\n\nCaused by:\n    Syntax Error")},46952:(e,t,r)=>{"use strict";r.d(t,{NotificationProvider:()=>a});var o=r(60687),n=r(43210),i=r(63213);let s=(0,n.createContext)(void 0);function a({children:e}){let{user:t}=(0,i.A)(),[r,a]=(0,n.useState)([]);(0,n.useCallback)(()=>{a([{id:"1",type:"order_confirmed",title:"تم تأكيد طلبك",message:"تم تأكيد طلبك #GT-********** بنجاح وسيتم تحضيره قريباً",priority:"high",isRead:!1,createdAt:new Date(Date.now()-72e5).toISOString(),actionUrl:"/track-order",actionText:"تتبع الطلب",userId:t?.id||"",metadata:{orderId:"GT-**********"}},{id:"2",type:"promotion",title:"عرض خاص - خصم 20%",message:"احصل على خصم 20% على جميع أزياء التخرج لفترة محدودة",priority:"medium",isRead:!1,createdAt:new Date(Date.now()-144e5).toISOString(),expiresAt:new Date(Date.now()+6048e5).toISOString(),actionUrl:"/catalog",actionText:"تسوق الآن",userId:t?.id||"",metadata:{promoCode:"GRAD20"}},{id:"3",type:"order_shipped",title:"تم شحن طلبك",message:"طلبك #GT-240115-002 في طريقه إليك. رقم التتبع: TRK-123456",priority:"high",isRead:!0,createdAt:new Date(Date.now()-864e5).toISOString(),actionUrl:"/track-order",actionText:"تتبع الشحنة",userId:t?.id||"",metadata:{orderId:"GT-240115-002",trackingNumber:"TRK-123456"}},{id:"4",type:"review_request",title:"قيم تجربتك معنا",message:"نود معرفة رأيك في المنتجات التي استلمتها مؤخراً",priority:"low",isRead:!1,createdAt:new Date(Date.now()-2592e5).toISOString(),actionUrl:"/reviews",actionText:"اكتب تقييم",userId:t?.id||""}])},[t]),(0,n.useCallback)(()=>{let e=["system","promotion","reminder"],t=e[Math.floor(Math.random()*e.length)],r={system:{title:"تحديث النظام",message:"تم تحديث النظام بميزات جديدة"},promotion:{title:"عرض محدود",message:"خصم خاص على المنتجات المختارة"},reminder:{title:"تذكير",message:"لا تنس إكمال طلبك في سلة التسوق"}};l({type:t,title:r[t].title,message:r[t].message,priority:"medium"})},[]);let l=e=>{let r={...e,id:Date.now().toString(),createdAt:new Date().toISOString(),userId:t?.id||"",isRead:!1};a(e=>[r,...e]),"granted"===Notification.permission&&new Notification(r.title,{body:r.message,icon:"/favicon.ico",tag:r.id})},d=()=>r.filter(e=>!e.isRead),c=d().length;return(0,o.jsx)(s.Provider,{value:{notifications:r,unreadCount:c,addNotification:l,markAsRead:e=>{a(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},markAllAsRead:()=>{a(e=>e.map(e=>({...e,isRead:!0})))},removeNotification:e=>{a(t=>t.filter(t=>t.id!==e))},clearAll:()=>{a([])},getNotificationsByType:e=>r.filter(t=>t.type===e),getUnreadNotifications:d},children:e})}},53881:(e,t,r)=>{"use strict";r.d(t,{gG:()=>o,ly:()=>n});var o=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});function n(e,t){let r={admin:4,school:3,delivery:2,student:1};return r[e]>=r[t]}},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx","default")},54431:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got '{'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx\x1b[0m:12:1]\n \x1b[2m 9\x1b[0m │ import { ErrorLayout } from '@/components/layouts/PageLayout'\n \x1b[2m10\x1b[0m │ import {\n \x1b[2m11\x1b[0m │   RefreshCw,\n \x1b[2m12\x1b[0m │   import {\n    \xb7 \x1b[35;1m         ─\x1b[0m\n \x1b[2m13\x1b[0m │   Home,\n \x1b[2m14\x1b[0m │   ArrowLeft,\n \x1b[2m15\x1b[0m │   GraduationCap,\n    ╰────\n\n\nCaused by:\n    Syntax Error")},57347:()=>{throw Error("Module parse failed: Identifier 'RefreshCw' has already been declared (10:20)\nFile was processed with these loaders:\n * ./node_modules/next/dist/build/webpack/loaders/next-flight-client-module-loader.js\n * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js\nYou may need an additional loader to handle the result of these loaders.\n| import { LanguageToggle } from '@/components/language-toggle';\n| import { useTranslation } from '@/hooks/useTranslation';\n> import { RefreshCw, RefreshCw } from \"__barrel_optimize__?names=RefreshCw!=!lucide-react\";\n| export default function NotFound() {\n|     const router = useRouter();")},61971:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>l});var o=r(60687),n=r(43210),i=r(53881),s=r(37024);let a=(0,n.createContext)(void 0);function l({children:e}){let[t,r]=(0,n.useState)(null),[l,d]=(0,n.useState)(null),[c,u]=(0,n.useState)(!0),[m,p]=(0,n.useState)(!1),f=async(e,t,r)=>({data:{user:{id:"1",email:e}},error:null}),h=async(e,t)=>{let o={id:"1",email:e},n=i.gG.STUDENT;e.includes("admin")?n=i.gG.ADMIN:e.includes("school")?n=i.gG.SCHOOL:e.includes("delivery")&&(n=i.gG.DELIVERY);let a={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:n,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};r(o),d(a);try{await s.ZG.setItem("user_session",o),await s.ZG.setItem("user_profile",a),await s.ZG.setItem("session_timestamp",Date.now().toString())}catch(e){}return setTimeout(()=>{n===i.gG.ADMIN?window.location.href="/dashboard/admin":n===i.gG.SCHOOL?window.location.href="/dashboard/school":n===i.gG.DELIVERY?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:o},error:null}},b=async()=>{try{return r(null),d(null),s.ZG.removeItem("user_session"),s.ZG.removeItem("user_profile"),s.ZG.removeItem("session_timestamp"),{error:null}}catch(e){return{error:"فشل في تسجيل الخروج"}}},v=async e=>{if(!t)return{data:null,error:"No user logged in"};let r={...l,...e};return d(r),{data:r,error:null}};return(0,o.jsx)(a.Provider,{value:{user:t,profile:l,loading:c,signUp:f,signIn:h,signOut:b,updateProfile:v,hasRole:e=>!!l&&(0,i.ly)(l.role,e)},children:e})}function d(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67270:(e,t,r)=>{"use strict";r.d(t,{MenuProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call MenuProvider() from the server but MenuProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\MenuContext.tsx","MenuProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useMenu() from the server but useMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\MenuContext.tsx","useMenu")},68272:(e,t,r)=>{Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,83701)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,67270)),Promise.resolve().then(r.bind(r,81326))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var o=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78e3:(e,t,r)=>{Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,96871)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,86748)),Promise.resolve().then(r.bind(r,46952))},78335:()=>{},81326:(e,t,r)=>{"use strict";r.d(t,{NotificationProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","NotificationProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","useNotifications"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getNotificationIcon() from the server but getNotificationIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","getNotificationIcon"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getNotificationColor() from the server but getNotificationColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","getNotificationColor"),(0,o.registerClientReference)(function(){throw Error("Attempted to call formatNotificationTime() from the server but formatNotificationTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","formatNotificationTime")},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-provider.tsx","ThemeProvider")},86748:(e,t,r)=>{"use strict";r.d(t,{MenuProvider:()=>a,b:()=>l});var o=r(60687),n=r(43210),i=r(52581);let s=(0,n.createContext)(void 0);function a({children:e}){let[t,r]=(0,n.useState)([]),[a,l]=(0,n.useState)(!0),[d,c]=(0,n.useState)(null),u=(0,n.useCallback)(async(e=!1)=>{try{l(!0),c(null);let t=await fetch(e?"/api/menu-items?include_inactive=true":"/api/menu-items"),o=await t.json();t.ok?r(o.menuItems||[]):c(o.error||"فشل في جلب عناصر القائمة")}catch(e){c("خطأ في الاتصال بالخادم")}finally{l(!1)}},[]),m=(0,n.useCallback)(async e=>{try{let t=await fetch("/api/menu-items",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),o=await t.json();if(t.ok)return r(e=>[...e,o.menuItem]),i.o.success(o.message),!0;return i.o.error(o.error||"فشل في إضافة عنصر القائمة"),!1}catch(e){return i.o.error("خطأ في الاتصال بالخادم"),!1}},[]),p=(0,n.useCallback)(async(e,o)=>{try{let n=t.find(t=>t.id===e);if(!n)return i.o.error("عنصر القائمة غير موجود"),!1;let s={...n,...o},a=await fetch(`/api/menu-items/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),l=await a.json();if(a.ok)return r(t=>t.map(t=>t.id===e?{...t,...o}:t)),i.o.success(l.message),!0;return i.o.error(l.error||"فشل في تحديث عنصر القائمة"),!1}catch(e){return i.o.error("خطأ في الاتصال بالخادم"),!1}},[t]),f=(0,n.useCallback)(async e=>{try{let t=await fetch(`/api/menu-items/${e}`,{method:"DELETE"}),o=await t.json();if(t.ok)return r(t=>t.filter(t=>t.id!==e)),i.o.success(o.message),!0;return i.o.error(o.error||"فشل في حذف عنصر القائمة"),!1}catch(e){return i.o.error("خطأ في الاتصال بالخادم"),!1}},[]),h=(0,n.useCallback)(async e=>{let r=t.find(t=>t.id===e);return r?await p(e,{is_active:!r.is_active}):(i.o.error("عنصر القائمة غير موجود"),!1)},[t,p]),b=(0,n.useCallback)(async e=>{try{r(e);let t=e.filter(e=>!e.parent_id).map((e,t)=>({id:e.id,order_index:t+1})),o=await fetch("/api/menu-items/reorder",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:t})}),n=await o.json();if(o.ok)return i.o.success(n.message),!0;return await u(),i.o.error(n.error||"فشل في إعادة ترتيب عناصر القائمة"),!1}catch(e){return await u(),i.o.error("خطأ في الاتصال بالخادم"),!1}},[u]),v=(0,n.useCallback)(()=>{u()},[u]),C=(0,n.useMemo)(()=>({menuItems:t,loading:a,error:d,fetchMenuItems:u,addMenuItem:m,updateMenuItem:p,deleteMenuItem:f,toggleItemStatus:h,reorderMenuItems:b,refreshMenu:v}),[t,a,d,u,m,p,f,h,b,v]);return(0,o.jsx)(s.Provider,{value:C,children:e})}function l(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useMenu must be used within a MenuProvider");return e}},89608:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,57347,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>p});var o=r(37413),n=r(83701),i=r(29131),s=r(81326),a=r(37043),l=r(67270),d=r(42629),c=r(6931);let u=Cairo({variable:"--font-cairo",subsets:["arabic","latin"],display:"swap"}),m=Geist_Mono({variable:"--font-geist-mono",subsets:["latin"]}),p={title:"Graduation Toqs - منصة أزياء التخرج المغربية",description:"أول منصة مغربية ذكية لتأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"};function f({children:e}){return(0,o.jsx)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:`${u.variable} ${m.variable} antialiased font-cairo`,children:(0,o.jsx)(n.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,o.jsx)(i.AuthProvider,{children:(0,o.jsx)(s.NotificationProvider,{children:(0,o.jsxs)(l.MenuProvider,{children:[(0,o.jsx)(a.CartProvider,{children:e}),(0,o.jsx)(d.LiveChat,{}),(0,o.jsx)(c.Toaster,{position:"top-right",dir:"rtl",richColors:!0,closeButton:!0})]})})})})})})}},96487:()=>{},96871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var o=r(60687);r(43210);var n=r(10218);function i({children:e,...t}){return(0,o.jsx)(n.N,{...t,children:e})}}};