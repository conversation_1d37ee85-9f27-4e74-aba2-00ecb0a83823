#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define the patterns to fix
const fixes = [
  // Fix broken variable names
  { pattern: /\bis ing\b/g, replacement: 'isLoading' },
  { pattern: /\bset ing\b/g, replacement: 'setLoading' },
  { pattern: /\bset Type\b/g, replacement: 'setFilterType' },
  { pattern: /\bupload set \b/g, replacement: 'uploadProgress' },
  { pattern: /\bset \(/g, replacement: 'setUploadProgress(' },
  { pattern: /\bImage er\b/g, replacement: 'ImageUploader' },
  { pattern: /\bHTML Element\b/g, replacement: 'HTMLInputElement' },
  { pattern: /\bOrderStatus Props\b/g, replacement: 'OrderStatusProps' },
  { pattern: /\bshow \b/g, replacement: 'showProgress' },
  { pattern: /\bon edChange\b/g, replacement: 'onCheckedChange' },
  { pattern: /\bhandle \b/g, replacement: 'handleFileUpload' },
  { pattern: /\bgetStatus \b/g, replacement: 'getStatusProgress' },

  // Fix JSX elements with missing tag names - common components
  { pattern: /< className="([^"]*)" \/>/g, replacement: '<div className="$1" />' },
  { pattern: /< className="([^"]*)">/g, replacement: '<div className="$1">' },
  { pattern: /<\/ >/g, replacement: '</div>' },
  
  // Fix specific component patterns
  { pattern: /< htmlFor="([^"]*)" className="([^"]*)">([^<]*)<\/ >/g, replacement: '<Label htmlFor="$1" className="$2">$3</Label>' },
  { pattern: /< id="([^"]*)"([^>]*)\/>/g, replacement: '<Input id="$1"$2/>' },
  { pattern: /< id="([^"]*)"([^>]*)>/g, replacement: '<Input id="$1"$2>' },
  { pattern: /< value=\{([^}]*)\}([^>]*)\/>/g, replacement: '<Input value={$1}$2/>' },
  { pattern: /< value=\{([^}]*)\}([^>]*)>/g, replacement: '<Select value={$1}$2>' },
  
  // Fix Tabs components
  { pattern: /< defaultValue="([^"]*)" className="([^"]*)">/, replacement: '<Tabs defaultValue="$1" className="$2">' },
  { pattern: /< List className="([^"]*)">/, replacement: '<TabsList className="$1">' },
  { pattern: /< Trigger value="([^"]*)" className="([^"]*)">/, replacement: '<TabsTrigger value="$1" className="$2">' },
  { pattern: /<\/ Trigger>/g, replacement: '</TabsTrigger>' },
  { pattern: /<\/ List>/g, replacement: '</TabsList>' },
  { pattern: /< Content value="([^"]*)" className="([^"]*)">/, replacement: '<TabsContent value="$1" className="$2">' },
  { pattern: /<\/ Content>/g, replacement: '</TabsContent>' },
  
  // Fix Select components
  { pattern: /< Trigger([^>]*)>/, replacement: '<SelectTrigger$1>' },
  { pattern: /<\/ Trigger>/g, replacement: '</SelectTrigger>' },
  { pattern: /< Value([^>]*)\/>/g, replacement: '<SelectValue$1/>' },
  { pattern: /< Content>/g, replacement: '<SelectContent>' },
  { pattern: /< Item([^>]*)>/g, replacement: '<SelectItem$1>' },
  { pattern: /<\/ Item>/g, replacement: '</SelectItem>' },
  
  // Fix Badge and Progress components
  { pattern: /< variant="([^"]*)"([^>]*)>/g, replacement: '<Badge variant="$1"$2>' },
  { pattern: /< value=\{([^}]*)\} className="([^"]*)"\s*\/>/g, replacement: '<Progress value={$1} className="$2" />' },
  
  // Fix common icon patterns
  { pattern: /< className="h-4 w-4([^"]*)" \/>/g, replacement: '<AlertCircle className="h-4 w-4$1" />' },
  { pattern: /< className="h-12 w-12([^"]*)" \/>/g, replacement: '<Upload className="h-12 w-12$1" />' },
];

// Import patterns to add
const importFixes = [
  {
    pattern: /import.*from '@\/components\/ui\/card'/,
    check: /\b(Label|Input|Textarea|Select|Badge|Progress)\b/,
    imports: [
      "import { Label } from '@/components/ui/label'",
      "import { Input } from '@/components/ui/input'", 
      "import { Textarea } from '@/components/ui/textarea'",
      "import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'",
      "import { Badge } from '@/components/ui/badge'",
      "import { Progress } from '@/components/ui/progress'"
    ]
  },
  {
    pattern: /import.*from '@\/components\/ui\/tabs'/,
    check: /\b(Tabs|TabsList|TabsTrigger|TabsContent)\b/,
    imports: [
      "import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'"
    ]
  },
  {
    pattern: /import.*from 'lucide-react'/,
    check: /\b(AlertCircle|Upload|Plus|Save|Eye)\b/,
    imports: [
      "import { AlertCircle, Upload, Plus, Save, Eye } from 'lucide-react'"
    ]
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply all fixes
    fixes.forEach(fix => {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        modified = true;
      }
    });
    
    // Add missing imports
    importFixes.forEach(importFix => {
      if (importFix.check.test(content)) {
        const hasExistingImport = importFix.pattern.test(content);
        if (!hasExistingImport) {
          // Find the last import statement
          const importLines = content.split('\n');
          let lastImportIndex = -1;
          
          for (let i = 0; i < importLines.length; i++) {
            if (importLines[i].trim().startsWith('import ')) {
              lastImportIndex = i;
            }
          }
          
          if (lastImportIndex >= 0) {
            importFix.imports.forEach(importStatement => {
              if (!content.includes(importStatement)) {
                importLines.splice(lastImportIndex + 1, 0, importStatement);
                lastImportIndex++;
                modified = true;
              }
            });
            
            content = importLines.join('\n');
          }
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Starting systematic syntax error fixes...');
console.log(`📁 Scanning directory: ${srcDir}`);

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} TypeScript/TSX files`);

let fixedCount = 0;
let totalCount = tsxFiles.length;

tsxFiles.forEach(file => {
  if (fixFile(file)) {
    fixedCount++;
  }
});

console.log('\n🎉 Fix Summary:');
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`📄 Total files: ${totalCount}`);
console.log(`⏭️  Files unchanged: ${totalCount - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n🚀 Run "npm run type-check" to verify fixes!');
} else {
  console.log('\n✨ All files are already in good shape!');
}
