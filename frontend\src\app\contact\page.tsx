
"use client"

import { useState } from 'react'
import { PageLayout } from '@/components/layouts/PageLayout'
import { useTranslation } from '@/hooks/useTranslation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import {
  Clock,
  import {
import { toast
} from 'sonner'


import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
  Clock
} from 'lucide-react'

interface ContactForm {
  name: string
  email: string
  phone: string
  subject: string
  message: string
  category: 'general' | 'order' | 'support' | 'partnership'
}

export default function ContactPage() {
  const { } = useTranslation() // ✅ إصلاح: إزالة متغير غير مستخدم
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<ContactForm>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    category: 'general'
  })

  const contact = [
    {
      icon: <div className="h-6 w-6 text-blue-600" />,
      title: 'اتصل بنا',
      details: ['+212 6 12 34 56 78', '+212 5 23 45 67 89'],
      description: 'متاح من 9 صباحاً إلى 6 مساءً',
      action: 'tel:+212612345678'
    },
    {
      icon: <div className="h-6 w-6 text-green-600" />,
      title: 'راسلنا',
      details: ['<EMAIL>', '<EMAIL>'],
      description: 'نرد خلال 24 ساعة',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: <div className="h-6 w-6 text-red-600" />,
      title: 'زورنا',
      details: ['شارع الحسن الثاني، بني ملال', 'المغرب 23000'],
      description: 'من الاثنين إلى الجمعة',
      action: 'https://maps.google.com'
    },
    {
      icon: <Clock className="h-6 w-6 text-purple-600" />,
      title: 'ساعات العمل',
      details: ['الاثنين - الجمعة: 9:00 - 18:00', 'السبت: 10:00 - 16:00'],
      description: 'مغلق يوم الأحد',
      action: null
    }
  ]

  const categories = [
    { value: 'general', label: 'استفسار عام', color: 'bg-blue-100 text-blue-800' },
    { value: 'order', label: 'حول الطلبات', color: 'bg-green-100 text-green-800' },
    { value: 'support', label: 'دعم فني', color: 'bg-orange-100 text-orange-800' },
    { value: 'partnership', label: 'شراكة', color: 'bg-purple-100 text-purple-800' }
  ]

  const handleFileUploadChange = (field: keyof ContactForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // محاكاة إرسال النموذج
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', {
        description: 'شكراً لتواصلك معنا'
      })
      
      // إعادة تعيين النموذج
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
        category: 'general'
      })
    } catch (_error) {
      toast.error('حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <PageLayout>
      {/* Page Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
          📞 تواصل معنا
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text">
          نحن هنا لمساعدتك! تواصل معنا عبر أي من الطرق التالية أو املأ النموذج أدناه
        </p>
      </div>

      {/* Contact Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        {contact .map((info, index) => (
          <Card key={index} className="text-center hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex justify-center mb-4">
                {info.icon}
              </div>
              <h3 className="font-semibold text-lg mb-2 arabic-text">{info.title}</h3>
              <div className="space-y-1 mb-3">
                {info.details.map((detail, idx) => (
                  <p key={idx} className="text-gray-600 dark:text-gray-400 text-sm">
                    {detail}
                  </p>
                ))}
              </div>
              <p className="text-xs text-gray-500 mb-4 arabic-text">{info.description}</p>
              {info.action && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.open(info.action!, '_blank')}
                  className="arabic-text"
                >
                  {info.title === 'اتصل بنا' ? 'اتصل الآن' : 
                   info.title === 'راسلنا' ? 'إرسال بريد' : 'عرض الخريطة'}
                </Button>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Contact Form */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text flex items-center gap-2">
              <Send className="h-5 w-5" />
              إرسال رسالة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Category ion */}
              <div>
                <div className="arabic-text">نوع الاستفسار</div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {categories.map((category) => (
                    < key={category.value}
                      variant={formData.category === category.value ? "default" : "outline"}
                      className={`cursor-pointer ${
                        formData.category === category.value ? category.color : ''
                      }`}
                      onClick={() => handleFileUploadChange('category', category.value)}
                    >
                      {category.label}
                    </div>
                  ))}
                </div>
              </div>

              {/* Name and Email */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name" className="arabic-text">الاسم الكامل *</Label>
                  <Input id="name"
                    value={formData.name}
                    onChange={(e) => handleFileUploadChange('name', e.target.value)}
                    placeholder="أدخل اسمك الكامل"
                    required
                    className="arabic-text"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="arabic-text">البريد الإلكتروني *</Label>
                  <Input id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleFileUploadChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              {/* and Subject */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone" className="arabic-text">رقم الهاتف</Label>
                  <Input id="phone"
                    value={formData.phone}
                    onChange={(e) => handleFileUploadChange('phone', e.target.value)}
                    placeholder="+212 6 12 34 56 78"
                  />
                </div>
                <div>
                  <Label htmlFor="subject" className="arabic-text">الموضوع *</Label>
                  <Input id="subject"
                    value={formData.subject}
                    onChange={(e) => handleFileUploadChange('subject', e.target.value)}
                    placeholder="موضوع الرسالة"
                    required
                    className="arabic-text"
                  />
                </div>
              </div>

              {/* Message */}
              <div>
                <Label htmlFor="message" className="arabic-text">الرسالة *</Label>
                <Input id="message"
                  value={formData.message}
                  onChange={(e) => handleFileUploadChange('message', e.target.value)}
                  placeholder="اكتب رسالتك هنا..."
                  rows={5}
                  required
                  className="arabic-text"
                />
              </div>

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full arabic-text"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    جاري الإرسال...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    إرسال الرسالة
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Additional */}
        <div className="space-y-6">
          {/* FAQ Link */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="h-6 w-6 text-blue-600" />
                <h3 className="font-semibold arabic-text">الأسئلة الشائعة</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-4 arabic-text">
                قد تجد إجابة سؤالك في قسم الأسئلة الشائعة
              </p>
              <Button variant="outline" className="w-full arabic-text" asChild>
                <a href="/support#faq">تصفح الأسئلة الشائعة</a>
              </Button>
            </CardContent>
          </Card>

          {/* Live Chat */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="relative">
                  <div className="h-6 w-6 text-green-600" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <h3 className="font-semibold arabic-text">دردشة مباشرة</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-4 arabic-text">
                تحدث مع فريق الدعم مباشرة للحصول على مساعدة فورية
              </p>
              <Button className="w-full arabic-text bg-green-600 hover:bg-green-700">
                <div className="h-4 w-4 mr-2" />
                بدء المحادثة
              </Button>
            </CardContent>
          </Card>

          {/* Response Time */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="h-6 w-6 text-green-600" />
                <h3 className="font-semibold arabic-text">أوقات الاستجابة</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="arabic-text">الدردشة المباشرة:</span>
                  <span className="text-green-600">فوري</span>
                </div>
                <div className="flex justify-between">
                  <span className="arabic-text">البريد الإلكتروني:</span>
                  <span className="text-blue-600">خلال 24 ساعة</span>
                </div>
                <div className="flex justify-between">
                  <span className="arabic-text">المكالمات الهاتفية:</span>
                  <span className="text-purple-600">خلال ساعات العمل</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  )
}
