
"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { Switch } from '@/components/ui/switch'

import {
  import {
import { toast
} from 'sonner'




import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  AlertCircle,
  Plus,
  Save,
  X
} from 'lucide-react'

interface Product {
  id?: string
  name: string
  description: string
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  is_published: boolean
  features: string[]
  specifications: Record<string, unknown>
}

interface SimpleProductFormProps {
  product?: Product
  onSave: (product: Product) => void
  onCancel: () => void
  isLoading?: boolean
}

export function SimpleProductForm({ product, onSave, onCancel, isLoading = false }: SimpleProductFormProps) {
  const [formData, setFormData] = useState<Product>({
    name: '',
    description: '',
    price: 0,
    rental_price: undefined,
    colors: [],
    sizes: [],
    images: [],
    stock_quantity: 0,
    is_available: true,
    is_published: true,
    features: [],
    specifications: {},
    ...product
  })

  const [newColor, setNewColor] = useState('')
  const [newSize, setNewSize] = useState('')
  const [newFeature, setNewFeature] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  // التحقق من صحة النموذج
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المنتج مطلوب'
    }

    if (formData.price <= 0) {
      newErrors.price = 'السعر يجب أن يكون أكبر من صفر'
    }

    if (formData.stock_quantity < 0) {
      newErrors.stock_quantity = 'كمية المخزون لا يمكن أن تكون سالبة'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // إضافة لون جديد
  const addColor = () => {
    if (newColor.trim() && !formData.colors.includes(newColor.trim())) {
      setFormData(prev => ({
        ...prev,
        colors: [...prev.colors, newColor.trim()]
      }))
      setNewColor('')
    }
  }

  // إزالة لون
  const removeColor = (color: string) => {
    setFormData(prev => ({
      ...prev,
      colors: prev.colors.filter(c => c !== color)
    }))
  }

  // إضافة مقاس جديد
  const addSize = () => {
    if (newSize.trim() && !formData.sizes.includes(newSize.trim())) {
      setFormData(prev => ({
        ...prev,
        sizes: [...prev.sizes, newSize.trim()]
      }))
      setNewSize('')
    }
  }

  // إزالة مقاس
  const removeSize = (size: string) => {
    setFormData(prev => ({
      ...prev,
      sizes: prev.sizes.filter(s => s !== size)
    }))
  }

  // إضافة ميزة جديدة
  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }))
      setNewFeature('')
    }
  }

  // إزالة ميزة
  const removeFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }))
  }

  // حفظ المنتج
  const handleSave = () => {
    if (validateForm()) {
      onSave(formData)
    } else {
      toast.error('يرجى تصحيح الأخطاء في النموذج')
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="arabic-text">
          {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </CardTitle>
        <CardDescription className="arabic-text">
          {product ? 'تعديل بيانات المنتج الموجود' : 'إضافة منتج جديد إلى المتجر'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* المعلومات الأساسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="arabic-text">اسم المنتج *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="أدخل اسم المنتج"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.name}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="price" className="arabic-text">السعر (Dhs) *</Label>
            <Input
              id="price"
              type="number"
              min="0"
              step="0.01"
              value={formData.price}
              onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
              placeholder="0.00"
              className={errors.price ? 'border-red-500' : ''}
            />
            {errors.price && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.price}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="rental_price" className="arabic-text">سعر الإيجار (Dhs)</Label>
            <Input
              id="rental_price"
              type="number"
              min="0"
              step="0.01"
              value={formData.rental_price || ''}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                rental_price: e.target.value ? parseFloat(e.target.value) : undefined 
              }))}
              placeholder="0.00"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="stock_quantity" className="arabic-text">كمية المخزون *</Label>
            <Input
              id="stock_quantity"
              type="number"
              min="0"
              value={formData.stock_quantity}
              onChange={(e) => setFormData(prev => ({ ...prev, stock_quantity: parseInt(e.target.value) || 0 }))}
              placeholder="0"
              className={errors.stock_quantity ? 'border-red-500' : ''}
            />
            {errors.stock_quantity && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.stock_quantity}
              </p>
            )}
          </div>
        </div>

        {/* الوصف */}
        <div className="space-y-2">
          <Label htmlFor="description" className="arabic-text">وصف المنتج *</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="أدخل وصف مفصل للمنتج"
            rows={4}
            className={errors.description ? 'border-red-500' : ''}
          />
          {errors.description && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {errors.description}
            </p>
          )}
        </div>

        {/* الألوان */}
        <div className="space-y-3">
          <Label className="arabic-text">الألوان المتاحة</Label>
          <div className="flex gap-2">
            <Input
              value={newColor}
              onChange={(e) => setNewColor(e.target.value)}
              placeholder="أضف لون جديد"
              onKeyPress={(e) => e.key === 'Enter' && addColor()}
            />
            <Button type="button" onClick={addColor} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {formData.colors.map((color, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {color}
                <button
                  type="button"
                  onClick={() => removeColor(color)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>

        {/* المقاسات */}
        <div className="space-y-3">
          <Label className="arabic-text">المقاسات المتاحة</Label>
          <div className="flex gap-2">
            <Input
              value={newSize}
              onChange={(e) => setNewSize(e.target.value)}
              placeholder="أضف مقاس جديد"
              onKeyPress={(e) => e.key === 'Enter' && addSize()}
            />
            <Button type="button" onClick={addSize} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {formData.sizes.map((size, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {size}
                <button
                  type="button"
                  onClick={() => removeSize(size)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>

        {/* الميزات */}
        <div className="space-y-3">
          <Label className="arabic-text">ميزات المنتج</Label>
          <div className="flex gap-2">
            <Input
              value={newFeature}
              onChange={(e) => setNewFeature(e.target.value)}
              placeholder="أضف ميزة جديدة"
              onKeyPress={(e) => e.key === 'Enter' && addFeature()}
            />
            <Button type="button" onClick={addFeature} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {formData.features.map((feature, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {feature}
                <button
                  type="button"
                  onClick={() => removeFeature(feature)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>

        {/* الإعدادات */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="is_available"
              checked={formData.is_available}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_available: checked }))}
            />
            <Label htmlFor="is_available" className="arabic-text">متاح للبيع</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_published"
              checked={formData.is_published}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}
            />
            <Label htmlFor="is_published" className="arabic-text">منشور في المتجر</Label>
          </div>
        </div>

        {/* أزرار الحفظ والإلغاء */}
        <div className="flex justify-end gap-3 pt-6 border-t">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            <X className="h-4 w-4 mr-2" />
            إلغاء
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'جاري الحفظ...' : 'حفظ المنتج'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
