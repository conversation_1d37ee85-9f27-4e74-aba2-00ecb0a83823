(()=>{var e={};e.id=3654,e.ids=[3654],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75295:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>l});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(38561);async function p(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_unpublished"),s=t.get("language")||"ar",n=u.C.getPages();r||(n=n.filter(e=>e.is_published));let a=n.map(e=>({...e,page_content:e.page_content.filter(e=>e.language===s)}));return i.NextResponse.json({pages:a,total:a.length})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function l(e){try{let t=cookies(),r=createClient(t),{data:{user:s}}=await r.auth.getUser();if(!s)return i.NextResponse.json({error:"غير مصرح لك بالوصول"},{status:401});let{data:n}=await r.from("profiles").select("role").eq("id",s.id).single();if(!n||"admin"!==n.role)return i.NextResponse.json({error:"غير مصرح لك بهذا الإجراء"},{status:403});let{slug:a,is_published:o,featured_image:u,content:p}=await e.json();if(!a||!p||!p.ar||!p.ar.title||!p.ar.content)return i.NextResponse.json({error:"البيانات المطلوبة مفقودة"},{status:400});let{data:l}=await r.from("pages").select("id").eq("slug",a).single();if(l)return i.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let{data:d,error:c}=await r.from("pages").insert({slug:a,is_published:o??!1,featured_image:u||null,author_id:s.id}).select().single();if(c)return i.NextResponse.json({error:"فشل في إضافة الصفحة"},{status:500});let x=[];for(let[e,t]of Object.entries(p))t&&"object"==typeof t&&t.title&&t.content&&x.push({page_id:d.id,language:e,title:t.title,content:t.content,meta_description:t.meta_description||null,meta_keywords:t.meta_keywords||null});if(x.length>0){let{error:e}=await r.from("page_content").insert(x);if(e)return await r.from("pages").delete().eq("id",d.id),i.NextResponse.json({error:"فشل في إضافة محتوى الصفحة"},{status:500})}return i.NextResponse.json({message:"تم إضافة الصفحة بنجاح",page:d},{status:201})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/pages/route",pathname:"/api/pages",filename:"route",bundlePath:"app/api/pages/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:g}=d;function m(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(75295));module.exports=s})();