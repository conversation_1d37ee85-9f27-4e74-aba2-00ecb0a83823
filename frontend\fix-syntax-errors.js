#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define the patterns to fix
const fixes = [
  // Fix broken variable names
  { pattern: /\bis ing\b/g, replacement: 'isLoading' },
  { pattern: /\bset ing\b/g, replacement: 'setLoading' },
  { pattern: /\bset Type\b/g, replacement: 'setFilterType' },
  { pattern: /\bupload set \b/g, replacement: 'uploadProgress' },
  { pattern: /\bset \(/g, replacement: 'setUploadProgress(' },
  { pattern: /\bImage er\b/g, replacement: 'ImageUploader' },
  { pattern: /\bHTML Element\b/g, replacement: 'HTMLInputElement' },
  { pattern: /\bOrderStatus Props\b/g, replacement: 'OrderStatusProps' },
  { pattern: /\bshow \b/g, replacement: 'showProgress' },
  { pattern: /\bon edChange\b/g, replacement: 'onCheckedChange' },
  { pattern: /\bhandle \b/g, replacement: 'handleFileUpload' },
  { pattern: /\bgetStatus \b/g, replacement: 'getStatusProgress' },
  { pattern: /\bset edFiles\b/g, replacement: 'setSelectedFiles' },
  { pattern: /\bon File\b/g, replacement: 'onFileSelect' },
  { pattern: /\bsave Open\b/g, replacement: 'saveOpen' },
  { pattern: /\bset Open\b/g, replacement: 'setSaveOpen' },
  { pattern: /\bshare Open\b/g, replacement: 'shareOpen' },
  { pattern: /\bsetShare Open\b/g, replacement: 'setShareOpen' },
  { pattern: /\bgetPopularity s\b/g, replacement: 'getPopularityStars' },
  { pattern: /\bFolder Folder\b/g, replacement: 'Folder' },

  // Fix JSX elements with missing tag names - common components
  { pattern: /< className="([^"]*)" \/>/g, replacement: '<div className="$1" />' },
  { pattern: /< className="([^"]*)">/g, replacement: '<div className="$1">' },
  { pattern: /<\/ >/g, replacement: '</div>' },

  // Fix specific component patterns
  { pattern: /< htmlFor="([^"]*)" className="([^"]*)">([^<]*)<\/ >/g, replacement: '<Label htmlFor="$1" className="$2">$3</Label>' },
  { pattern: /< id="([^"]*)"([^>]*)\/>/g, replacement: '<Input id="$1"$2/>' },
  { pattern: /< id="([^"]*)"([^>]*)>/g, replacement: '<Input id="$1"$2>' },
  { pattern: /< value=\{([^}]*)\}([^>]*)\/>/g, replacement: '<Input value={$1}$2/>' },
  { pattern: /< value=\{([^}]*)\}([^>]*)>/g, replacement: '<Select value={$1}$2>' },
  { pattern: /< placeholder="([^"]*)"([^>]*)\/>/g, replacement: '<Input placeholder="$1"$2/>' },
  { pattern: /< placeholder="([^"]*)"([^>]*)>/g, replacement: '<Input placeholder="$1"$2>' },

  // Fix Tabs components
  { pattern: /< defaultValue="([^"]*)" className="([^"]*)">/, replacement: '<Tabs defaultValue="$1" className="$2">' },
  { pattern: /< List className="([^"]*)">/, replacement: '<TabsList className="$1">' },
  { pattern: /< Trigger value="([^"]*)" className="([^"]*)">/, replacement: '<TabsTrigger value="$1" className="$2">' },
  { pattern: /< Trigger([^>]*)>/, replacement: '<TabsTrigger$1>' },
  { pattern: /<\/ Trigger>/g, replacement: '</TabsTrigger>' },
  { pattern: /<\/ List>/g, replacement: '</TabsList>' },
  { pattern: /< Content value="([^"]*)" className="([^"]*)">/, replacement: '<TabsContent value="$1" className="$2">' },
  { pattern: /< Content>/g, replacement: '<TabsContent>' },
  { pattern: /<\/ Content>/g, replacement: '</TabsContent>' },

  // Fix Select components
  { pattern: /< Value([^>]*)\/>/g, replacement: '<SelectValue$1/>' },
  { pattern: /< Item([^>]*)>/g, replacement: '<SelectItem$1>' },
  { pattern: /<\/ Item>/g, replacement: '</SelectItem>' },

  // Fix Badge and Progress components
  { pattern: /< variant="([^"]*)"([^>]*)>/g, replacement: '<Badge variant="$1"$2>' },
  { pattern: /< key=\{([^}]*)\} variant="([^"]*)"([^>]*)>/g, replacement: '<Badge key={$1} variant="$2"$3>' },
  { pattern: /< value=\{([^}]*)\} className="([^"]*)"\s*\/>/g, replacement: '<Progress value={$1} className="$2" />' },

  // Fix Dialog components
  { pattern: /< open=\{([^}]*)\} onOpenChange=\{([^}]*)\}>/g, replacement: '<Dialog open={$1} onOpenChange={$2}>' },

  // Fix common icon patterns
  { pattern: /< className="h-4 w-4([^"]*)" \/>/g, replacement: '<AlertCircle className="h-4 w-4$1" />' },
  { pattern: /< className="h-12 w-12([^"]*)" \/>/g, replacement: '<Upload className="h-12 w-12$1" />' },
  { pattern: /< className="h-3 w-3([^"]*)" \/>/g, replacement: '<Star className="h-3 w-3$1" />' },
  { pattern: /<  className="([^"]*)" \/>/g, replacement: '<Bell className="$1" />' },

  // Fix empty JSX elements
  { pattern: /< \/>/g, replacement: '<Separator />' },
];

// Import patterns to fix and add
const importFixes = [
  // Fix broken import statements first
  { pattern: /^import { Label } from '@\/components\/ui\/label'$/gm, replacement: '' },
  { pattern: /^import { Input } from '@\/components\/ui\/input'$/gm, replacement: '' },
  { pattern: /^import { Textarea } from '@\/components\/ui\/textarea'$/gm, replacement: '' },
  { pattern: /^import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@\/components\/ui\/select'$/gm, replacement: '' },
  { pattern: /^import { Badge } from '@\/components\/ui\/badge'$/gm, replacement: '' },
  { pattern: /^import { Progress } from '@\/components\/ui\/progress'$/gm, replacement: '' },
  { pattern: /^import { Tabs, TabsList, TabsTrigger, TabsContent } from '@\/components\/ui\/tabs'$/gm, replacement: '' },
  { pattern: /^import { AlertCircle, Upload, Plus, Save, Eye } from 'lucide-react'$/gm, replacement: '' },
];

// Components that need imports
const componentImports = [
  {
    check: /\b(Label|Input|Textarea|Select|Badge|Progress)\b/,
    imports: [
      "import { Label } from '@/components/ui/label'",
      "import { Input } from '@/components/ui/input'",
      "import { Textarea } from '@/components/ui/textarea'",
      "import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'",
      "import { Badge } from '@/components/ui/badge'",
      "import { Progress } from '@/components/ui/progress'"
    ]
  },
  {
    check: /\b(Tabs|TabsList|TabsTrigger|TabsContent)\b/,
    imports: [
      "import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'"
    ]
  },
  {
    check: /\b(Dialog|DialogContent|DialogHeader|DialogTitle|DialogTrigger)\b/,
    imports: [
      "import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'"
    ]
  },
  {
    check: /\b(AlertCircle|Upload|Plus|Save|Eye|Star|Bell|Separator)\b/,
    imports: [
      "import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'"
    ]
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Apply all fixes
    fixes.forEach(fix => {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        modified = true;
      }
    });

    // Remove broken import statements
    importFixes.forEach(fix => {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        modified = true;
      }
    });

    // Add missing imports
    componentImports.forEach(importGroup => {
      if (importGroup.check.test(content)) {
        // Find the last import statement
        const importLines = content.split('\n');
        let lastImportIndex = -1;

        for (let i = 0; i < importLines.length; i++) {
          if (importLines[i].trim().startsWith('import ')) {
            lastImportIndex = i;
          }
        }

        if (lastImportIndex >= 0) {
          importGroup.imports.forEach(importStatement => {
            if (!content.includes(importStatement)) {
              importLines.splice(lastImportIndex + 1, 0, importStatement);
              lastImportIndex++;
              modified = true;
            }
          });

          content = importLines.join('\n');
        }
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Starting systematic syntax error fixes...');
console.log(`📁 Scanning directory: ${srcDir}`);

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} TypeScript/TSX files`);

let fixedCount = 0;
let totalCount = tsxFiles.length;

tsxFiles.forEach(file => {
  if (fixFile(file)) {
    fixedCount++;
  }
});

console.log('\n🎉 Fix Summary:');
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`📄 Total files: ${totalCount}`);
console.log(`⏭️  Files unchanged: ${totalCount - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n🚀 Run "npm run type-check" to verify fixes!');
} else {
  console.log('\n✨ All files are already in good shape!');
}
