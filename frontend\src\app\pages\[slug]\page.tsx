
"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Navigation } from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

import { useTranslation } from '@/hooks/useTranslation'

import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  Eye,
  Eye
} from 'lucide-react'

// أنواع البيانات
interface PageContent {
  id: string
  page_id: string
  language: 'ar' | 'en' | 'fr'
  title: string
  content: string
  meta_description?: string
  meta_keywords?: string
}

interface Page {
  id: string
  slug: string
  is_published: boolean
  author_id: string
  featured_image?: string
  created_at: string
  updated_at: string
  page_content: PageContent[]
  profiles?: {
    full_name: string
  }
}

export default function DynamicPage() {
  const params = useParams()
  const { locale } = useTranslation()
  const [page, setPage] = useState<Page | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const slug = params.slug as string

  // جلب بيانات الصفحة
  useEffect(() => {
    const fetchPage = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch(`/api/pages/slug/${slug}?language=${locale}`)
        const data = await response.json()
        
        if (response.ok) {
          setPage(data.page)
          
          // تحديث عنوان الصفحة والوصف
          if (data.page.page_content?.[0]) {
            const content = data.page.page_content[0]
            document.title = `${content.title} - Graduation Toqs`
            
            // تحديث meta description
            const metaDescription = document.querySelector('meta[name="description"]')
            if (metaDescription && content.meta_description) {
              metaDescription.setAttribute('content', content.meta_description)
            }
            
            // تحديث meta keywords
            const metaKeywords = document.querySelector('meta[name="keywords"]')
            if (metaKeywords && content.meta_keywords) {
              metaKeywords.setAttribute('content', content.meta_keywords)
            }
          }
        } else {
          setError(data.error || 'فشل في جلب الصفحة')
        }
      } catch (error) {
        console.error('Error fetching page:', error)
        setError('خطأ في الاتصال بالخادم')
      } finally {
        setLoading(false)
      }
    }

    if (slug) {
      fetchPage()
    }
  }, [slug, locale])

  // مشاركة الصفحة
  const sharePage = async () => {
    if (navigator.share && page?.page_content?.[0]) {
      try {
        await navigator.share({
          title: page.page_content[0].title,
          text: page.page_content[0].meta_description || '',
          url: window.location.href,
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      // نسخ الرابط إلى الحافظة
      try {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('تم نسخ الرابط إلى الحافظة')
      } catch (error) {
        console.error('Error copying to clipboard:', error)
        toast.error('فشل في نسخ الرابط')
      }
    }
  }

  // حفظ الصفحة في المفضلة
  const bookmarkPage = () => {
    if (!page?.page_content?.[0]) return
    
    const bookmarks = JSON.parse(localStorage.getItem('bookmarkedPages') || '[]')
    const pageData = {
      id: page.id,
      slug: page.slug,
      title: page.page_content[0].title,
      created_at: page.created_at
    }
    
    const isBookmarked = bookmarks.some((bookmark: any) => bookmark.id === page.id)
    
    if (isBookmarked) {
      const updatedBookmarks = bookmarks.filter((bookmark: any) => bookmark.id !== page.id)
      localStorage.setItem('bookmarkedPages', JSON.stringify(updatedBookmarks))
    } else {
      bookmarks.push(pageData)
      localStorage.setItem('bookmarkedPages', JSON.stringify(bookmarks))
    }
  }

  // التحقق من وجود الصفحة في المفضلة
  const isBookmarked = () => {
    if (!page) return false
    const bookmarks = JSON.parse(localStorage.getItem('bookmarkedPages') || '[]')
    return bookmarks.some((bookmark: any) => bookmark.id === page.id)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (error || !page) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="py-16">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
                الصفحة غير موجودة 😔
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 arabic-text">
                {error || 'الصفحة التي تبحث عنها غير متاحة أو تم حذفها'}
              </p>
              <Button asChild>
                <a href="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للرئيسية
                </a>
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  const content = page.page_content?.[0]
  if (!content) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="py-16">
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
                المحتوى غير متاح
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 arabic-text">
                محتوى هذه الصفحة غير متاح باللغة الحالية
              </p>
              <Button asChild>
                <a href="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للرئيسية
                </a>
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <Button variant="outline" size="sm" className="mb-4" asChild>
              <a href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للرئيسية
              </a>
            </Button>

            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text leading-tight">
                  {content.title}
                </h1>
                
                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300 mb-6">
                  {page.profiles?.full_name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span className="arabic-text">{page.profiles.full_name}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{new Date(page.created_at).toLocaleDateString('en-US')}</span>
                  </div>
                  <Badge variant="secondary" className="arabic-text">
                    <Eye className="h-3 w-3 mr-1" />
                    منشور
                  </Badge>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={bookmarkPage}
                  className={isBookmarked() ? 'bg-blue-50 border-blue-200' : ''}
                >
                  <Bookmark className={`h-4 w-4 ${isBookmarked() ? 'fill-current text-blue-600' : ''}`} />
                </Button>
                <Button variant="outline" size="sm" onClick={sharePage}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          {page.featured_image && (
            <div className="mb-8">
              <img
                src={page.featured_image}
                alt={content.title}
                className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
              />
            </div>
          )}

          {/* Content */}
          <Card>
            <CardContent className="p-8">
              <div 
                className="prose prose-lg max-w-none dark:prose-invert arabic-text"
                style={{ 
                  lineHeight: '1.8',
                  fontSize: '1.1rem'
                }}
                dangerouslySetInnerHTML={{ 
                  __html: content.content.replace(/\n/g, '<br />') 
                }}
              />
            </CardContent>
          </Card>

          {/* Meta Information */}
          {content.meta_description && (
            <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2 arabic-text">
                ملخص الصفحة
              </h3>
              <p className="text-gray-600 dark:text-gray-300 arabic-text">
                {content.meta_description}
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
