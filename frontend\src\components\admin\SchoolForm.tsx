
'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'

import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {  Content, List, Trigger } from '@/components/ui/tabs'

import { Mock } from '@/lib/mockData'





import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
  s,
  X
} from 'lucide-react'

interface FormProps {
  school?: Mock onSubmit: (schoolData: Partial<Mock >) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

interface FormData {
  name: string
  name_en: string
  name_fr: string
  address: string
  city: string
  phone: string
  email: string
  website: string
  logo_url: string
  graduation_date: string
  student_count: number
  is_active: boolean
  settings: {
    graduation_ceremony_location: string
    dress_code: string
    photography_allowed: boolean
  }
}

interface FormErrors {
  [key: string]: string
}

export default function Form({ school, onSubmit, onCancel, isLoading = false }: FormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    name_en: '',
    name_fr: '',
    address: '',
    city: '',
    phone: '',
    email: '',
    website: '',
    logo_url: '',
    graduation_date: '',
    student_count: 0,
    is_active: true,
    settings: {
      graduation_ceremony_location: '',
      dress_code: 'formal',
      photography_allowed: true
    }
  })

  const [errors, setErrors] = useState<FormErrors>({})

  // تحميل بيانات المدرسة للتحديث
  useEffect(() => {
    if (school) {
      setFormData({
        name: school.name || '',
        name_en: school.name_en || '',
        name_fr: school.name_fr || '',
        address: school.address || '',
        city: school.city || '',
        phone: school.phone || '',
        email: school.email || '',
        website: school.website || '',
        logo_url: school.logo_url || '',
        graduation_date: school.graduation_date || '',
        student_count: school.student_count || 0,
        is_active: school.is_active,
        settings: {
          graduation_ceremony_location: school.settings?.graduation_ceremony_location || '',
          dress_code: school.settings?.dress_code || 'formal',
          photography_allowed: school.settings?.photography_allowed ?? true
        }
      })
    }
  }, [school])

  const updateFormData = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const update = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [field]: value
      }
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // التحقق من الحقول المطلوبة
    if (!formData.name.trim()) {
      newErrors.name = 'اسم المدرسة مطلوب'
    }

    if (formData.student_count < 0) {
      newErrors.student_count = 'عدد الطلاب يجب أن يكون رقماً موجباً'
    }

    // التحقق من صحة البريد الإلكتروني
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    // التحقق من صحة الموقع الإلكتروني
    if (formData.website && !/^https?:\/\/.+/.test(formData.website)) {
      newErrors.website = 'الموقع الإلكتروني يجب أن يبدأ بـ http:// أو https://'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
    } catch (_error) {}
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic" className="arabic-text">المعلومات الأساسية</TabsTrigger>
          <SelectTrigger value="contact" className="arabic-text">معلومات التواصل</TabsTrigger>
          <TabsTrigger value="settings" className="arabic-text">الإعدادات</TabsTrigger>
        </TabsList>

        {/* المعلومات الأساسية */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                < s className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="arabic-text">اسم المدرسة *</Label>
                  <Input id="name"
                    value={formData.name}
                    onChange={(e) => updateFormData('name', e.target.value)}
                    placeholder="جامعة الإمارات العربية المتحدة"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 arabic-text">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name_en" className="arabic-text">الاسم بالإنجليزية</Label>
                  <Input id="name_en"
                    value={formData.name_en}
                    onChange={(e) => updateFormData('name_en', e.target.value)}
                    placeholder="United Arab Emirates University"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name_fr" className="arabic-text">الاسم بالفرنسية</Label>
                  <Input id="name_fr"
                    value={formData.name_fr}
                    onChange={(e) => updateFormData('name_fr', e.target.value)}
                    placeholder="Université des Émirats Arabes Unis"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city" className="arabic-text">المدينة</Label>
                  <Input id="city"
                    value={formData.city}
                    onChange={(e) => updateFormData('city', e.target.value)}
                    placeholder="دبي"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address" className="arabic-text">العنوان</Label>
                <Input id="address"
                  value={formData.address}
                  onChange={(e) => updateFormData('address', e.target.value)}
                  placeholder="شارع الجامعة، المنطقة الأكاديمية"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="student_count" className="arabic-text">عدد الطلاب</Label>
                  <Input id="student_count"
                    type="number"
                    min="0"
                    value={formData.student_count}
                    onChange={(e) => updateFormData('student_count', parseInt(e.target.value) || 0)}
                    placeholder="1000"
                    className={errors.student_count ? 'border-red-500' : ''}
                  />
                  {errors.student_count && (
                    <p className="text-sm text-red-500 arabic-text">{errors.student_count}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="graduation_date" className="arabic-text">تاريخ التخرج</Label>
                  <Input id="graduation_date"
                    type="date"
                    value={formData.graduation_date}
                    onChange={(e) => updateFormData('graduation_date', e.target.value)}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => updateFormData('is_active', checked)}
                />
                <label htmlFor="is_active" className="arabic-text">
                  مدرسة نشطة
                  {formData.is_active ? (
                    <Badge variant="default" className="mr-2">نشطة</div>
                  ) : (
                    <Badge variant="secondary" className="mr-2">غير نشطة</div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* معلومات التواصل */}
        <TabsContent value="contact" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <div className="h-5 w-5" />
                معلومات التواصل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="arabic-text">رقم الهاتف</Label>
                  <Input id="phone"
                    value={formData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                    placeholder="+971-4-123-4567"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="arabic-text">البريد الإلكتروني</Label>
                  <Input id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 arabic-text">{errors.email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="website" className="arabic-text">الموقع الإلكتروني</Label>
                  <Input id="website"
                    value={formData.website}
                    onChange={(e) => updateFormData('website', e.target.value)}
                    placeholder="https://www.university.edu"
                    className={errors.website ? 'border-red-500' : ''}
                  />
                  {errors.website && (
                    <p className="text-sm text-red-500 arabic-text">{errors.website}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logo_url" className="arabic-text">رابط الشعار</Label>
                  <Input id="logo_url"
                    value={formData.logo_url}
                    onChange={(e) => updateFormData('logo_url', e.target.value)}
                    placeholder="https://example.com/logo.png"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* الإعدادات */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text flex items-center gap-2">
                <div className="h-5 w-5" />
                إعدادات التخرج
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="ceremony_location" className="arabic-text">مكان حفل التخرج</Label>
                <Input id="ceremony_location"
                  value={formData.settings.graduation_ceremony_location}
                  onChange={(e) => update ('graduation_ceremony_location', e.target.value)}
                  placeholder="قاعة الاحتفالات الكبرى"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dress_code" className="arabic-text">نوع الزي المطلوب</Label>
                <select
                  id="dress_code"
                  value={formData.settings.dress_code}
                  onChange={(e) => update ('dress_code', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="formal">رسمي</option>
                  <option value="academic">أكاديمي</option>
                  <option value="business">عمل</option>
                  <option value="casual">عادي</option>
                </select>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="photography_allowed"
                  checked={formData.settings.photography_allowed}
                  onCheckedChange={(checked) => update ('photography_allowed', checked)}
                />
                <Label htmlFor="photography_allowed" className="arabic-text">
                  السماح بالتصوير
                </Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex justify-end gap-4 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          إلغاء
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
        >
          <div className="h-4 w-4 mr-2" />
          {isLoading ? 'جاري الحفظ...' : school ? 'تحديث المدرسة' : 'إضافة المدرسة'}
        </Button>
      </div>
    </form>
  )
}
