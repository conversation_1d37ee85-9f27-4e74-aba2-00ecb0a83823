
'use client'

import {
  useState
} from 'react'
import {
  Button
} from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  useTextAIModel
} from '@/hooks/useAIModels'


import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  AlertCircle,
  RefreshCw,
  Brain,
  Wand2,
  AlertCircle
} from 'lucide-react'

interface ProductData {
  name: string
  description: string
  category: string
  price: number
  features: string[]
  specifications: { [key: string]: string }
}

interface AIProductEnhancerProps {
  productData: ProductData
  onUpdate: (field: string, value: any) => void
  className?: string
}

type AIAction = 'generate_description' | 'generate_title' | 'generate_features' | 'generate_specifications' | 'suggest_category' | 'optimize_seo'

interface AIResult {
  description?: string
  title?: string
  features?: string[]
  specifications?: { [key: string]: string }
  suggestedCategory?: string
  confidence?: number
  keywords?: string[]
  metaDescription?: string
}

export function AIProductEnhancer({ productData, onUpdate, className = "" }: AIProductEnhancerProps) {
  const { model, available, loading: modelLoading } = useTextAIModel()
  const [activeAction, setActiveAction] = useState<AIAction | null>(null)
  const [results, setResults] = useState<{ [key in AIAction]?: AIResult }>({})
  const [selectedModel, setSelectedModel] = useState<string>('')

  const handleAIAction = async (action: AIAction) => {
    if (!available || !model) {
      alert('لا توجد نماذج ذكاء اصطناعي نشطة')
      return
    }

    setActiveAction(action)
    
    try {
      const response = await fetch('/api/ai/product-enhancement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          productData,
          modelId: selectedModel || model.id,
          language: 'ar'
        })
      })

      if (!response.ok) {
        throw new Error('فشل في تحسين المنتج')
      }

      const data = await response.json()
      
      if (data.success) {
        setResults(prev => ({
          ...prev,
          [action]: data.result
        }))
      } else {
        throw new Error(data.error || 'خطأ غير معروف')
      }
    } catch (error) {
      console.error('AI Enhancement Error:', error)
      alert(error instanceof Error ? error.message : 'خطأ في تحسين المنتج')
    } finally {
      setActiveAction(null)
    }
  }

  const applyResult = (action: AIAction, field: string, value: any) => {
    onUpdate(field, value)
    // إزالة النتيجة بعد التطبيق
    setResults(prev => {
      const newResults = { ...prev }
      delete newResults[action]
      return newResults
    })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // يمكن إضافة toast notification هنا
  }

  if (modelLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span className="arabic-text">جاري تحميل نماذج الذكاء الاصطناعي...</span>
        </CardContent>
      </Card>
    )
  }

  if (!available) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6 text-center">
          <div>
            <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 arabic-text">لا توجد نماذج ذكاء اصطناعي نشطة</h3>
            <p className="text-gray-600 dark:text-gray-400 arabic-text">
              يرجى تفعيل نموذج ذكاء اصطناعي واحد على الأقل لاستخدام هذه الميزة
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 arabic-text">
          <Brain className="h-5 w-5 text-blue-600" />
          تحسين المنتج بالذكاء الاصطناعي
        </CardTitle>
        <CardDescription className="arabic-text">
          استخدم الذكاء الاصطناعي لتحسين بيانات المنتج وتوليد محتوى احترافي
        </CardDescription>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            {model.name}
          </Badge>
          <Badge variant="secondary">
            {model.provider}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* أزرار الإجراءات السريعة */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAIAction('generate_description')}
            disabled={activeAction === 'generate_description'}
            className="arabic-text"
          >
            {activeAction === 'generate_description' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <FileText className="h-4 w-4 mr-2" />
            )}
            توليد وصف
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAIAction('generate_title')}
            disabled={activeAction === 'generate_title'}
            className="arabic-text"
          >
            {activeAction === 'generate_title' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Tag className="h-4 w-4 mr-2" />
            )}
            توليد عنوان
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAIAction('generate_features')}
            disabled={activeAction === 'generate_features'}
            className="arabic-text"
          >
            {activeAction === 'generate_features' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <List className="h-4 w-4 mr-2" />
            )}
            توليد ميزات
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAIAction('generate_specifications')}
            disabled={activeAction === 'generate_specifications'}
            className="arabic-text"
          >
            {activeAction === 'generate_specifications' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Settings className="h-4 w-4 mr-2" />
            )}
            توليد مواصفات
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAIAction('suggest_category')}
            disabled={activeAction === 'suggest_category'}
            className="arabic-text"
          >
            {activeAction === 'suggest_category' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Wand2 className="h-4 w-4 mr-2" />
            )}
            اقتراح فئة
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAIAction('optimize_seo')}
            disabled={activeAction === 'optimize_seo'}
            className="arabic-text"
          >
            {activeAction === 'optimize_seo' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            تحسين SEO
          </Button>
        </div>

        {/* عرض النتائج */}
        {Object.entries(results).map(([action, result]) => (
          <Card key={action} className="border-green-200 bg-green-50 dark:bg-green-900/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-green-700 dark:text-green-300 arabic-text">
                نتيجة {getActionName(action as AIAction)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {result.description && (
                <div>
                  <Textarea
                    value={result.description}
                    readOnly
                    className="min-h-[100px] arabic-text"
                  />
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => applyResult(action as AIAction, 'description', result.description)}
                    >
                      تطبيق
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(result.description!)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {result.title && (
                <div>
                  <input
                    type="text"
                    value={result.title}
                    readOnly
                    className="w-full p-2 border rounded arabic-text"
                  />
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => applyResult(action as AIAction, 'name', result.title)}
                    >
                      تطبيق
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(result.title!)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {result.features && (
                <div>
                  <div className="space-y-1">
                    {result.features.map((feature, index) => (
                      <div key={index} className="p-2 bg-white dark:bg-gray-800 rounded border arabic-text">
                        {feature}
                      </div>
                    ))}
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => applyResult(action as AIAction, 'features', result.features)}
                    >
                      تطبيق الكل
                    </Button>
                  </div>
                </div>
              )}

              {result.specifications && (
                <div>
                  <div className="space-y-1">
                    {Object.entries(result.specifications).map(([key, value]) => (
                      <div key={key} className="flex gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <span className="font-medium arabic-text">{key}:</span>
                        <span className="arabic-text">{value}</span>
                      </div>
                    ))}
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => applyResult(action as AIAction, 'specifications', result.specifications)}
                    >
                      تطبيق الكل
                    </Button>
                  </div>
                </div>
              )}

              {result.suggestedCategory && (
                <div>
                  <div className="p-2 bg-white dark:bg-gray-800 rounded border arabic-text">
                    الفئة المقترحة: <strong>{getCategoryName(result.suggestedCategory)}</strong>
                    {result.confidence && (
                      <Badge variant="secondary" className="mr-2">
                        {Math.round(result.confidence * 100)}% ثقة
                      </Badge>
                    )}
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => applyResult(action as AIAction, 'category', result.suggestedCategory)}
                    >
                      تطبيق
                    </Button>
                  </div>
                </div>
              )}

              {result.keywords && (
                <div>
                  <div className="flex flex-wrap gap-2">
                    {result.keywords.map((keyword, index) => (
                      <Badge key={index} variant="outline" className="arabic-text">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                  {result.metaDescription && (
                    <Textarea
                      value={result.metaDescription}
                      readOnly
                      className="mt-2 arabic-text"
                      placeholder="وصف SEO"
                    />
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  )
}

function getActionName(action: AIAction): string {
  const actionNames: { [key in AIAction]: string } = {
    'generate_description': 'توليد الوصف',
    'generate_title': 'توليد العنوان',
    'generate_features': 'توليد الميزات',
    'generate_specifications': 'توليد المواصفات',
    'suggest_category': 'اقتراح الفئة',
    'optimize_seo': 'تحسين SEO'
  }
  
  return actionNames[action]
}

function getCategoryName(category: string): string {
  const categoryNames: { [key: string]: string } = {
    'gown': 'ثوب التخرج',
    'cap': 'قبعة التخرج',
    'stole': 'وشاح التخرج',
    'tassel': 'شرابة التخرج',
    'hood': 'قلنسوة التخرج'
  }
  
  return categoryNames[category] || category
}
