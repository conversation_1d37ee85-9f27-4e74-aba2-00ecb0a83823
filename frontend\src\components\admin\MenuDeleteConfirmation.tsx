
"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'

import {
  import {
import { EnhancedConfirmDialog
} from '@/components/ui/enhanced-confirm-dialog'
import {
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  Save,
  Eye,
  Clock,
  X
} from 'lucide-react'

interface MenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id: string | null
  target_type: 'internal' | 'external'
  target_value: string
  is_active: boolean
  order_index: number
  children?: MenuItem[]
}

interface MenuDeleteConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  item: MenuItem | null
  childItems: MenuItem[]
  hasUnsavedChanges: boolean
  onConfirmDelete: () => Promise<void>
  onSaveMenu?: () => Promise<void>
}

export function MenuDeleteConfirmation({
  open,
  onOpenChange,
  item,
  childItems,
  hasUnsavedChanges,
  onConfirmDelete,
  onSaveMenu
}: MenuDeleteConfirmationProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  if (!item) return null

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await onConfirmDelete()
      onOpenChange(false)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleSave = async () => {
    if (!onSaveMenu) return
    
    setIsSaving(true)
    try {
      await onSaveMenu()
      onOpenChange(false)
    } finally {
      setIsSaving(false)
    }
  }

  const getAdditionalInfo = () => {
    const info = []
    
    if (childItems.length > 0) {
      info.push(`يحتوي على ${childItems.length} عنصر فرعي`)
      info.push('سيتم حذف العناصر الفرعية أيضاً')
    }
    
    if (item.is_active) {
      info.push('العنصر مفعل حالياً في القائمة')
    }
    
    if (item.target_type === 'external') {
      info.push('يحتوي على رابط خارجي')
    }
    
    if (item.slug) {
      info.push(`الرابط المختصر: /${item.slug}`)
    }

    return info
  }

  return (
    <EnhancedConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title="تأكيد حذف عنصر القائمة"
      description="هل أنت متأكد من حذف هذا العنصر من القائمة؟ هذا الإجراء لا يمكن التراجع عنه."
      itemName={item.title_ar}
      itemType="عنصر القائمة"
      variant="destructive"
      confirmText="حذف العنصر نهائياً"
      saveText="حفظ القائمة أولاً"
      showSaveOption={hasUnsavedChanges}
      additionalInfo={getAdditionalInfo()}
      icon="trash"
      onConfirm={handleDelete}
      onSave={hasUnsavedChanges ? handleSave : undefined}
    />
  )
}

// مكون تأكيد حذف متعدد
interface BulkDeleteConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  items: MenuItem[]
  hasUnsavedChanges: boolean
  onConfirmDelete: () => Promise<void>
  onSaveMenu?: () => Promise<void>
}

export function BulkDeleteConfirmation({
  open,
  onOpenChange,
  items,
  hasUnsavedChanges,
  onConfirmDelete,
  onSaveMenu
}: BulkDeleteConfirmationProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await onConfirmDelete()
      onOpenChange(false)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleSave = async () => {
    if (!onSaveMenu) return
    
    setIsSaving(true)
    try {
      await onSaveMenu()
      onOpenChange(false)
    } finally {
      setIsSaving(false)
    }
  }

  const getAdditionalInfo = () => {
    const info = []
    const activeItems = items.filter(item => item.is_active).length
    const inactiveItems = items.length - activeItems
    const externalLinks = items.filter(item => item.target_type === 'external').length
    
    info.push(`${items.length} عنصر محدد للحذف`)
    
    if (activeItems > 0) {
      info.push(`${activeItems} عنصر مفعل`)
    }
    
    if (inactiveItems > 0) {
      info.push(`${inactiveItems} عنصر غير مفعل`)
    }
    
    if (externalLinks > 0) {
      info.push(`${externalLinks} رابط خارجي`)
    }

    return info
  }

  return (
    <EnhancedConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title="تأكيد حذف عناصر متعددة"
      description={`هل أنت متأكد من حذف ${items.length} عنصر من القائمة؟ هذا الإجراء لا يمكن التراجع عنه.`}
      itemName={`${items.length} عنصر`}
      itemType="عناصر القائمة"
      variant="destructive"
      confirmText="حذف العناصر نهائياً"
      saveText="حفظ القائمة أولاً"
      showSaveOption={hasUnsavedChanges}
      additionalInfo={getAdditionalInfo()}
      icon="trash"
      onConfirm={handleDelete}
      onSave={hasUnsavedChanges ? handleSave : undefined}
    />
  )
}

// مكون تأكيد تفعيل/إلغاء تفعيل جماعي
interface BulkToggleConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  items: MenuItem[]
  action: 'activate' | 'deactivate'
  hasUnsavedChanges: boolean
  onConfirmToggle: () => Promise<void>
  onSaveMenu?: () => Promise<void>
}

export function BulkToggleConfirmation({
  open,
  onOpenChange,
  items,
  action,
  hasUnsavedChanges,
  onConfirmToggle,
  onSaveMenu
}: BulkToggleConfirmationProps) {
  const [isToggling, setIsToggling] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const handleToggle = async () => {
    setIsToggling(true)
    try {
      await onConfirmToggle()
      onOpenChange(false)
    } finally {
      setIsToggling(false)
    }
  }

  const handleSave = async () => {
    if (!onSaveMenu) return
    
    setIsSaving(true)
    try {
      await onSaveMenu()
      onOpenChange(false)
    } finally {
      setIsSaving(false)
    }
  }

  const isActivating = action === 'activate'
  const actionText = isActivating ? 'تفعيل' : 'إلغاء تفعيل'
  const actionIcon = isActivating ? 'success' : 'warning'

  return (
    <EnhancedConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title={`تأكيد ${actionText} العناصر`}
      description={`هل أنت متأكد من ${actionText} ${items.length} عنصر في القائمة؟`}
      itemName={`${items.length} عنصر`}
      itemType="عناصر القائمة"
      variant={isActivating ? 'default' : 'warning'}
      confirmText={`${actionText} العناصر`}
      saveText="حفظ القائمة أولاً"
      showSaveOption={hasUnsavedChanges}
      additionalInfo={[`${items.length} عنصر سيتم ${actionText}ه`]}
      icon={actionIcon}
      onConfirm={handleToggle}
      onSave={hasUnsavedChanges ? handleSave : undefined}
    />
  )
}
