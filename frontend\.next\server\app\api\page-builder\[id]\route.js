(()=>{var e={};e.id=6865,e.ids=[6865],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},93249:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>b});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>d,PUT:()=>p});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(38561);async function d(e,{params:r}){try{let e=u.C.getPageProjects().find(e=>e.id===r.id);if(!e)return i.NextResponse.json({error:"المشروع غير موجود"},{status:404});return i.NextResponse.json({project:e})}catch(e){return i.NextResponse.json({error:"خطأ في جلب المشروع"},{status:500})}}async function p(e,{params:r}){try{let t=await e.json(),s=u.C.getPageProjects(),n=s.findIndex(e=>e.id===r.id);if(-1===n)return i.NextResponse.json({error:"المشروع غير موجود"},{status:404});let o={...s[n],...t,id:r.id,updatedAt:new Date().toISOString()};return s[n]=o,u.C.savePageProjects(s),i.NextResponse.json({message:"تم تحديث المشروع بنجاح",project:o})}catch(e){return i.NextResponse.json({error:"خطأ في تحديث المشروع"},{status:500})}}async function x(e,{params:r}){try{let e=u.C.getPageProjects(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return i.NextResponse.json({error:"المشروع غير موجود"},{status:404});if(e[t].isPublished)return i.NextResponse.json({error:"لا يمكن حذف المشروع المنشور. يرجى إلغاء نشره أولاً.",suggestion:"unpublish_first"},{status:400});let s=e.splice(t,1)[0];return u.C.savePageProjects(e),i.NextResponse.json({message:`تم حذف المشروع "${s.name}" بنجاح`,project:s})}catch(e){return i.NextResponse.json({error:"خطأ في حذف المشروع"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/page-builder/[id]/route",pathname:"/api/page-builder/[id]",filename:"route",bundlePath:"app/api/page-builder/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:b,serverHooks:m}=c;function g(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:b})}},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(93249));module.exports=s})();