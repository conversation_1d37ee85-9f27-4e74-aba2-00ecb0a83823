{"version": 3, "file": "filters.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["pseudo-selectors/filters.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,QAAQ,MAAM,UAAU,CAAC;AAUhC,SAAS,YAAY,CACjB,IAAgC,EAChC,OAAmC;IAEnC,OAAO,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACvC,OAAO,MAAM,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC,CAAC;AACN,CAAC;AAED,MAAM,CAAC,MAAM,OAAO,GAA2B;IAC3C,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE;QAC5B,OAAO,SAAS,QAAQ,CAAC,IAAI;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC;IACN,CAAC;IACD,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEjC,OAAO,SAAS,SAAS,CAAC,IAAI;YAC1B,OAAO,CACH,IAAI,CAAC,IAAI,CAAC;gBACV,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CACtD,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED,4BAA4B;IAC5B,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QACvC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,QAAQ,CAAC,SAAS;YAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,QAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,QAAQ,CAAC,IAAI;YACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAAE,MAAM;gBACrC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC5B,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IACD,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QAC5C,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,QAAQ,CAAC,SAAS;YAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,QAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,YAAY,CAAC,IAAI;YAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAAE,MAAM;gBACrC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC5B,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IACD,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QACzC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,QAAQ,CAAC,SAAS;YAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,QAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,SAAS,CAAC,IAAI;YAC1B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;oBAAE,MAAM;gBACxC,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;oBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAC3D;oBACE,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IACD,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,QAAQ,CAAC,SAAS;YAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,QAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,aAAa,CAAC,IAAI;YAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;oBAAE,MAAM;gBACxC,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;oBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAC3D;oBACE,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IAED,yCAAyC;IACzC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE;QACzB,OAAO,CAAC,IAAI,EAAE,EAAE;YACZ,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACvC,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CACD,IAAgC,EAChC,IAAY,EACZ,OAA2C,EAC3C,OAAgB;QAEhB,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE3B,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,sBAAsB;YACtB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SAC/C;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,8DAA8D;YAC9D,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3D;QAED,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,EAAE,kBAAkB,CAAC,WAAW,CAAC;IACtC,OAAO,EAAE,kBAAkB,CAAC,WAAW,CAAC;IACxC,MAAM,EAAE,kBAAkB,CAAC,UAAU,CAAC;CACzC,CAAC;AAEF;;;;;GAKG;AACH,SAAS,kBAAkB,CACvB,IAA4C;IAE5C,OAAO,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE;QAClD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3B,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;YAC5B,OAAO,QAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,OAAO,SAAS,MAAM,CAAC,IAAI;YACvB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC;IACN,CAAC,CAAC;AACN,CAAC"}