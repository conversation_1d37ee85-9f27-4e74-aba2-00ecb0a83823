
/**
 * ✅ إصلاح حرج: Input Validation & Sanitization
 * 
 * يوفر تحقق شامل من صحة المدخلات وتنظيفها
 * لمنع هجمات XSS وSQL Injection وغيرها
 * 
 * @security CRITICAL - يجب استخدام هذا لجميع المدخلات
 */

import DOMPurify from 'isomorphic-dompurify'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'


// تعبيرات منتظمة للتحقق من صحة البيانات
const VALIDATION_PATTERNS = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^(\+?[1-9]\d{1,14}|0[1-9]\d{8,9})$/,
  arabicName: /^[\u0600-\u06FF\s]{2,50}$/,
  englishName: /^[a-zA-Z\s]{2,50}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  moroccanPhone: /^(\+212|0)[5-7][0-9]{8}$/
}

// قوائم سوداء للكلمات الخطيرة
const DANGEROUS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /data:text\/html/gi,
  /vbscript:/gi,
  /<iframe/gi,
  /<object/gi,
  /<embed/gi,
  /<link/gi,
  /<meta/gi,
  /eval\s*\(/gi,
  /expression\s*\(/gi
]

/**
 * نتيجة التحقق من صحة البيانات
 */
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  sanitizedValue?: any
}

/**
 * ✅ تنظيف HTML من المحتوى الخطير
 */
export function sanitizeHTML(input: string, allowedTags: string[] = []): string {
  if (!input || typeof input !== 'string') {
    return ''
  }

  // إعدادات DOMPurify
  const config = {
    ALLOWED_TAGS: allowedTags.length > 0 ? allowedTags : ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'title', 'alt'],
    FORBID_TAGS: ['script', 'object', 'embed', 'iframe', 'form', 'input'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'style']
  }

  return DOMPurify.sanitize(input, config)
}

/**
 * ✅ تنظيف النصوص من الأحرف الخطيرة
 */
export function sanitizeText(input: string): string {
  if (!input || typeof input !== 'string') {
    return ''
  }

  // إزالة الأحرف الخطيرة
  let sanitized = input
    .replace(/[<>]/g, '') // إزالة < و >
    .replace(/['"]/g, '') // إزالة علامات الاقتباس
    .replace(/[&]/g, '&amp;') // تحويل &
    .trim()

  // فحص الأنماط الخطيرة
  DANGEROUS_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '')
  })

  return sanitized
}

/**
 * ✅ تحقق من صحة البريد الإلكتروني
 */
export function validateEmail(email: string): ValidationResult {
  const errors: string[] = []

  if (!email) {
    errors.push('البريد الإلكتروني مطلوب')
    return { isValid: false, errors }
  }

  if (email.length > 255) {
    errors.push('البريد الإلكتروني طويل جداً')
  }

  if (!VALIDATION_PATTERNS.email.test(email)) {
    errors.push('البريد الإلكتروني غير صحيح')
  }

  // فحص الأحرف الخطيرة
  if (DANGEROUS_PATTERNS.some(pattern => pattern.test(email))) {
    errors.push('البريد الإلكتروني يحتوي على أحرف غير مسموحة')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitizeText(email.toLowerCase())
  }
}

/**
 * ✅ تحقق من صحة كلمة المرور
 */
export function validatePassword(password: string): ValidationResult {
  const errors: string[] = []

  if (!password) {
    errors.push('كلمة المرور مطلوبة')
    return { isValid: false, errors }
  }

  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
  }

  if (password.length > 128) {
    errors.push('كلمة المرور طويلة جداً')
  }

  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير')
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير')
  }

  if (!/[0-9]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم')
  }

  if (!/[@$!%*?&]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص')
  }

  // فحص كلمات المرور الشائعة
  const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein']
  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('كلمة المرور ضعيفة جداً')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: password // لا نقوم بتنظيف كلمة المرور
  }
}

/**
 * ✅ تحقق من صحة الاسم
 */
export function validateName(name: string, language: 'ar' | 'en' = 'ar'): ValidationResult {
  const errors: string[] = []

  if (!name) {
    errors.push('الاسم مطلوب')
    return { isValid: false, errors }
  }

  if (name.length < 2) {
    errors.push('الاسم قصير جداً')
  }

  if (name.length > 50) {
    errors.push('الاسم طويل جداً')
  }

  const pattern = language === 'ar' ? VALIDATION_PATTERNS.arabicName : VALIDATION_PATTERNS.englishName
  if (!pattern.test(name)) {
    errors.push(language === 'ar' ? 'الاسم يجب أن يحتوي على أحرف عربية فقط' : 'الاسم يجب أن يحتوي على أحرف إنجليزية فقط')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitizeText(name)
  }
}

/**
 * ✅ تحقق من صحة رقم الهاتف المغربي
 */
export function validateMoroccanPhone(phone: string): ValidationResult {
  const errors: string[] = []

  if (!phone) {
    errors.push('رقم الهاتف مطلوب')
    return { isValid: false, errors }
  }

  // تنظيف رقم الهاتف
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

  if (!VALIDATION_PATTERNS.moroccanPhone.test(cleanPhone)) {
    errors.push('رقم الهاتف المغربي غير صحيح')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: cleanPhone
  }
}

/**
 * ✅ تحقق من صحة URL
 */
export function validateURL(url: string): ValidationResult {
  const errors: string[] = []

  if (!url) {
    errors.push('الرابط مطلوب')
    return { isValid: false, errors }
  }

  if (!VALIDATION_PATTERNS.url.test(url)) {
    errors.push('الرابط غير صحيح')
  }

  // فحص البروتوكولات المسموحة
  if (!url.startsWith('https://') && !url.startsWith('http://')) {
    errors.push('الرابط يجب أن يبدأ بـ http:// أو https://')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitizeText(url)
  }
}

/**
 * ✅ تحقق شامل من بيانات المستخدم
 */
export function validateUserData(userData: {
  email?: string
  password?: string
  firstName?: string
  lastName?: string
  phone?: string
}): ValidationResult {
  const errors: string[] = []
  const sanitizedData: unknown = {}

  // تحقق من البريد الإلكتروني
  if (userData.email) {
    const emailValidation = validateEmail(userData.email)
    if (!emailValidation.isValid) {
      errors.push(...emailValidation.errors)
    } else {
      sanitizedData.email = emailValidation.sanitizedValue
    }
  }

  // تحقق من كلمة المرور
  if (userData.password) {
    const passwordValidation = validatePassword(userData.password)
    if (!passwordValidation.isValid) {
      errors.push(...passwordValidation.errors)
    } else {
      sanitizedData.password = passwordValidation.sanitizedValue
    }
  }

  // تحقق من الاسم الأول
  if (userData.firstName) {
    const firstNameValidation = validateName(userData.firstName)
    if (!firstNameValidation.isValid) {
      errors.push(...firstNameValidation.errors.map(err => `الاسم الأول: ${err}`))
    } else {
      sanitizedData.firstName = firstNameValidation.sanitizedValue
    }
  }

  // تحقق من الاسم الأخير
  if (userData.lastName) {
    const lastNameValidation = validateName(userData.lastName)
    if (!lastNameValidation.isValid) {
      errors.push(...lastNameValidation.errors.map(err => `الاسم الأخير: ${err}`))
    } else {
      sanitizedData.lastName = lastNameValidation.sanitizedValue
    }
  }

  // تحقق من رقم الهاتف
  if (userData.phone) {
    const phoneValidation = validateMoroccanPhone(userData.phone)
    if (!phoneValidation.isValid) {
      errors.push(...phoneValidation.errors)
    } else {
      sanitizedData.phone = phoneValidation.sanitizedValue
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitizedData
  }
}

/**
 * ✅ منع SQL Injection
 */
export function sanitizeForSQL(input: string): string {
  if (!input || typeof input !== 'string') {
    return ''
  }

  return input
    .replace(/['"\\;]/g, '') // إزالة علامات الاقتباس والفاصلة المنقوطة
    .replace(/--/g, '') // إزالة تعليقات SQL
    .replace(/\/\*/g, '') // إزالة تعليقات متعددة الأسطر
    .replace(/\*\//g, '')
    .replace(/xp_/gi, '') // إزالة stored procedures خطيرة
    .replace(/sp_/gi, '')
    .trim()
}

/**
 * ✅ فحص محتوى الملفات المرفوعة
 */
export function validateFileUpload(file: File, allowedTypes: string[] = [], maxSize: number = 5 * 1024 * 1024): ValidationResult {
  const errors: string[] = []

  if (!file) {
    errors.push('الملف مطلوب')
    return { isValid: false, errors }
  }

  // فحص نوع الملف
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    errors.push(`نوع الملف غير مسموح. الأنواع المسموحة: ${allowedTypes.join(', ')}`)
  }

  // فحص حجم الملف
  if (file.size > maxSize) {
    errors.push(`حجم الملف كبير جداً. الحد الأقصى: ${Math.round(maxSize / 1024 / 1024)}MB`)
  }

  // فحص اسم الملف
  const fileName = sanitizeText(file.name)
  if (fileName !== file.name) {
    errors.push('اسم الملف يحتوي على أحرف غير مسموحة')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: {
      name: fileName,
      type: file.type,
      size: file.size
    }
  }
}
