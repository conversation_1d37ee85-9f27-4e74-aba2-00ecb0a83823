(()=>{var e={};e.id=2041,e.ids=[2041],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>m,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>d,POST:()=>l,PUT:()=>p});var n=r(96559),a=r(48088),i=r(37719),o=r(32190),u=r(38561);async function d(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_unpublished"),s=t.get("created_by"),n=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),i=t.get("sort_by")||"updated_at",d=t.get("sort_order")||"desc",l=u.C.getPageProjects();r||(l=l.filter(e=>e.isPublished)),s&&(l=l.filter(e=>e.createdBy===s)),l.sort((e,t)=>{let r=e[i],s=t[i];return"desc"===d?new Date(s).getTime()-new Date(r).getTime():new Date(r).getTime()-new Date(s).getTime()});let p=(n-1)*a,c=l.slice(p,p+a),g={total:l.length,published:l.filter(e=>e.isPublished).length,unpublished:l.filter(e=>!e.isPublished).length,byGenerationMode:l.reduce((e,t)=>(e[t.generationMode]=(e[t.generationMode]||0)+1,e),{}),byLanguage:l.reduce((e,t)=>(e[t.settings.language]=(e[t.settings.language]||0)+1,e),{})};return o.NextResponse.json({projects:c,total:l.length,page:n,limit:a,totalPages:Math.ceil(l.length/a),stats:g})}catch(e){return o.NextResponse.json({error:"خطأ في جلب مشاريع الصفحات"},{status:500})}}async function l(e){try{let{name:t,description:r,templateId:s,generationMode:n,settings:a}=await e.json();if(!t||!n)return o.NextResponse.json({error:"اسم المشروع وطريقة الإنشاء مطلوبان"},{status:400});let i=u.C.getPageProjects();if(i.find(e=>e.name.toLowerCase()===t.toLowerCase()))return o.NextResponse.json({error:"مشروع بنفس الاسم موجود بالفعل"},{status:400});let d=[];if(s){let e=u.C.getPageTemplates().find(e=>e.id===s);e&&(d=e.components)}let l={id:u.C.generateId(),name:t,description:r||"",components:d,templateId:s,generationMode:n,settings:{title:t,description:r||"",keywords:[],language:"ar",direction:"rtl",...a},isPublished:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),createdBy:"admin-1",version:1};return i.push(l),u.C.savePageProjects(i),o.NextResponse.json({message:"تم إنشاء المشروع بنجاح",project:l},{status:201})}catch(e){return o.NextResponse.json({error:"خطأ في إنشاء المشروع"},{status:500})}}async function p(e){try{let{action:t,projectIds:r,data:s}=await e.json();if(!t||!r||!Array.isArray(r))return o.NextResponse.json({error:"الإجراء ومعرفات المشاريع مطلوبة"},{status:400});let n=u.C.getPageProjects(),a=0;for(let e of r){let r=n.findIndex(t=>t.id===e);if(-1===r)continue;let i=n[r];switch(t){case"publish":i.isPublished=!0,i.publishedUrl=`https://example.com/pages/${i.id}`;break;case"unpublish":i.isPublished=!1,i.publishedUrl=void 0;break;case"duplicate":let o={...i,id:u.C.generateId(),name:`${i.name} - نسخة`,isPublished:!1,publishedUrl:void 0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),version:1};n.push(o);break;case"update_settings":s&&(i.settings={...i.settings,...s});break;default:continue}i.updatedAt=new Date().toISOString(),"duplicate"!==t&&(n[r]=i),a++}return u.C.savePageProjects(n),o.NextResponse.json({message:`تم تحديث ${a} مشروع بنجاح`,updatedCount:a})}catch(e){return o.NextResponse.json({error:"خطأ في تحديث المشاريع"},{status:500})}}async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("ids")?.split(",")||[];if(0===r.length)return o.NextResponse.json({error:"معرفات المشاريع مطلوبة"},{status:400});let s=u.C.getPageProjects(),n=0,a=s.filter(e=>r.includes(e.id)&&e.isPublished);if(a.length>0)return o.NextResponse.json({error:"لا يمكن حذف المشاريع المنشورة. يرجى إلغاء نشرها أولاً.",publishedProjects:a.map(e=>({id:e.id,name:e.name}))},{status:400});let i=s.filter(e=>!r.includes(e.id)||(n++,!1));return u.C.savePageProjects(i),o.NextResponse.json({message:`تم حذف ${n} مشروع بنجاح`,deletedCount:n})}catch(e){return o.NextResponse.json({error:"خطأ في حذف المشاريع"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/page-builder/route",pathname:"/api/page-builder",filename:"route",bundlePath:"app/api/page-builder/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:x,serverHooks:m}=g;function h(){return(0,i.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:x})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(3745));module.exports=s})();