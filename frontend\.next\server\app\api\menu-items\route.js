(()=>{var e={};e.id=1715,e.ids=[1715],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79175:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>l,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var n=r(96559),o=r(48088),i=r(37719),a=r(32190),u=r(38561);async function d(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_inactive"),s=t.get("parent_id"),n=u.C.getMenuItems();return r||(n=n.filter(e=>e.is_active)),s&&(n="null"===s?n.filter(e=>!e.parent_id):n.filter(e=>e.parent_id===s)),n.sort((e,t)=>e.order_index-t.order_index),a.NextResponse.json({menuItems:n,total:n.length})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e){try{let{title_ar:t,title_en:r,title_fr:s,slug:n,icon:o,parent_id:i,order_index:d,is_active:p,target_type:x,target_value:m}=await e.json();if(!t||!n||!x||!m)return a.NextResponse.json({error:"البيانات المطلوبة مفقودة"},{status:400});let c=u.C.getMenuItems();if(c.find(e=>e.slug===n))return a.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let l={id:u.C.generateId(),title_ar:t,title_en:r||void 0,title_fr:s||void 0,slug:n,icon:o||void 0,parent_id:i||void 0,order_index:d||0,is_active:p??!0,target_type:x,target_value:m,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return c.push(l),u.C.saveMenuItems(c),a.NextResponse.json({message:"تم إضافة عنصر القائمة بنجاح",menuItem:l},{status:201})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/menu-items/route",pathname:"/api/menu-items",filename:"route",bundlePath:"app/api/menu-items/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:c,serverHooks:l}=x;function b(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:c})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(79175));module.exports=s})();