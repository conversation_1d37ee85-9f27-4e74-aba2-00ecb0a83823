(()=>{var e={};e.id=6778,e.ids=[6778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75742:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>b,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{DELETE:()=>x,GET:()=>d,PUT:()=>p});var n=r(96559),i=r(48088),o=r(37719),a=r(32190),u=r(38561);async function d(e,{params:t}){try{let{id:e}=await t,r=u.C.getMenuItems().find(t=>t.id===e);if(!r)return a.NextResponse.json({error:"عنصر القائمة غير موجود"},{status:404});return a.NextResponse.json({menuItem:r})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:t}){try{let{id:r}=await t,{title_ar:s,title_en:n,title_fr:i,slug:o,icon:d,parent_id:p,order_index:x,is_active:m,target_type:c,target_value:l}=await e.json(),b=u.C.getMenuItems(),g=b.findIndex(e=>e.id===r);if(-1===g)return a.NextResponse.json({error:"عنصر القائمة غير موجود"},{status:404});if(o&&o!==b[g].slug&&b.find(e=>e.slug===o&&e.id!==r))return a.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let f={...b[g],title_ar:s||b[g].title_ar,title_en:void 0!==n?n:b[g].title_en,title_fr:void 0!==i?i:b[g].title_fr,slug:o||b[g].slug,icon:void 0!==d?d:b[g].icon,parent_id:void 0!==p?p:b[g].parent_id,order_index:void 0!==x?x:b[g].order_index,is_active:void 0!==m?m:b[g].is_active,target_type:c||b[g].target_type,target_value:l||b[g].target_value,updated_at:new Date().toISOString()};return b[g]=f,u.C.saveMenuItems(b),a.NextResponse.json({message:"تم تحديث عنصر القائمة بنجاح",menuItem:f})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function x(e,{params:t}){try{let{id:e}=await t,r=u.C.getMenuItems(),s=r.findIndex(t=>t.id===e);if(-1===s)return a.NextResponse.json({error:"عنصر القائمة غير موجود"},{status:404});if(r.filter(t=>t.parent_id===e).length>0)return a.NextResponse.json({error:"لا يمكن حذف عنصر يحتوي على عناصر فرعية"},{status:400});return r.splice(s,1),u.C.saveMenuItems(r),a.NextResponse.json({message:"تم حذف عنصر القائمة بنجاح"})}catch(e){return a.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/menu-items/[id]/route",pathname:"/api/menu-items/[id]",filename:"route",bundlePath:"app/api/menu-items/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:b}=m;function g(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(75742));module.exports=s})();