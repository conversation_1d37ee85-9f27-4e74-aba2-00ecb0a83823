(()=>{var e={};e.id=4341,e.ids=[4341],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23878:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>p,POST:()=>l,PUT:()=>m});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(38561);async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("category");t.get("language");let s="true"===t.get("include_premium"),a=t.get("sort_by")||"usageCount",n=t.get("sort_order")||"desc",o=parseInt(t.get("page")||"1"),p=parseInt(t.get("limit")||"20"),l=u.C.getPageTemplates();r&&(l=l.filter(e=>e.category===r)),s||(l=l.filter(e=>!e.isPremium)),l.sort((e,t)=>{let r=e[a],s=t[a];return"desc"===n?s-r:r-s});let m=(o-1)*p,g=l.slice(m,m+p),d=Array.from(new Set(u.C.getPageTemplates().map(e=>e.category))).map(e=>({category:e,count:u.C.getPageTemplates().filter(t=>t.category===e).length,premiumCount:u.C.getPageTemplates().filter(t=>t.category===e&&t.isPremium).length}));return i.NextResponse.json({templates:g,total:l.length,page:o,limit:p,totalPages:Math.ceil(l.length/p),categories:d,stats:{total:u.C.getPageTemplates().length,free:u.C.getPageTemplates().filter(e=>!e.isPremium).length,premium:u.C.getPageTemplates().filter(e=>e.isPremium).length,aiGenerated:u.C.getPageTemplates().filter(e=>e.isAIGenerated).length}})}catch(e){return i.NextResponse.json({error:"خطأ في جلب قوالب الصفحات"},{status:500})}}async function l(e){try{let{name:t,nameAr:r,nameEn:s,nameFr:a,description:n,category:o,components:p,preview:l,thumbnail:m,isAIGenerated:g,isPremium:d,tags:c,metadata:x}=await e.json();if(!t||!r||!o||!p)return i.NextResponse.json({error:"الاسم والفئة والمكونات مطلوبة"},{status:400});let f=u.C.getPageTemplates();if(f.find(e=>e.name.toLowerCase()===t.toLowerCase()||e.nameAr.toLowerCase()===r.toLowerCase()))return i.NextResponse.json({error:"قالب بنفس الاسم موجود بالفعل"},{status:400});let b={id:u.C.generateId(),name:t,nameAr:r,nameEn:s,nameFr:a,description:n,category:o,components:p.map(e=>({...e,id:e.id||u.C.generateId()})),preview:l||"/images/templates/default-preview.jpg",thumbnail:m||"/images/templates/default-thumb.jpg",isAIGenerated:g||!1,isPremium:d||!1,tags:c||[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),usageCount:0,rating:0,metadata:x||{colors:[],fonts:[],layout:"single-page",responsive:!0}};return f.push(b),u.C.savePageTemplates(f),i.NextResponse.json({message:"تم إنشاء القالب بنجاح",template:b},{status:201})}catch(e){return i.NextResponse.json({error:"خطأ في إنشاء القالب"},{status:500})}}async function m(e){try{let t=await e.json(),{templateId:r,action:s}=t;if(!r||!s)return i.NextResponse.json({error:"معرف القالب والإجراء مطلوبان"},{status:400});let a=u.C.getPageTemplates(),n=a.findIndex(e=>e.id===r);if(-1===n)return i.NextResponse.json({error:"القالب غير موجود"},{status:404});let o=a[n];switch(s){case"increment_usage":o.usageCount+=1;break;case"rate":let{rating:p}=t;p>=1&&p<=5&&(o.rating=p);break;default:return i.NextResponse.json({error:"إجراء غير مدعوم"},{status:400})}return o.updatedAt=new Date().toISOString(),a[n]=o,u.C.savePageTemplates(a),i.NextResponse.json({message:"تم تحديث القالب بنجاح",template:o})}catch(e){return i.NextResponse.json({error:"خطأ في تحديث القالب"},{status:500})}}async function g(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return i.NextResponse.json({error:"معرف القالب مطلوب"},{status:400});let s=u.C.getPageTemplates(),a=s.findIndex(e=>e.id===r);if(-1===a)return i.NextResponse.json({error:"القالب غير موجود"},{status:404});let n=s[a],o=u.C.getPageProjects().filter(e=>e.templateId===r);if(o.length>0)return i.NextResponse.json({error:"لا يمكن حذف القالب لأنه مستخدم في مشاريع موجودة",usedInProjects:o.map(e=>({id:e.id,name:e.name}))},{status:400});return s.splice(a,1),u.C.savePageTemplates(s),i.NextResponse.json({message:"تم حذف القالب بنجاح",deletedTemplate:{id:n.id,name:n.name,nameAr:n.nameAr}})}catch(e){return i.NextResponse.json({error:"خطأ في حذف القالب"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/page-builder/templates/route",pathname:"/api/page-builder/templates",filename:"route",bundlePath:"app/api/page-builder/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\templates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:f}=d;function b(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(23878));module.exports=s})();