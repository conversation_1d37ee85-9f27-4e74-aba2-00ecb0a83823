
"use client"

import { PageLayout } from '@/components/layouts/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

import { Badge } from '@/components/ui/badge'
import { Eye } from 'lucide-react'

  Shield, 
  Lock, 
  Eye, 
  Database, 
  Share2,
  UserCheck,
  AlertTriangle,
  CheckCircle,
  Mail,
  Phone,
  Cookie,
  Globe
} from 'lucide-react'

export default function PrivacyPolicyPage() {
  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8" showFooter={true}>
      {/* Page Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Shield className="h-8 w-8 text-green-600" />
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white arabic-text">
            سياسة الخصوصية
          </h1>
        </div>
        <p className="text-xl text-gray-600 dark:text-gray-300 arabic-text">
          كيف نحمي ونستخدم بياناتكم الشخصية في منصة أزياء التخرج المغربية
        </p>
        <Badge variant="outline" className="mt-4">
          آخر تحديث: يناير 2024
        </Badge>
      </div>

      <div className="max-w-4xl mx-auto space-y-8">
        {/* مقدمة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <CheckCircle className="h-5 w-5 text-green-600" />
              مقدمة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              نحن في منصة أزياء التخرج المغربية نقدر خصوصيتكم ونلتزم بحماية بياناتكم الشخصية. 
              هذه السياسة توضح كيف نجمع ونستخدم ونحمي معلوماتكم الشخصية.
            </p>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              باستخدامكم لمنصتنا، فإنكم توافقون على جمع واستخدام المعلومات وفقاً لهذه السياسة.
            </p>
          </CardContent>
        </Card>

        {/* المعلومات التي نجمعها */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Database className="h-5 w-5 text-blue-600" />
              المعلومات التي نجمعها
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                  <UserCheck className="h-4 w-4 text-green-600" />
                  المعلومات الشخصية:
                </h4>
                <ul className="space-y-2 mr-6">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    الاسم الكامل
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    عنوان البريد الإلكتروني
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    رقم الهاتف
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    العنوان البريدي
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    معلومات المدرسة أو الجامعة
                  </li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-600" />
                  معلومات الاستخدام:
                </h4>
                <ul className="space-y-2 mr-6">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    عنوان IP
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    نوع المتصفح والجهاز
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    الصفحات التي تمت زيارتها
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    وقت ومدة الزيارة
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                  <Cookie className="h-4 w-4 text-orange-600" />
                  ملفات تعريف الارتباط (Cookies):
                </h4>
                <p className="text-gray-700 dark:text-gray-300 mr-6">
                  نستخدم ملفات تعريف الارتباط لتحسين تجربة الاستخدام وحفظ تفضيلاتكم.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* كيف نستخدم المعلومات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Eye className="h-5 w-5 text-purple-600" />
              كيف نستخدم المعلومات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>معالجة الطلبات:</strong> لتنفيذ طلباتكم وتوصيل المنتجات وإدارة المدفوعات.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>التواصل:</strong> لإرسال تأكيدات الطلبات وتحديثات الحالة والإشعارات المهمة.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>تحسين الخدمة:</strong> لفهم كيفية استخدام المنصة وتحسين الخدمات المقدمة.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>الأمان:</strong> لحماية المنصة من الاحتيال والاستخدام غير المشروع.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>التسويق:</strong> لإرسال عروض وأخبار المنتجات (بموافقتكم فقط).
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* مشاركة المعلومات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Share2 className="h-5 w-5 text-orange-600" />
              مشاركة المعلومات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              نحن لا نبيع أو نؤجر أو نشارك معلوماتكم الشخصية مع أطراف ثالثة، إلا في الحالات التالية:
            </p>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>شركات التوصيل:</strong> نشارك معلومات التوصيل اللازمة فقط لإتمام عملية التوصيل.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>مقدمي الخدمات:</strong> مع الشركات التي تساعدنا في تشغيل المنصة (مع اتفاقيات سرية).
                </p>
              </div>
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>المتطلبات القانونية:</strong> عند الطلب من السلطات المختصة أو للامتثال للقوانين.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>بموافقتكم:</strong> في أي حالات أخرى بموافقتكم الصريحة.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* حماية البيانات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Lock className="h-5 w-5 text-red-600" />
              حماية البيانات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              نتخذ إجراءات أمنية متقدمة لحماية بياناتكم الشخصية:
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="font-medium">تشفير البيانات</span>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="font-medium">خوادم آمنة</span>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="font-medium">مراقبة مستمرة</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="font-medium">وصول محدود</span>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="font-medium">نسخ احتياطية آمنة</span>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="font-medium">تحديثات أمنية</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* حقوقكم */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <UserCheck className="h-5 w-5 text-blue-600" />
              حقوقكم
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              لديكم الحقوق التالية فيما يتعلق ببياناتكم الشخصية:
            </p>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>الوصول:</strong> طلب نسخة من البيانات الشخصية التي نحتفظ بها عنكم.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>التصحيح:</strong> طلب تصحيح أي معلومات غير صحيحة أو غير مكتملة.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>الحذف:</strong> طلب حذف بياناتكم الشخصية في ظروف معينة.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>التحكم:</strong> إلغاء الاشتراك في الرسائل التسويقية في أي وقت.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  <strong>النقل:</strong> طلب نقل بياناتكم إلى خدمة أخرى بصيغة قابلة للقراءة.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ملفات تعريف الارتباط */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Cookie className="h-5 w-5 text-orange-600" />
              ملفات تعريف الارتباط (Cookies)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              نستخدم أنواع مختلفة من ملفات تعريف الارتباط:
            </p>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">ملفات ضرورية:</h4>
                <p className="text-gray-700 dark:text-gray-300 mr-4">
                  مطلوبة لتشغيل المنصة بشكل صحيح (تسجيل الدخول، السلة، إلخ).
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">ملفات التحليل:</h4>
                <p className="text-gray-700 dark:text-gray-300 mr-4">
                  تساعدنا في فهم كيفية استخدام المنصة وتحسينها.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">ملفات التفضيلات:</h4>
                <p className="text-gray-700 dark:text-gray-300 mr-4">
                  تحفظ إعداداتكم مثل اللغة والوضع الليلي.
                </p>
              </div>
            </div>
            <p className="text-gray-700 dark:text-gray-300 mt-4">
              يمكنكم إدارة ملفات تعريف الارتباط من خلال إعدادات المتصفح.
            </p>
          </CardContent>
        </Card>

        {/* الأطفال */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <UserCheck className="h-5 w-5 text-purple-600" />
              خصوصية الأطفال
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300">
              منصتنا غير مخصصة للأطفال دون سن 13 عاماً. نحن لا نجمع عمداً معلومات شخصية من الأطفال دون هذا السن.
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              إذا علمنا أننا جمعنا معلومات من طفل دون سن 13 عاماً، سنقوم بحذف هذه المعلومات فوراً.
            </p>
          </CardContent>
        </Card>

        {/* تحديث السياسة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              تحديث سياسة الخصوصية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300">
              قد نقوم بتحديث سياسة الخصوصية من وقت لآخر. سنقوم بإشعاركم بأي تغييرات جوهرية عبر:
            </p>
            <ul className="space-y-2 mr-4">
              <li className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-blue-600" />
                البريد الإلكتروني
              </li>
              <li className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-green-600" />
                إشعار على المنصة
              </li>
              <li className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                تحديث تاريخ "آخر تحديث"
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* التواصل */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Phone className="h-5 w-5 text-blue-600" />
              التواصل معنا
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              لأي استفسارات حول سياسة الخصوصية أو لممارسة حقوقكم، يمكنكم التواصل معنا:
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">البريد الإلكتروني:</p>
                  <p className="text-blue-600"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">الهاتف:</p>
                  <p className="text-green-600">+212-5XX-XXXXXX</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator className="my-8" />

        {/* Footer */}
        <div className="text-center text-gray-500 dark:text-gray-400 arabic-text">
          <p>© 2024 منصة أزياء التخرج المغربية. جميع الحقوق محفوظة.</p>
          <p className="mt-2">آخر تحديث: يناير 2024</p>
        </div>
      </div>
    </PageLayout>
  )
}
