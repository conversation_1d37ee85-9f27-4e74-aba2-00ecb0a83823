(()=>{var e={};e.id=2076,e.ids=[2076],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49079:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>p,serverHooks:()=>c,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{PUT:()=>d});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(38561);async function d(e){try{let{items:r}=await e.json();if(!r||!Array.isArray(r))return i.NextResponse.json({error:"قائمة العناصر مطلوبة"},{status:400});let t=u.C.getMenuItems();return r.forEach((e,r)=>{let s=t.findIndex(r=>r.id===e.id);-1!==s&&(t[s].order_index=r+1,t[s].updated_at=new Date().toISOString())}),u.C.saveMenuItems(t),i.NextResponse.json({message:"تم تحديث ترتيب القائمة بنجاح"})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/menu-items/reorder/route",pathname:"/api/menu-items/reorder",filename:"route",bundlePath:"app/api/menu-items/reorder/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\menu-items\\reorder\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:c}=p;function b(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(49079));module.exports=s})();