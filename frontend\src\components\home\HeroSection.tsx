
"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useTranslation } from "@/hooks/useTranslation"
import { Badge } from '@/components/ui/badge'
import { <PERSON>, ArrowRight } from 'lucide-react'

  GraduationCap, 
  Sparkles, 
  ArrowRight, 
  Play,
  Users,
  School,
  Star,
  ChevronDown
} from "lucide-react"

export function HeroSection() {
  const { t } = useTranslation()
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" />

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-4 sm:left-10 w-16 h-16 sm:w-20 sm:h-20 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-pulse" />
        <div className="absolute top-40 right-4 sm:right-20 w-12 h-12 sm:w-16 sm:h-16 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 animate-bounce" />
        <div className="absolute bottom-20 left-4 sm:left-20 w-20 h-20 sm:w-24 sm:h-24 bg-yellow-200 dark:bg-yellow-800 rounded-full opacity-20 animate-pulse" />
        <div className="absolute bottom-40 right-4 sm:right-10 w-10 h-10 sm:w-12 sm:h-12 bg-green-200 dark:bg-green-800 rounded-full opacity-20 animate-bounce" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 mobile-container py-12 sm:py-16 md:py-20">
        <div className="text-center max-w-5xl mx-auto">
          {/* Badges */}
          <div className={`flex flex-wrap justify-center gap-2 sm:gap-3 mb-6 sm:mb-8 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            <Badge variant="secondary" className="mobile-badge mobile-badge-primary px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium">
              <Users className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              {t('home.hero.badges.trusted')}
            </Badge>
            <Badge variant="secondary" className="mobile-badge mobile-badge-success px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium">
              <School className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              {t('home.hero.badges.schools')}
            </Badge>
            <Badge variant="secondary" className="mobile-badge mobile-badge-warning px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium">
              <Star className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              {t('home.hero.badges.satisfaction')}
            </Badge>
          </div>

          {/* Main Title */}
          <h1 className={`mobile-text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6 arabic-text leading-tight transition-all duration-1000 delay-200 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
              {t('home.hero.title')}
            </span>
          </h1>

          {/* Subtitle */}
          <p className={`mobile-text-lg sm:text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-6 sm:mb-8 max-w-4xl mx-auto arabic-text leading-relaxed transition-all duration-1000 delay-400 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            {t('home.hero.subtitle')}
          </p>

          {/* Description */}
          <p className={`mobile-text-base sm:text-lg text-gray-500 dark:text-gray-400 mb-8 sm:mb-12 max-w-3xl mx-auto arabic-text leading-relaxed transition-all duration-1000 delay-600 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            {t('home.hero.description')}
          </p>

          {/* CTA Buttons */}
          <div className={`flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            <Button
              size="lg"
              className="mobile-btn mobile-btn-primary w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 sm:px-8 py-3 sm:py-4 mobile-text-base sm:text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group"
              asChild
            >
              <a href="/customize" className="flex items-center justify-center gap-2">
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                {t('home.hero.cta.primary')}
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform" />
              </a>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="mobile-btn mobile-btn-secondary w-full sm:w-auto border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 px-6 sm:px-8 py-3 sm:py-4 mobile-text-base sm:text-lg font-semibold arabic-text transition-all duration-300"
              asChild
            >
              <a href="/catalog" className="flex items-center justify-center gap-2">
                <GraduationCap className="w-4 h-4 sm:w-5 sm:h-5" />
                {t('home.hero.cta.secondary')}
              </a>
            </Button>

            <Button
              variant="ghost"
              size="lg"
              className="mobile-btn w-full sm:w-auto text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 px-6 sm:px-8 py-3 sm:py-4 mobile-text-base sm:text-lg font-semibold arabic-text transition-all duration-300 group"
            >
              <Play className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform" />
              {t('home.hero.cta.watchDemo')}
            </Button>
          </div>

          {/* Scroll Indicator */}
          <div className={`animate-bounce transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100' : 'opacity-0'
          }`}>
            <ChevronDown className="w-8 h-8 text-gray-400 mx-auto" />
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white dark:from-gray-900 to-transparent" />
    </section>
  )
}
