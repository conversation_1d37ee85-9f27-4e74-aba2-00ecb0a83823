
"use client"

import {
  useState
} from 'react'
import {
  PageLayout
} from '@/components/layouts/PageLayout'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  Button
} from '@/components/ui/button'


import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
  Truck,
  ShoppingCart,
  Truck
} from 'lucide-react'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
}

const faqData: FAQItem[] = [
  // أسئلة عامة
  {
    id: '1',
    question: 'ما هي منصة أزياء التخرج المغربية؟',
    answer: 'منصة أزياء التخرج المغربية هي أول منصة متخصصة في المغرب لتأجير وبيع أزياء التخرج، بما في ذلك الأثواب والقبعات والأوشحة والإكسسوارات. نقدم خدماتنا للطلاب والمدارس والجامعات في جميع أنحاء المغرب.',
    category: 'عام',
    tags: ['منصة', 'تعريف', 'خدمات']
  },
  {
    id: '2',
    question: 'في أي مناطق تقدمون خدماتكم؟',
    answer: 'نقدم خدماتنا حالياً في منطقة بني ملال-خنيفرا، مع خطط للتوسع لتشمل جميع مناطق المغرب قريباً. يمكنكم التحقق من توفر الخدمة في منطقتكم عند إجراء الطلب.',
    category: 'عام',
    tags: ['مناطق', 'توصيل', 'خدمة']
  },
  
  // أسئلة الطلبات
  {
    id: '3',
    question: 'كيف يمكنني إجراء طلب؟',
    answer: 'يمكنكم إجراء طلب بسهولة من خلال: 1) تصفح الكتالوج واختيار المنتجات، 2) إضافة المنتجات للسلة، 3) إدخال معلومات التوصيل، 4) اختيار طريقة الدفع، 5) تأكيد الطلب. ستتلقون تأكيداً عبر البريد الإلكتروني.',
    category: 'طلبات',
    tags: ['طلب', 'شراء', 'خطوات']
  },
  {
    id: '4',
    question: 'هل يمكنني تعديل أو إلغاء طلبي؟',
    answer: 'يمكنكم تعديل أو إلغاء الطلب خلال ساعة من تأكيده. بعد ذلك، يرجى التواصل معنا مباشرة. إذا تم شحن الطلب، فلن يمكن إلغاؤه ولكن يمكن إرجاعه وفقاً لسياسة الإرجاع.',
    category: 'طلبات',
    tags: ['تعديل', 'إلغاء', 'طلب']
  },
  {
    id: '5',
    question: 'كم يستغرق تجهيز الطلب؟',
    answer: 'عادة ما يتم تجهيز الطلبات خلال 24 ساعة من التأكيد. للطلبات الجماعية أو المخصصة، قد تستغرق 2-3 أيام عمل. ستتلقون إشعاراً عند شحن الطلب.',
    category: 'طلبات',
    tags: ['وقت', 'تجهيز', 'شحن']
  },

  // أسئلة الدفع
  {
    id: '6',
    question: 'ما هي طرق الدفع المتاحة؟',
    answer: 'نقبل الطرق التالية: 1) الدفع عند الاستلام (نقداً)، 2) التحويل البنكي، 3) البطاقات الائتمانية (قريباً). جميع المعاملات آمنة ومحمية.',
    category: 'دفع',
    tags: ['دفع', 'طرق', 'أمان']
  },
  {
    id: '7',
    question: 'هل الدفع آمن؟',
    answer: 'نعم، جميع معاملات الدفع محمية بأحدث تقنيات التشفير. نحن لا نحتفظ بمعلومات البطاقات الائتمانية على خوادمنا، ونستخدم بوابات دفع معتمدة وآمنة.',
    category: 'دفع',
    tags: ['أمان', 'تشفير', 'حماية']
  },
  {
    id: '8',
    question: 'متى يتم خصم المبلغ؟',
    answer: 'للدفع عند الاستلام: يتم الدفع عند وصول الطلب. للتحويل البنكي: يتم التأكيد بعد استلام الحوالة. للبطاقات الائتمانية: يتم الخصم فوراً عند تأكيد الطلب.',
    category: 'دفع',
    tags: ['خصم', 'توقيت', 'مبلغ']
  },

  // أسئلة التوصيل
  {
    id: '9',
    question: 'كم تبلغ رسوم التوصيل؟',
    answer: 'رسوم التوصيل 50 درهم لجميع المناطق المخدومة. التوصيل مجاني للطلبات التي تزيد عن 500 درهم. للطلبات الجماعية (أكثر من 10 قطع)، قد نقدم خصومات خاصة على التوصيل.',
    category: 'توصيل',
    tags: ['رسوم', 'توصيل', 'مجاني']
  },
  {
    id: '10',
    question: 'كم يستغرق التوصيل؟',
    answer: 'التوصيل يتم خلال 24-48 ساعة من شحن الطلب، حسب المنطقة. للمناطق القريبة: 24 ساعة. للمناطق البعيدة: 48 ساعة. ستتلقون رقم تتبع لمراقبة حالة الشحنة.',
    category: 'توصيل',
    tags: ['وقت', 'توصيل', 'تتبع']
  },

  // أسئلة التأجير
  {
    id: '11',
    question: 'كم تبلغ مدة التأجير؟',
    answer: 'المدة الافتراضية للتأجير هي 7 أيام من تاريخ الاستلام. يمكن تمديد المدة مقابل رسوم إضافية. للمناسبات الخاصة، يمكن ترتيب مدد مخصصة.',
    category: 'تأجير',
    tags: ['مدة', 'تأجير', 'تمديد']
  },
  {
    id: '12',
    question: 'ماذا لو تأخرت في إرجاع المنتج المؤجر؟',
    answer: 'في حالة التأخير، يتم فرض رسوم إضافية قدرها 25 درهم لكل يوم تأخير. يرجى التواصل معنا إذا كنتم بحاجة لتمديد فترة التأجير لتجنب الرسوم الإضافية.',
    category: 'تأجير',
    tags: ['تأخير', 'رسوم', 'إرجاع']
  },

  // أسئلة الإرجاع
  {
    id: '13',
    question: 'ما هي سياسة الإرجاع؟',
    answer: 'للمنتجات المباعة: يمكن الإرجاع خلال 3 أيام في حالة وجود عيب أو عدم مطابقة. للمنتجات المؤجرة: يجب الإرجاع في الموعد المحدد وبنفس الحالة. المنتج يجب أن يكون في حالته الأصلية مع الفاتورة.',
    category: 'إرجاع',
    tags: ['إرجاع', 'سياسة', 'شروط']
  },
  {
    id: '14',
    question: 'من يتحمل تكلفة الإرجاع؟',
    answer: 'إذا كان الإرجاع بسبب عيب في المنتج أو خطأ منا، نتحمل تكلفة الإرجاع. إذا كان الإرجاع لأسباب شخصية، يتحمل العميل تكلفة الإرجاع.',
    category: 'إرجاع',
    tags: ['تكلفة', 'إرجاع', 'مسؤولية']
  }
]

const categories = [
  { id: 'all', name: 'جميع الأسئلة', icon: HelpCircle, color: 'bg-blue-500' },
  { id: 'عام', name: 'أسئلة عامة', icon: Settings, color: 'bg-green-500' },
  { id: 'طلبات', name: 'الطلبات', icon: ShoppingCart, color: 'bg-purple-500' },
  { id: 'دفع', name: 'الدفع', icon: CreditCard, color: 'bg-orange-500' },
  { id: 'توصيل', name: 'التوصيل', icon: Truck, color: 'bg-blue-500' },
  { id: 'تأجير', name: 'التأجير', icon: Users, color: 'bg-indigo-500' },
  { id: 'إرجاع', name: 'الإرجاع', icon: RotateCcw, color: 'bg-red-500' }
]

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const filteredFAQs = faqData.filter(item => {
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8" showFooter={true}>
      {/* Page Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <HelpCircle className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white arabic-text">
            الأسئلة الشائعة
          </h1>
        </div>
        <p className="text-xl text-gray-600 dark:text-gray-300 arabic-text">
          إجابات على أكثر الأسئلة شيوعاً حول منصة أزياء التخرج المغربية
        </p>
      </div>

      <div className="max-w-6xl mx-auto">
        {/* Search and Filter */}
        <div className="mb-8 space-y-6">
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="ابحث في الأسئلة الشائعة..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 arabic-text"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.id)}
                  className="arabic-text"
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {category.name}
                </Button>
              )
            })}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 arabic-text">لم يتم العثور على أسئلة تطابق بحثكم</p>
              </CardContent>
            </Card>
          ) : (
            filteredFAQs.map((item) => (
              <Card key={item.id} className="transition-all duration-200 hover:shadow-md">
                <CardHeader 
                  className="cursor-pointer"
                  onClick={() => toggleExpanded(item.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <HelpCircle className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <CardTitle className="text-right arabic-text text-lg">
                          {item.question}
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {item.category}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {expandedItems.includes(item.id) ? (
                      <ChevronUp className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </CardHeader>
                
                {expandedItems.includes(item.id) && (
                  <CardContent className="pt-0">
                    <div className="mr-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed arabic-text">
                        {item.answer}
                      </p>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))
          )}
        </div>

        {/* Contact Section */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle className="text-center arabic-text">
              لم تجد إجابة لسؤالك؟
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-gray-600 dark:text-gray-300 mb-6 arabic-text">
              فريق الدعم لدينا جاهز لمساعدتكم في أي وقت
            </p>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="bg-blue-100 dark:bg-blue-900 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Phone className="h-6 w-6 text-blue-600" />
                </div>
                <h4 className="font-medium arabic-text">اتصل بنا</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">+212-5XX-XXXXXX</p>
              </div>
              <div className="text-center">
                <div className="bg-green-100 dark:bg-green-900 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Mail className="h-6 w-6 text-green-600" />
                </div>
                <h4 className="font-medium arabic-text">راسلنا</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 dark:bg-purple-900 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="h-6 w-6 text-purple-600" />
                </div>
                <h4 className="font-medium arabic-text">دردشة مباشرة</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">متاح 24/7</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  )
}
