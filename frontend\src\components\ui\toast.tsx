
"use client"

import {
  useState,
  useEffect
} from 'react'
import {
  <PERSON><PERSON>
} from './button'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  X,
  CheckCircle,
  AlertCircle,
  Info,
  AlertTriangle
} from 'lucide-react'

export interface Toast {
  id: string
  title?: string
  description: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

interface ToastProps {
  toast: Toast
  onRemove: (id: string) => void
}

export function ToastComponent({ toast, onRemove }: ToastProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onRemove(toast.id)
    }, toast.duration || 5000)

    return () => clearTimeout(timer)
  }, [toast.id, toast.duration, onRemove])

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'info':
        return <Info className="h-5 w-5 text-blue-600" />
    }
  }

  const getBgColor = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'info':
        return 'bg-blue-50 border-blue-200'
    }
  }

  return (
    <div className={`rounded-lg border p-4 shadow-lg ${getBgColor()} animate-in slide-in-from-right-full`}>
      <div className="flex items-start gap-3">
        {getIcon()}
        <div className="flex-1">
          {toast.title && (
            <h4 className="font-medium text-gray-900 arabic-text">{toast.title}</h4>
          )}
          <p className="text-sm text-gray-700 arabic-text">{toast.description}</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => onRemove(toast.id)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

interface ToastContainerProps {
  toasts: Toast[]
  onRemove: (id: string) => void
}

export function ToastContainer({ toasts, onRemove }: ToastContainerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map((toast) => (
        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />
      ))}
    </div>
  )
}

// Hook لإدارة التوست
export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = Date.now().toString()
    setToasts(prev => [...prev, { ...toast, id }])
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const success = (description: string, title?: string) => {
    addToast({ type: 'success', description, title })
  }

  const error = (description: string, title?: string) => {
    addToast({ type: 'error', description, title })
  }

  const warning = (description: string, title?: string) => {
    addToast({ type: 'warning', description, title })
  }

  const info = (description: string, title?: string) => {
    addToast({ type: 'info', description, title })
  }

  return {
    toasts,
    addToast,
    removeToast,
    success,
    error,
    warning,
    info
  }
}
