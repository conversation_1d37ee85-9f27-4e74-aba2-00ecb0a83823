
"use client"

import { useState, useEffect, useRef } from 'react'
import { useTranslation } from "@/hooks/useTranslation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"


import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
  ArrowRight,
  Brain,
  Camera,
  Lightbulb,
  Wand2,
  Cpu,
  ArrowRight
} from 'lucide-react'

interface AIFeatureProps {
  icon: React.ReactNode
  title: string
  description: string
  color: string
  delay: number
  isActive?: boolean
}

function AIFeatureCard({ icon, title, description, color, delay, isActive }: AIFeatureProps) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <Card className={`h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2 ${
        isActive ? 'ring-2 ring-blue-500 shadow-lg' : ''
      }`}>
        <CardHeader className="text-center pb-4">
          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${color} group-hover:scale-110 transition-transform duration-300 relative`}>
            {icon}
            {isActive && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white">
                <div className="w-full h-full bg-green-500 rounded-full animate-ping"></div>
              </div>
            )}
          </div>
          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2">
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4">
            {description}
          </p>

          <Button
            variant="ghost" 
            size="sm" 
            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn"
          >
            جرب الآن
            <ArrowRight className="w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform" />
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

function AIModelCard({ name, provider, status, description, delay }: {
  name: string
  provider: string
  status: 'active' | 'inactive'
  description: string
  delay: number
}) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <Card className="hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Cpu className="w-5 h-5 text-blue-600" />
              <span className="font-semibold text-gray-900 dark:text-white">{name}</span>
            </div>
            <Badge variant={status === 'active' ? 'default' : 'secondary'}
              className={status === 'active' ? 'bg-green-500' : ''}
            >
              {status === 'active' ? 'نشط' : 'غير نشط'}
            </Badge>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">{provider}</p>
          <p className="text-sm text-gray-600 dark:text-gray-300 arabic-text">{description}</p>
        </CardContent>
      </Card>
    </div>
  )
}

export function AIFeaturesSection() {
  const { t } = useTranslation()

  const aiFeatures = [
    {
      icon: <Brain className="w-8 h-8 text-white" />,
      title: t('home.ai.features.assistant.title'),
      description: t('home.ai.features.assistant.description'),
      color: "bg-gradient-to-br from-blue-500 to-blue-600",
      delay: 0,
      isActive: true
    },
    {
      icon: <Camera className="w-8 h-8 text-white" />,
      title: t('home.ai.features.visualization.title'),
      description: t('home.ai.features.visualization.description'),
      color: "bg-gradient-to-br from-purple-500 to-purple-600",
      delay: 200,
      isActive: true
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-white" />,
      title: t('home.ai.features.recommendations.title'),
      description: t('home.ai.features.recommendations.description'),
      color: "bg-gradient-to-br from-yellow-500 to-yellow-600",
      delay: 400,
      isActive: true
    },
    {
      icon: <Wand2 className="w-8 h-8 text-white" />,
      title: t('home.ai.features.chat.title'),
      description: t('home.ai.features.chat.description'),
      color: "bg-gradient-to-br from-green-500 to-green-600",
      delay: 600,
      isActive: true
    }
  ]

  const aiModels = [
    {
      name: 'GPT-4',
      provider: 'OpenAI',
      status: 'active' as const,
      description: 'نموذج متقدم للمحادثة والتوصيات الذكية',
      delay: 0
    },
    {
      name: 'Claude 3',
      provider: 'Anthropic',
      status: 'active' as const,
      description: 'مساعد ذكي للتحليل والاقتراحات المخصصة',
      delay: 200
    },
    {
      name: 'Gemini Pro',
      provider: 'Google',
      status: 'active' as const,
      description: 'نموذج متعدد الوسائط للمعاينة والتصميم',
      delay: 400
    },
    {
      name: 'Mistral AI',
      provider: 'Mistral',
      status: 'inactive' as const,
      description: 'نموذج سريع للاستجابات الفورية',
      delay: 600
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Brain className="w-4 h-4" />
            الذكاء الاصطناعي
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            {t('home.ai.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text">
            {t('home.ai.subtitle')}
          </p>
        </div>

        {/* AI Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {aiFeatures.map((feature, index) => (
            <AIFeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              color={feature.color}
              delay={feature.delay}
              isActive={feature.isActive}
            />
          ))}
        </div>

        {/* AI Models Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
              نماذج الذكاء الاصطناعي المتاحة
            </h3>
            <p className="text-gray-600 dark:text-gray-300 arabic-text">
              نستخدم أحدث نماذج الذكاء الاصطناعي لتقديم أفضل تجربة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {aiModels.map((model, index) => (
              <AIModelCard
                key={index}
                name={model.name}
                provider={model.provider}
                status={model.status}
                description={model.description}
                delay={index * 100}
              />
            ))}
          </div>

          {/* AI Demo Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 mb-6">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Brain className="w-6 h-6 text-blue-600" />
                <span className="text-lg font-semibold text-gray-900 dark:text-white arabic-text">
                  جرب المساعد الذكي الآن
                </span>
              </div>
              <p className="text-gray-600 dark:text-gray-300 arabic-text mb-4">
                اطلب من مساعدنا الذكي مساعدتك في اختيار ثوب التخرج المثالي
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                  <Wand2 className="w-4 h-4 mr-2" />
                  ابدأ المحادثة
                </Button>
                <Button variant="outline">
                  <Camera className="w-4 h-4 mr-2" />
                  معاينة ثلاثية الأبعاد
                </Button>
                <Button variant="outline">
                  <Cpu className="w-4 h-4 mr-2" />
                  تخصيص ذكي
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
