
"use client"

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ThemeToggle } from '@/components/theme-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import { ErrorLayout } from '@/components/layouts/PageLayout'
import {
  RefreshCw,
  import {
  Home,
  ArrowLeft,
  GraduationCap,
  Bug,
  Home
} from 'lucide-react'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // تسجيل الخطأ في وحدة التحكم للمطورين
    console.error('Error occurred:', error)
  }, [error])

  const handleGoHome = () => {
    window.location.href = '/'
  }

  const handleGoBack = () => {
    window.history.back()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <GraduationCap className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Toqs
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <LanguageToggle />
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-16 flex items-center justify-center min-h-[calc(100vh-80px)]">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-8 text-center">
            {/* Error Icon */}
            <div className="mb-8">
              <div className="mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
                <div className="h-12 w-12 text-red-600 dark:text-red-400" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                حدث خطأ!
              </h1>
              <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 arabic-text">
                عذراً، حدث خطأ غير متوقع
              </h2>
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <p className="text-gray-600 dark:text-gray-400 text-lg mb-4 arabic-text">
                نعتذر عن هذا الإزعاج. حدث خطأ تقني أثناء معالجة طلبك.
              </p>
              <p className="text-gray-500 dark:text-gray-500 arabic-text">
                يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.
              </p>
            </div>

            {/* Error Details (for development) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mb-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left">
                <div className="flex items-center gap-2 mb-2">
                  <Bug className="h-4 w-4 text-red-600 dark:text-red-400" />
                  <h3 className="font-semibold text-red-600 dark:text-red-400">
                    تفاصيل الخطأ (وضع التطوير)
                  </h3>
                </div>
                <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-auto">
                  {error.message}
                </pre>
                {error.digest && (
                  <p className="text-xs text-gray-500 mt-2">
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={reset}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="arabic-text">إعادة المحاولة</span>
              </Button>

              <Button 
                variant="outline" 
                onClick={handleGoHome}
                className="flex items-center gap-2"
              >
                <Home className="h-4 w-4" />
                <span className="arabic-text">الصفحة الرئيسية</span>
              </Button>

              <Button 
                variant="outline" 
                onClick={handleGoBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="arabic-text">العودة للخلف</span>
              </Button>
            </div>

            {/* Additional Help */}
            <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 arabic-text">
                إذا استمر الخطأ
              </h3>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2 arabic-text">
                <p>• تأكد من اتصالك بالإنترنت</p>
                <p>• امسح ذاكرة التخزين المؤقت للمتصفح</p>
                <p>• جرب إعادة تحميل الصفحة</p>
                <p>• تواصل مع فريق الدعم إذا استمرت المشكلة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
