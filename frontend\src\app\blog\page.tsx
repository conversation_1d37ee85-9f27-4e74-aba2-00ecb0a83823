
"use client"

import {
  useState,
  useEffect
} from 'react'
import {
  useAuth
} from '@/contexts/AuthContext'
import {
  Navigation
} from '@/components/Navigation'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Button
} from '@/components/ui/button'



import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  Eye,
  Clock,
  Heart,
  ArrowRight,
  Eye
} from 'lucide-react'

// أنواع البيانات
interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: {
    name: string
    avatar: string
    role: string
  }
  category: string
  tags: string[]
  publishedAt: string
  readTime: number
  views: number
  likes: number
  comments: number
  featured: boolean
  image: string
}

interface BlogCategory {
  id: string
  name: string
  count: number
  color: string
}

export default function BlogPage() {
  const { } = useAuth() // ✅ إصلاح: إزالة متغيرات غير مستخدمة
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  // تحميل البيانات الوهمية
  useEffect(() => {
    const mockPosts: BlogPost[] = [
      {
        id: '1',
        title: 'دليل شامل لاختيار زي التخرج المثالي',
        excerpt: 'كل ما تحتاج معرفته لاختيار زي التخرج المناسب لك، من الألوان والأقمشة إلى الإكسسوارات المميزة.',
        content: 'محتوى المقال الكامل...',
        author: {
          name: 'سارة أحمد',
          avatar: '/api/placeholder/40/40',
          role: 'خبيرة الأزياء'
        },
        category: 'نصائح',
        tags: ['أزياء التخرج', 'نصائح', 'دليل'],
        publishedAt: '2024-01-20T10:00:00Z',
        readTime: 8,
        views: 1250,
        likes: 89,
        comments: 23,
        featured: true,
        image: '/api/placeholder/600/300'
      },
      {
        id: '2',
        title: 'تاريخ أزياء التخرج عبر العصور',
        excerpt: 'رحلة عبر التاريخ لاستكشاف تطور أزياء التخرج من العصور القديمة حتى اليوم.',
        content: 'محتوى المقال الكامل...',
        author: {
          name: 'محمد حسن',
          avatar: '/api/placeholder/40/40',
          role: 'مؤرخ الأزياء'
        },
        category: 'تاريخ',
        tags: ['تاريخ', 'تراث', 'ثقافة'],
        publishedAt: '2024-01-18T14:30:00Z',
        readTime: 12,
        views: 890,
        likes: 67,
        comments: 15,
        featured: false,
        image: '/api/placeholder/600/300'
      },
      {
        id: '3',
        title: 'كيفية العناية بزي التخرج بعد الحفل',
        excerpt: 'نصائح مهمة للحفاظ على زي التخرج وتنظيفه وتخزينه بالطريقة الصحيحة.',
        content: 'محتوى المقال الكامل...',
        author: {
          name: 'فاطمة علي',
          avatar: '/api/placeholder/40/40',
          role: 'خبيرة التنظيف'
        },
        category: 'العناية',
        tags: ['عناية', 'تنظيف', 'تخزين'],
        publishedAt: '2024-01-15T09:15:00Z',
        readTime: 6,
        views: 654,
        likes: 45,
        comments: 8,
        featured: false,
        image: '/api/placeholder/600/300'
      },
      {
        id: '4',
        title: 'أحدث صيحات أزياء التخرج لعام 2024',
        excerpt: 'اكتشف أحدث الاتجاهات والصيحات في عالم أزياء التخرج لهذا العام.',
        content: 'محتوى المقال الكامل...',
        author: {
          name: 'أحمد محمد',
          avatar: '/api/placeholder/40/40',
          role: 'مصمم أزياء'
        },
        category: 'صيحات',
        tags: ['صيحات', '2024', 'موضة'],
        publishedAt: '2024-01-12T16:45:00Z',
        readTime: 10,
        views: 1100,
        likes: 78,
        comments: 19,
        featured: true,
        image: '/api/placeholder/600/300'
      }
    ]

    const mockCategories: BlogCategory[] = [
      { id: '1', name: 'نصائح', count: 15, color: 'bg-blue-100 text-blue-800' },
      { id: '2', name: 'تاريخ', count: 8, color: 'bg-green-100 text-green-800' },
      { id: '3', name: 'العناية', count: 12, color: 'bg-purple-100 text-purple-800' },
      { id: '4', name: 'صيحات', count: 10, color: 'bg-pink-100 text-pink-800' },
      { id: '5', name: 'أخبار', count: 6, color: 'bg-orange-100 text-orange-800' }
    ]

    setTimeout(() => {
      setPosts(mockPosts)
      setCategories(mockCategories)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === null || post.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const featuredPosts = posts.filter(post => post.featured)
  const regularPosts = filteredPosts.filter(post => !post.featured)

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-rose-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rose-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            مدونة أزياء التخرج 📚
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            مقالات ونصائح مفيدة حول أزياء التخرج والمناسبات الخاصة
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="البحث في المقالات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 arabic-text"
                  />
                </div>
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant={selectedCategory === null ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(null)}
                  className="arabic-text"
                >
                  الكل
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.name ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.name)}
                    className="arabic-text"
                  >
                    {category.name}
                    <Badge variant="secondary" className="mr-1">
                      {category.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
              المقالات المميزة ⭐
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {featuredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gradient-to-br from-rose-100 to-orange-100 dark:from-rose-900 dark:to-orange-900"></div>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge className={categories.find(c => c.name === post.category)?.color}>
                        {post.category}
                      </Badge>
                      <Badge variant="outline" className="arabic-text">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        مميز
                      </Badge>
                    </div>
                    
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
                      {post.title}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-400 mb-4 arabic-text">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          <span className="arabic-text">{post.author.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(post.publishedAt).toLocaleDateString('en-US')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span className="arabic-text">{post.readTime} دقائق</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{post.views}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-4 w-4" />
                          <span>{post.likes}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-4 w-4" />
                          <span>{post.comments}</span>
                        </div>
                      </div>
                      
                      <Button variant="outline" size="sm" className="arabic-text">
                        قراءة المزيد
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Regular Posts */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Posts List */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 arabic-text">
              أحدث المقالات 📝
            </h2>
            
            <div className="space-y-6">
              {regularPosts.map((post) => (
                <Card key={post.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex gap-6">
                      {/* Post Image */}
                      <div className="w-32 h-24 bg-gradient-to-br from-rose-100 to-orange-100 dark:from-rose-900 dark:to-orange-900 rounded-lg flex-shrink-0"></div>
                      
                      {/* Post Content */}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={categories.find(c => c.name === post.category)?.color}>
                            {post.category}
                          </Badge>
                          <div className="flex gap-1">
                            {post.tags.slice(0, 2).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 arabic-text">
                          {post.title}
                        </h3>
                        
                        <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text line-clamp-2">
                          {post.excerpt}
                        </p>
                        
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span className="arabic-text">{post.author.name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(post.publishedAt).toLocaleDateString('en-US')}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span className="arabic-text">{post.readTime} د</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              <Eye className="h-3 w-3" />
                              <span>{post.views}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Heart className="h-3 w-3" />
                              <span>{post.likes}</span>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Share2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {regularPosts.length === 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text">
                    لا توجد مقالات
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 arabic-text">
                    {searchTerm || selectedCategory 
                      ? 'لم يتم العثور على مقالات تطابق البحث' 
                      : 'ستظهر المقالات هنا قريباً'
                    }
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">التصنيفات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.name)}
                      className={`w-full flex items-center justify-between p-2 rounded-lg transition-colors ${
                        selectedCategory === category.name
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                      }`}
                    >
                      <span className="arabic-text">{category.name}</span>
                      <Badge variant="secondary">{category.count}</Badge>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Popular Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">المقالات الأكثر قراءة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {posts
                    .sort((a, b) => b.views - a.views)
                    .slice(0, 5)
                    .map((post, index) => (
                      <div key={post.id} className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm arabic-text line-clamp-2">
                            {post.title}
                          </h4>
                          <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                            <Eye className="h-3 w-3" />
                            <span>{post.views}</span>
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(post.publishedAt).toLocaleDateString('en-US')}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter Signup */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">اشترك في النشرة الإخبارية</CardTitle>
                <CardDescription className="arabic-text">
                  احصل على أحدث المقالات والنصائح
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Input placeholder="بريدك الإلكتروني" className="arabic-text" />
                  <Button className="w-full arabic-text">
                    اشتراك
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
