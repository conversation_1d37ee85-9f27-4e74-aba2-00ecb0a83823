
"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'

import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'






import {
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { AlertCircle, Upload, Plus, Save, Eye, Star, Bell } from 'lucide-react'
  Plus,
  Star,
  Camera,
  Plus
} from 'lucide-react'

// أنواع البيانات
interface Review {
  id: string
  userId: string
  userName: string
  userAvatar: string
  productId: string
  productName: string
  productImage: string
  rating: number
  title: string
  content: string
  images: string[]
  helpful: number
  notHelpful: number
  verified: boolean
  createdAt: string
  response?: {
    content: string
    respondedAt: string
    respondedBy: string
  }
}

interface ReviewStats {
  totalReviews: number
  averageRating: number
  ratingDistribution: { [key: number]: number }
  verifiedPurchases: number
  helpfulVotes: number
}

export default function ReviewsPage() {
  const { user, profile } = useAuth()
  const [activeTab, setActiveTab] = useState('all')
  const [reviews, setReviews] = useState<Review[]>([])
  const [stats, setStats] = useState<ReviewStats | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [ratingFilter, setRatingFilter] = useState<number | null>(null)
  const [newReview, setNewReview] = useState({
    productId: '',
    rating: 0,
    title: '',
    content: '',
    images: [] as string[]
  })
  const [loading, setLoading] = useState(true)

  // تحميل البيانات الوهمية
  useEffect(() => {
    const mockReviews: Review[] = [
      {
        id: '1',
        userId: 'user1',
        userName: 'أحمد محمد',
        userAvatar: '/api/placeholder/40/40',
        productId: 'prod1',
        productName: 'زي التخرج الكلاسيكي',
        productImage: '/api/placeholder/80/80',
        rating: 5,
        title: 'منتج ممتاز وجودة عالية',
        content: 'استلمت الزي في الوقت المحدد والجودة فاقت توقعاتي. القماش ممتاز والخياطة احترافية. أنصح بشدة!',
        images: ['/api/placeholder/200/200', '/api/placeholder/200/200'],
        helpful: 15,
        notHelpful: 1,
        verified: true,
        createdAt: '2024-01-15T10:00:00Z',
        response: {
          content: 'شكراً لك على تقييمك الإيجابي! نحن سعداء لأن المنتج نال إعجابك.',
          respondedAt: '2024-01-16T09:00:00Z',
          respondedBy: 'فريق خدمة العملاء'
        }
      },
      {
        id: '2',
        userId: 'user2',
        userName: 'فاطمة علي',
        userAvatar: '/api/placeholder/40/40',
        productId: 'prod2',
        productName: 'قبعة التخرج المميزة',
        productImage: '/api/placeholder/80/80',
        rating: 4,
        title: 'جيد جداً مع ملاحظات بسيطة',
        content: 'القبعة جميلة ومريحة، لكن الشرابة كانت أقصر مما توقعت. بشكل عام راضية عن الشراء.',
        images: ['/api/placeholder/200/200'],
        helpful: 8,
        notHelpful: 2,
        verified: true,
        createdAt: '2024-01-12T14:30:00Z'
      },
      {
        id: '3',
        userId: 'user3',
        userName: 'محمد حسن',
        userAvatar: '/api/placeholder/40/40',
        productId: 'prod1',
        productName: 'زي التخرج الكلاسيكي',
        productImage: '/api/placeholder/80/80',
        rating: 3,
        title: 'متوسط، يحتاج تحسين',
        content: 'الزي وصل متأخراً قليلاً والمقاس لم يكن مضبوطاً تماماً. الجودة مقبولة لكن يمكن تحسينها.',
        images: [],
        helpful: 3,
        notHelpful: 5,
        verified: true,
        createdAt: '2024-01-10T16:45:00Z'
      }
    ]

    const mockStats: ReviewStats = {
      totalReviews: 127,
      averageRating: 4.3,
      ratingDistribution: {
        5: 65,
        4: 32,
        3: 18,
        2: 8,
        1: 4
      },
      verifiedPurchases: 98,
      helpfulVotes: 234
    }

    setTimeout(() => {
      setReviews(mockReviews)
      setStats(mockStats)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.productName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRating = ratingFilter === null || review.rating === ratingFilter
    
    return matchesSearch && matchesRating
  })

  const handleSubmitReview = () => {
    if (!newReview.rating || !newReview.title || !newReview.content) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    // محاكاة إرسال التقييم
    console.log('New review:', newReview)
    alert('تم إرسال تقييمك بنجاح! شكراً لك على مشاركة تجربتك.')
    
    setNewReview({
      productId: '',
      rating: 0,
      title: '',
      content: '',
      images: []
    })
  }

  const renderStars = (rating: number, interactive = false, onRate?: (rating: number) => void) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => interactive && onRate && onRate(star)}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
            disabled={!interactive}
          >
            <Star
              className={`h-5 w-5 ${
                star <= rating
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            التقييمات والمراجعات ⭐
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            شارك تجربتك واقرأ آراء العملاء الآخرين
          </p>
        </div>

        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
                <p className="text-2xl font-bold">{stats.averageRating}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط التقييم</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <MessageSquare className="h-8 w-8 text-blue-500" />
                </div>
                <p className="text-2xl font-bold">{stats.totalReviews}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي التقييمات</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Award className="h-8 w-8 text-green-500" />
                </div>
                <p className="text-2xl font-bold">{stats.verifiedPurchases}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">مشتريات موثقة</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <ThumbsUp className="h-8 w-8 text-purple-500" />
                </div>
                <p className="text-2xl font-bold">{stats.helpfulVotes}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">تصويتات مفيدة</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Rating Distribution */}
        {stats && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="arabic-text">توزيع التقييمات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center gap-4">
                    <div className="flex items-center gap-1 w-16">
                      <span className="text-sm font-medium">{rating}</span>
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    </div>
                    <div className="flex-1">
                      <Progress 
                        value={(stats.ratingDistribution[rating] / stats.totalReviews) * 100} 
                        className="h-2"
                      />
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
                      {stats.ratingDistribution[rating]}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all" className="arabic-text">
              <MessageSquare className="h-4 w-4 mr-2" />
              جميع التقييمات
            </TabsTrigger>
            <TabsTrigger value="write" className="arabic-text">
              <Plus className="h-4 w-4 mr-2" />
              كتابة تقييم
            </TabsTrigger>
            <TabsTrigger value="my-reviews" className="arabic-text">
              <Users className="h-4 w-4 mr-2" />
              تقييماتي
            </TabsTrigger>
          </TabsList>

          {/* All Reviews */}
          <TabsContent value="all" className="space-y-6 mt-6">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="البحث في التقييمات..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 arabic-text"
                      />
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant={ratingFilter === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => setRatingFilter(null)}
                      className="arabic-text"
                    >
                      الكل
                    </Button>
                    {[5, 4, 3, 2, 1].map((rating) => (
                      <Button
                        key={rating}
                        variant={ratingFilter === rating ? "default" : "outline"}
                        size="sm"
                        onClick={() => setRatingFilter(rating)}
                        className="flex items-center gap-1"
                      >
                        {rating}
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reviews List */}
            <div className="space-y-6">
              {filteredReviews.map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      {/* User Avatar */}
                      <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex-shrink-0">
                        <div className="w-full h-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-full"></div>
                      </div>

                      {/* Review Content */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium arabic-text">{review.userName}</h3>
                              {review.verified && (
                                <Badge variant="secondary" className="text-xs arabic-text">
                                  <Award className="h-3 w-3 mr-1" />
                                  مشترٍ موثق
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-2">
                              {renderStars(review.rating)}
                              <span className="text-sm text-gray-500">
                                {new Date(review.createdAt).toLocaleDateString('ar-SA')}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded">
                              <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded"></div>
                            </div>
                            <span className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                              {review.productName}
                            </span>
                          </div>
                        </div>

                        <h4 className="font-medium mb-2 arabic-text">{review.title}</h4>
                        <p className="text-gray-600 dark:text-gray-400 mb-3 arabic-text">
                          {review.content}
                        </p>

                        {/* Review Images */}
                        {review.images.length > 0 && (
                          <div className="flex gap-2 mb-3">
                            {review.images.map((image, index) => (
                              <div key={index} className="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded">
                                <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded"></div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Helpful Votes */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <span className="text-sm text-gray-500 arabic-text">
                              هل كان هذا التقييم مفيداً؟
                            </span>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="sm">
                                <ThumbsUp className="h-4 w-4 mr-1" />
                                {review.helpful}
                              </Button>
                              <Button variant="ghost" size="sm">
                                <ThumbsDown className="h-4 w-4 mr-1" />
                                {review.notHelpful}
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Store Response */}
                        {review.response && (
                          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="arabic-text">
                                رد من {review.response.respondedBy}
                              </Badge>
                              <span className="text-xs text-gray-500">
                                {new Date(review.response.respondedAt).toLocaleDateString('ar-SA')}
                              </span>
                            </div>
                            <p className="text-sm arabic-text">{review.response.content}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Write Review */}
          <TabsContent value="write" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">كتابة تقييم جديد</CardTitle>
                <CardDescription className="arabic-text">
                  شارك تجربتك مع المنتجات التي اشتريتها
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="product" className="arabic-text">المنتج</Label>
                  <select
                    id="product"
                    value={newReview.productId}
                    onChange={(e) => setNewReview(prev => ({ ...prev, productId: e.target.value }))}
                    className="w-full p-2 border rounded-md arabic-text"
                  >
                    <option value="">اختر المنتج</option>
                    <option value="prod1">زي التخرج الكلاسيكي</option>
                    <option value="prod2">قبعة التخرج المميزة</option>
                    <option value="prod3">وشاح التخرج الفاخر</option>
                  </select>
                </div>

                <div>
                  <Label className="arabic-text">التقييم</Label>
                  <div className="mt-2">
                    {renderStars(newReview.rating, true, (rating) => 
                      setNewReview(prev => ({ ...prev, rating }))
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="title" className="arabic-text">عنوان التقييم</Label>
                  <Input
                    id="title"
                    value={newReview.title}
                    onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="اكتب عنواناً مختصراً لتقييمك..."
                    className="arabic-text"
                  />
                </div>

                <div>
                  <Label htmlFor="content" className="arabic-text">تفاصيل التقييم</Label>
                  <Textarea
                    id="content"
                    value={newReview.content}
                    onChange={(e) => setNewReview(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="اكتب تقييمك التفصيلي هنا..."
                    rows={5}
                    className="arabic-text"
                  />
                </div>

                <div>
                  <Label className="arabic-text">إضافة صور (اختياري)</Label>
                  <div className="mt-2 flex items-center gap-4">
                    <Button variant="outline" className="arabic-text">
                      <Camera className="h-4 w-4 mr-2" />
                      إضافة صور
                    </Button>
                    <span className="text-sm text-gray-500 arabic-text">
                      يمكنك إضافة حتى 5 صور
                    </span>
                  </div>
                </div>

                <Button 
                  onClick={handleSubmitReview}
                  disabled={!newReview.rating || !newReview.title || !newReview.content}
                  className="w-full arabic-text"
                >
                  <Send className="h-4 w-4 mr-2" />
                  إرسال التقييم
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* My Reviews */}
          <TabsContent value="my-reviews" className="space-y-6 mt-6">
            <Card>
              <CardContent className="text-center py-12">
                <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text">
                  لا توجد تقييمات بعد
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6 arabic-text">
                  ابدأ بكتابة تقييمك الأول للمنتجات التي اشتريتها
                </p>
                <Button onClick={() => setActiveTab('write')} className="arabic-text">
                  <Plus className="h-4 w-4 mr-2" />
                  كتابة تقييم جديد
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
