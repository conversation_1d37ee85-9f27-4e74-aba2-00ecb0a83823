(()=>{var e={};e.id=7413,e.ids=[7413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45660:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>b,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>u,PUT:()=>p});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),d=t(38561);async function u(e,{params:r}){try{let e=d.C.getOrders().find(e=>e.id===r.id);if(!e)return i.NextResponse.json({error:"الطلب غير موجود"},{status:404});return i.NextResponse.json({order:e})}catch(e){return i.NextResponse.json({error:"خطأ في جلب الطلب"},{status:500})}}async function p(e,{params:r}){try{let{status:t,payment_status:s,tracking_number:n,delivery_date:o,notes:a,shipping_address:u}=await e.json(),p=d.C.getOrders(),x=p.findIndex(e=>e.id===r.id);if(-1===x)return i.NextResponse.json({error:"الطلب غير موجود"},{status:404});let c={...p[x],...t&&{status:t},...s&&{payment_status:s},...n&&{tracking_number:n},...o&&{delivery_date:o},...a&&{notes:a},...u&&{shipping_address:u},updated_at:new Date().toISOString()};return p[x]=c,d.C.saveOrders(p),i.NextResponse.json({message:"تم تحديث الطلب بنجاح",order:c})}catch(e){return i.NextResponse.json({error:"خطأ في تحديث الطلب"},{status:500})}}async function x(e,{params:r}){try{let e=d.C.getOrders(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return i.NextResponse.json({error:"الطلب غير موجود"},{status:404});let s=e[t];if("delivered"===s.status||"shipped"===s.status)return i.NextResponse.json({error:"لا يمكن حذف طلب تم تسليمه أو شحنه"},{status:400});return e.splice(t,1),d.C.saveOrders(e),i.NextResponse.json({message:"تم حذف الطلب بنجاح"})}catch(e){return i.NextResponse.json({error:"خطأ في حذف الطلب"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/orders/[id]/route",pathname:"/api/orders/[id]",filename:"route",bundlePath:"app/api/orders/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:l,serverHooks:b}=c;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(45660));module.exports=s})();