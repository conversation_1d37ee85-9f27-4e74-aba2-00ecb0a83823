{"version": 3, "file": "traversal.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["traversal.ts"], "names": [], "mappings": ";;AAgBA,kCAEC;AAUD,8BAEC;AAaD,kCAeC;AAUD,8CAKC;AAUD,8BAMC;AASD,0BAEC;AAUD,gDAIC;AAUD,gDAIC;AAhID,yCAOoB;AAEpB;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,IAAa;IACrC,OAAO,IAAA,wBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;AAClD,CAAC;AAGD;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,IAAa;IACnC,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAC/B,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,WAAW,CAAC,IAAa;;IACrC,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,MAAM,IAAI,IAAI;QAAE,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;IAE/C,IAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;IAClB,IAAA,IAAI,GAAW,IAAI,KAAf,EAAE,IAAI,GAAK,IAAI,KAAT,CAAU;IAC1B,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;QAClB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC,KAAW,IAAI,EAAb,IAAI,UAAA,CAAU,CAAC;IACtB,CAAC;IACD,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;QAClB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC,KAAW,IAAI,EAAb,IAAI,UAAA,CAAU,CAAC;IACtB,CAAC;IACD,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,iBAAiB,CAC7B,IAAa,EACb,IAAY;;IAEZ,OAAO,MAAA,IAAI,CAAC,OAAO,0CAAG,IAAI,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CAAC,IAAa,EAAE,IAAY;IACjD,OAAO,CACH,IAAI,CAAC,OAAO,IAAI,IAAI;QACpB,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAC7B,CAAC;AACN,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,OAAO,CAAC,IAAa;IACjC,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,kBAAkB,CAAC,IAAa;;IACtC,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;IACpB,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,IAAA,kBAAK,EAAC,IAAI,CAAC;QAAE,CAAC,KAAW,IAAI,EAAb,IAAI,UAAA,CAAU,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,kBAAkB,CAAC,IAAa;;IACtC,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;IACpB,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,IAAA,kBAAK,EAAC,IAAI,CAAC;QAAE,CAAC,KAAW,IAAI,EAAb,IAAI,UAAA,CAAU,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC"}