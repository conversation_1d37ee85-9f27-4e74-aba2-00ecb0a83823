
"use client"

import { PageLayout } from '@/components/layouts/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

import { Badge } from '@/components/ui/badge'
import { Truck } from 'lucide-react'

  FileText, 
  Shield, 
  CreditCard, 
  Truck, 
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Phone,
  Mail
} from 'lucide-react'

export default function TermsConditionsPage() {
  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8" showFooter={true}>
      {/* Page Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <FileText className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white arabic-text">
            الشروط والأحكام
          </h1>
        </div>
        <p className="text-xl text-gray-600 dark:text-gray-300 arabic-text">
          شروط وأحكام استخدام منصة أزياء التخرج المغربية
        </p>
        <Badge variant="outline" className="mt-4">
          آخر تحديث: يناير 2024
        </Badge>
      </div>

      <div className="max-w-4xl mx-auto space-y-8">
        {/* مقدمة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <CheckCircle className="h-5 w-5 text-green-600" />
              مقدمة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              مرحباً بكم في منصة أزياء التخرج المغربية، أول منصة متخصصة في تأجير وبيع أزياء التخرج في المغرب. 
              باستخدامكم لهذه المنصة، فإنكم توافقون على الالتزام بالشروط والأحكام التالية.
            </p>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              هذه الشروط والأحكام تحكم استخدامكم للموقع الإلكتروني والخدمات المقدمة من خلاله. 
              يرجى قراءة هذه الشروط بعناية قبل استخدام المنصة.
            </p>
          </CardContent>
        </Card>

        {/* تعريفات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <FileText className="h-5 w-5 text-blue-600" />
              التعريفات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="grid gap-4">
              <div className="border-r-4 border-blue-500 pr-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">المنصة:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  موقع أزياء التخرج المغربية الإلكتروني وجميع الخدمات المرتبطة به.
                </p>
              </div>
              <div className="border-r-4 border-green-500 pr-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">المستخدم:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  أي شخص يستخدم المنصة سواء كان طالباً، مدرسة، أو مؤسسة تعليمية.
                </p>
              </div>
              <div className="border-r-4 border-purple-500 pr-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">المنتجات:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  أزياء التخرج بما في ذلك الأثواب، القبعات، الأوشحة، والإكسسوارات.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* شروط الاستخدام */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Shield className="h-5 w-5 text-green-600" />
              شروط الاستخدام
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  يجب أن يكون عمر المستخدم 18 عاماً أو أكثر، أو أن يحصل على موافقة ولي الأمر.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  يجب تقديم معلومات صحيحة ودقيقة عند التسجيل وإجراء الطلبات.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  المحافظة على سرية بيانات الحساب وعدم مشاركتها مع الآخرين.
                </p>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-gray-700 dark:text-gray-300">
                  استخدام المنصة للأغراض المشروعة فقط وعدم انتهاك حقوق الآخرين.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* شروط الطلب والدفع */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <CreditCard className="h-5 w-5 text-blue-600" />
              شروط الطلب والدفع
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">الأسعار:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  جميع الأسعار معروضة بالدرهم المغربي وتشمل الضرائب المطبقة. 
                  نحتفظ بالحق في تغيير الأسعار دون إشعار مسبق.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">طرق الدفع:</h4>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    الدفع عند الاستلام
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    التحويل البنكي
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    البطاقات الائتمانية (قريباً)
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">تأكيد الطلب:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  يعتبر الطلب مؤكداً بعد استلام الدفعة أو تأكيد طريقة الدفع المختارة.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* شروط التأجير */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Calendar className="h-5 w-5 text-purple-600" />
              شروط التأجير
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">مدة التأجير:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  المدة الافتراضية للتأجير هي 7 أيام من تاريخ الاستلام. 
                  يمكن تمديد المدة مقابل رسوم إضافية.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">حالة المنتج:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  يجب إرجاع المنتج بنفس الحالة التي تم استلامه بها. 
                  أي أضرار أو تلف سيؤدي إلى رسوم إضافية.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">التأخير في الإرجاع:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  في حالة التأخير في إرجاع المنتج، سيتم فرض رسوم إضافية قدرها 25 درهم لكل يوم تأخير.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* شروط التوصيل */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Truck className="h-5 w-5 text-orange-600" />
              شروط التوصيل
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">مناطق التوصيل:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  نقوم بالتوصيل في جميع أنحاء منطقة بني ملال-خنيفرة، مع خطط للتوسع لمناطق أخرى.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">أوقات التوصيل:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  التوصيل يتم خلال 24-48 ساعة من تأكيد الطلب، حسب المنطقة والتوفر.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">رسوم التوصيل:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  رسوم التوصيل 50 درهم. التوصيل مجاني للطلبات التي تزيد عن 500 درهم.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* سياسة الإرجاع والاستبدال */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <RotateCcw className="h-5 w-5 text-red-600" />
              سياسة الإرجاع والاستبدال
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">للمنتجات المباعة:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  يمكن إرجاع المنتجات خلال 3 أيام من الاستلام في حالة وجود عيب في التصنيع أو عدم مطابقة الوصف.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">للمنتجات المؤجرة:</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  يجب إرجاع المنتجات المؤجرة في الموعد المحدد وبنفس الحالة التي تم استلامها بها.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">شروط الإرجاع:</h4>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    المنتج في حالته الأصلية
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    وجود فاتورة الشراء أو إيصال التأجير
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    عدم استخدام المنتج (للمبيعات)
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* المسؤولية وإخلاء المسؤولية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              المسؤولية وإخلاء المسؤولية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <div className="space-y-4">
              <p className="text-gray-700 dark:text-gray-300">
                نحن نبذل قصارى جهدنا لضمان دقة المعلومات المعروضة على المنصة، 
                لكننا لا نضمن خلوها من الأخطاء أو عدم انقطاع الخدمة.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                لا نتحمل المسؤولية عن أي أضرار مباشرة أو غير مباشرة قد تنتج عن استخدام المنصة أو المنتجات.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                المستخدم مسؤول عن استخدام المنصة بطريقة قانونية ومناسبة.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* تعديل الشروط */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <FileText className="h-5 w-5 text-gray-600" />
              تعديل الشروط والأحكام
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300">
              نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت. 
              سيتم إشعار المستخدمين بأي تغييرات جوهرية عبر البريد الإلكتروني أو إشعار على المنصة.
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              استمرار استخدام المنصة بعد التعديلات يعني موافقتكم على الشروط الجديدة.
            </p>
          </CardContent>
        </Card>

        {/* معلومات التواصل */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 arabic-text">
              <Phone className="h-5 w-5 text-blue-600" />
              التواصل معنا
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 arabic-text">
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              لأي استفسارات حول هذه الشروط والأحكام، يمكنكم التواصل معنا:
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">البريد الإلكتروني:</p>
                  <p className="text-blue-600"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">الهاتف:</p>
                  <p className="text-green-600">+212-5XX-XXXXXX</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator className="my-8" />

        {/* Footer */}
        <div className="text-center text-gray-500 dark:text-gray-400 arabic-text">
          <p>© 2024 منصة أزياء التخرج المغربية. جميع الحقوق محفوظة.</p>
          <p className="mt-2">آخر تحديث: يناير 2024</p>
        </div>
      </div>
    </PageLayout>
  )
}
