
"use client"

import {
  useState
} from 'react'
import {
  But<PERSON>
} from '@/components/ui/button'

import {
  Switch
} from '@/components/ui/switch'
import { MobileCard,
  MobileCardContent
} from '@/components/ui/mobile-card'

import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  Eye
} from 'lucide-react'

interface MenuItem {
  id: string
  title_ar: string
  title_en: string
  title_fr: string
  slug: string
  icon: string
  parent_id: string | null
  target_type: 'internal' | 'external'
  target_value: string
  is_active: boolean
  order_index: number
  children?: MenuItem[]
}

interface MenuItemCardProps {
  item: MenuItem
  level?: number
  onEdit: (item: MenuItem) => void
  onDelete: (id: string) => void
  onToggleStatus: (item: MenuItem) => void
  dragHandleProps?: any
  isDragging?: boolean
}

export function MenuItemCard({
  item,
  level = 0,
  onEdit,
  onDelete,
  onToggleStatus,
  dragHandleProps,
  isDragging = false
}: MenuItemCardProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const hasChildren = item.children && item.children.length > 0

  const getTargetIcon = () => {
    switch (item.target_type) {
      case 'external':
        return <ExternalLink className="h-4 w-4 text-blue-500" />
      default:
        return <LinkIcon className="h-4 w-4 text-green-500" />
    }
  }

  const getTargetLabel = () => {
    switch (item.target_type) {
      case 'external':
        return 'رابط خارجي'
      default:
        return 'صفحة داخلية'
    }
  }

  return (
    <div className={`${level > 0 ? 'mr-6 sm:mr-8' : ''}`}>
      <MobileCard 
        variant="outlined"
        className={`mb-3 transition-all duration-200 ${
          isDragging ? 'opacity-50 scale-95' : 'hover:shadow-md'
        } ${!item.is_active ? 'opacity-60' : ''}`}
      >
        <MobileCardContent className="p-4">
          <div className="flex items-start gap-3">
            {/* Drag Handle */}
            <div 
              {...dragHandleProps}
              className="touch-target cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              <GripVertical className="h-5 w-5 text-gray-400" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              {/* Header */}
              <div className="flex items-start justify-between gap-3 mb-3">
                <div className="flex-1 min-w-0">
                  {/* Title */}
                  <div className="flex items-center gap-2 mb-2">
                    {hasChildren && (
                      <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="touch-target p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        )}
                      </button>
                    )}
                    
                    {item.icon && (
                      <span className="text-lg">{item.icon}</span>
                    )}
                    
                    <h3 className="mobile-text-base font-semibold text-gray-900 dark:text-white arabic-text truncate">
                      {item.title_ar}
                    </h3>
                  </div>

                  {/* Translations */}
                  <div className="space-y-1 mb-3">
                    {item.title_en && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-3 w-3 text-gray-400" />
                        <span className="mobile-text-sm text-gray-600 dark:text-gray-400">
                          EN: {item.title_en}
                        </span>
                      </div>
                    )}
                    {item.title_fr && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-3 w-3 text-gray-400" />
                        <span className="mobile-text-sm text-gray-600 dark:text-gray-400">
                          FR: {item.title_fr}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Target Info */}
                  <div className="flex items-center gap-2 mb-3">
                    {getTargetIcon()}
                    <span className="mobile-text-sm text-gray-600 dark:text-gray-400">
                      {getTargetLabel()}
                    </span>
                    {item.target_value && (
                      <span className="mobile-text-sm text-blue-600 dark:text-blue-400 truncate">
                        {item.target_value}
                      </span>
                    )}
                  </div>

                  {/* Badges */}
                  <div className="flex flex-wrap gap-2 mb-3">
                    <Badge 
                      variant={item.is_active ? "default" : "secondary"}
                      className="mobile-text-sm"
                    >
                      {item.is_active ? 'مفعل' : 'غير مفعل'}
                    </Badge>
                    
                    {item.slug && (
                      <Badge variant="outline" className="mobile-text-sm">
                        /{item.slug}
                      </Badge>
                    )}
                    
                    {hasChildren && (
                      <Badge variant="outline" className="mobile-text-sm">
                        {item.children!.length} عنصر فرعي
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex flex-col sm:flex-row items-end sm:items-center gap-2">
                  {/* Status Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={item.is_active}
                      onCheckedChange={() => onToggleStatus(item)}
                      className="touch-target"
                    />
                    {item.is_active ? (
                      <Eye className="h-4 w-4 text-green-500" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(item)}
                      className="touch-target mobile-button h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(item.id)}
                      className="touch-target mobile-button h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Children */}
          {hasChildren && isExpanded && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="space-y-2">
                {item.children!.map((child) => (
                  <MenuItemCard
                    key={child.id}
                    item={child}
                    level={level + 1}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onToggleStatus={onToggleStatus}
                  />
                ))}
              </div>
            </div>
          )}
        </MobileCardContent>
      </MobileCard>
    </div>
  )
}
