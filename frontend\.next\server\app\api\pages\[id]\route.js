(()=>{var e={};e.id=6684,e.ids=[6684],e.modules={2504:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{DELETE:()=>x,GET:()=>d,PUT:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(38561);async function d(e,{params:t}){try{let{searchParams:r}=new URL(e.url),s=r.get("language")||"ar",n=u.C.getPages().find(e=>e.id===t.id);if(!n)return i.NextResponse.json({error:"الصفحة غير موجودة"},{status:404});let a=n.page_content.filter(e=>e.language===s);return i.NextResponse.json({page:{...n,page_content:a}})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function p(e,{params:t}){try{let{slug:r,is_published:s,featured_image:n,content:a}=await e.json(),o=u.C.getPages(),d=o.findIndex(e=>e.id===t.id);if(-1===d)return i.NextResponse.json({error:"الصفحة غير موجودة"},{status:404});if(r&&r!==o[d].slug&&o.find(e=>e.slug===r&&e.id!==t.id))return i.NextResponse.json({error:"الرابط المختصر موجود بالفعل"},{status:400});let p={...o[d],slug:r||o[d].slug,is_published:s??o[d].is_published,featured_image:n??o[d].featured_image,updated_at:new Date().toISOString()};if(a){let e=[];["ar","en","fr"].forEach(r=>{a[r]?.title&&e.push({id:`${t.id}-${r}`,page_id:t.id,language:r,title:a[r].title,content:a[r].content||"",meta_description:a[r].meta_description||"",meta_keywords:a[r].meta_keywords||""})}),p.page_content=e}return o[d]=p,u.C.savePages(o),i.NextResponse.json({message:"تم تحديث الصفحة بنجاح",page:p})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function x(e,{params:t}){try{let e=u.C.getPages(),r=e.findIndex(e=>e.id===t.id);if(-1===r)return i.NextResponse.json({error:"الصفحة غير موجودة"},{status:404});return e.splice(r,1),u.C.savePages(e),i.NextResponse.json({message:"تم حذف الصفحة بنجاح"})}catch(e){return i.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/pages/[id]/route",pathname:"/api/pages/[id]",filename:"route",bundlePath:"app/api/pages/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\pages\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:l,serverHooks:m}=c;function b(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:l})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(2504));module.exports=s})();