
'use client'

import { useState, useEffect } from 'react'
import { PageProject, PageTemplate, AIGenerationRequest } from '@/types/page-builder'
import { MockDataManager } from '@/lib/mockData'
import { Button } from '@/components/ui/button'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { toast } from 'sonner'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Upload, Plus, Save, Eye, RefreshCw, Wand2 } from 'lucide-react'

  Wand2,
  Layout,
  Palette,
  Eye,
  Save,
  Download,
  Upload,
  Plus,
  Grid,
  Smartphone,
  Tablet,
  Monitor,
  Zap,
  Sparkles,
  RefreshCw,
  GraduationCap,
  Home,
  ShoppingBag,
  Info,
  Phone,
  Grid3X3,
  FileText,
  Link as LinkIcon,
  ExternalLink,
  User,
  Settings
} from 'lucide-react'

interface PageBuilderProps {
  project?: PageProject
  onSave?: (project: PageProject) => void
  onPreview?: (project: PageProject) => void
  onPublish?: (project: PageProject) => void
}

// دالة للحصول على مكون الأيقونة المناسب
const getIconComponent = (iconName: string) => {
  const iconMap: { [key: string]: any } = {
    'Home': Home,
    'ShoppingBag': ShoppingBag,
    'Info': Info,
    'Phone': Phone,
    'FileText': FileText,
    'Link': LinkIcon,
    'ExternalLink': ExternalLink,
    'User': User,
    'Settings': Settings,
    'GraduationCap': GraduationCap,
    'Grid3X3': Grid3X3
  }

  return iconMap[iconName] || Home
}

export function PageBuilder({ project, onSave, onPreview, onPublish }: PageBuilderProps) {
  const [currentProject, setCurrentProject] = useState<PageProject | null>(project || null)
  const [templates, setTemplates] = useState<PageTemplate[]>([])
  const [showAIDialog, setShowAIDialog] = useState(false)
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [aiPrompt, setAiPrompt] = useState('')
  const [pageName, setPageName] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<PageTemplate | null>(null)
  const [includeMainHeader, setIncludeMainHeader] = useState(true)
  const [mainMenuItems, setMainMenuItems] = useState<any[]>([])
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  const [selectedAIModel, setSelectedAIModel] = useState('')
  const [pageCategory, setPageCategory] = useState('')
  const [targetAudience, setTargetAudience] = useState('')
  const [businessType, setBusinessType] = useState('')
  const [selectedColors, setSelectedColors] = useState<string[]>(['#3B82F6', '#8B5CF6'])
  const [includeImages, setIncludeImages] = useState(true)
  const [includeText, setIncludeText] = useState(true)
  const [pageStyle, setPageStyle] = useState('modern')
  const [generationProgress, setGenerationProgress] = useState(0)
  const [availableModels, setAvailableModels] = useState<any[]>([])
  const [isLoadingModels, setIsLoadingModels] = useState(false)

  // جلب القوالب
  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/page-builder/templates')
      const data = await response.json()

      if (response.ok) {
        setTemplates(data.templates)
      } else {
        toast.error(data.error || 'خطأ في جلب القوالب')
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // جلب النماذج المتاحة
  const fetchAvailableModels = () => {
    setIsLoadingModels(true)
    try {
      // جلب المزودين من localStorage مباشرة
      const stored = localStorage.getItem('mockData_aiProviders')
      const allProviders = stored ? JSON.parse(stored) : []

      console.log('All providers from localStorage:', allProviders)

      // فلترة المزودين النشطين فقط
      const activeProviders = allProviders.filter((p: any) => p.status === 'active')

      console.log('Active AI providers loaded:', activeProviders)

      // تحديث state
      setAvailableModels(activeProviders)

      // تحديد النموذج الافتراضي
      setTimeout(() => {
        if (activeProviders.length > 0) {
          const firstProvider = activeProviders[0]
          if (firstProvider.models && firstProvider.models.length > 0) {
            setSelectedAIModel(`${firstProvider.id}-${firstProvider.models[0]}`)
            console.log('Default model selected:', `${firstProvider.id}-${firstProvider.models[0]}`)
          }
        } else {
          toast.error('لا توجد نماذج ذكاء اصطناعي نشطة. يرجى إضافة وتفعيل مزود أولاً.')
        }
      }, 100)

    } catch (error) {
      console.error('Error fetching AI models:', error)
      toast.error('فشل في جلب نماذج الذكاء الاصطناعي')
      setAvailableModels([])
    } finally {
      setIsLoadingModels(false)
    }
  }

  // جلب عناصر القائمة الرئيسية
  const fetchMainMenuItems = async () => {
    try {
      const response = await fetch('/api/menu-items')
      const data = await response.json()

      if (response.ok) {
        setMainMenuItems(data.menuItems || [])
      }
    } catch (error) {
      console.error('Error fetching menu items:', error)
    }
  }

  useEffect(() => {
    fetchTemplates()
    fetchMainMenuItems()
    fetchAvailableModels()
  }, [])

  // مراقبة تغيير availableModels
  useEffect(() => {
    console.log('Available models state updated:', availableModels)
  }, [availableModels])

  // إنشاء مشروع جديد
  const createNewProject = async (name: string, templateId?: string) => {
    try {
      const response = await fetch('/api/page-builder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name,
          templateId,
          generationMode: templateId ? 'template' : 'manual'
        })
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setCurrentProject(data.project)
        toast.success(data.message)
      } else {
        toast.error(data.error)
      }
    } catch (error) {
      console.error('Error creating project:', error)
      toast.error('خطأ في إنشاء المشروع')
    }
  }

  // توليد صفحة بالذكاء الاصطناعي
  const generateWithAI = async () => {
    if (!aiPrompt.trim()) {
      toast.error('يرجى إدخال وصف للصفحة')
      return
    }

    if (!pageName.trim()) {
      toast.error('يرجى إدخال اسم للصفحة')
      return
    }

    setIsGenerating(true)
    setGenerationProgress(0)

    // محاكاة تقدم التوليد
    const progressInterval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval)
          return 90
        }
        return prev + Math.random() * 15
      })
    }, 500)

    try {
      const generationRequest: AIGenerationRequest = {
        prompt: aiPrompt,
        language: 'ar',
        category: pageCategory as any,
        style: pageStyle,
        colors: selectedColors,
        includeImages: includeImages,
        includeText: includeText,
        pageType: pageCategory,
        targetAudience: targetAudience,
        businessType: businessType,
        modelId: selectedAIModel,
        includeMainHeader: includeMainHeader,
        mainMenuItems: includeMainHeader ? mainMenuItems : []
      }

      const response = await fetch('/api/page-builder/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(generationRequest)
      })
      
      const data = await response.json()
      
      if (response.ok && data.success) {
        // إنشاء مشروع جديد مع المكونات المولدة
        const newProject: PageProject = {
          id: Date.now().toString(),
          name: pageName.trim(),
          description: `مولد من: ${aiPrompt}`,
          components: data.components || [],
          generationMode: 'ai',
          settings: {
            title: pageName.trim(),
            description: aiPrompt,
            keywords: [],
            language: 'ar',
            direction: 'rtl'
          },
          isPublished: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'admin-1',
          version: 1
        }

        // حفظ المشروع في localStorage
        const existingProjects = MockDataManager.getPageProjects()
        existingProjects.push(newProject)
        MockDataManager.savePageProjects(existingProjects)

        setCurrentProject(newProject)
        setShowAIDialog(false)
        setAiPrompt('')
        setPageName('')

        toast.success('تم توليد الصفحة بنجاح!')
        
        if (data.suggestions && data.suggestions.length > 0) {
          toast.info(`اقتراحات للتحسين: ${data.suggestions[0]}`)
        }
      } else {
        toast.error(data.error || 'فشل في توليد الصفحة')
      }
    } catch (error) {
      console.error('Error generating page:', error)
      toast.error('خطأ في توليد الصفحة')
    } finally {
      clearInterval(progressInterval)
      setGenerationProgress(100)
      setTimeout(() => {
        setIsGenerating(false)
        setGenerationProgress(0)
      }, 1000)
    }
  }

  // استخدام قالب
  const useTemplate = async (template: PageTemplate) => {
    try {
      const projectName = `مشروع من قالب: ${template.nameAr}`
      
      const newProject: PageProject = {
        id: Date.now().toString(),
        name: projectName,
        description: `مبني على قالب: ${template.nameAr}`,
        components: template.components,
        templateId: template.id,
        generationMode: 'template',
        settings: {
          title: projectName,
          description: template.description,
          keywords: template.tags,
          language: 'ar',
          direction: 'rtl'
        },
        isPublished: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin-1',
        version: 1
      }

      setCurrentProject(newProject)
      setShowTemplateDialog(false)
      
      // تحديث إحصائيات استخدام القالب
      await fetch('/api/page-builder/templates', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: template.id,
          action: 'increment_usage'
        })
      })
      
      toast.success('تم تطبيق القالب بنجاح!')
    } catch (error) {
      console.error('Error using template:', error)
      toast.error('خطأ في تطبيق القالب')
    }
  }

  // حفظ المشروع
  const saveProject = async () => {
    if (!currentProject) return

    try {
      await onSave?.(currentProject)
      toast.success('تم حفظ المشروع بنجاح')
    } catch (error) {
      console.error('Error saving project:', error)
      toast.error('خطأ في حفظ المشروع')
    }
  }

  // معاينة المشروع
  const previewProject = () => {
    if (!currentProject) return
    onPreview?.(currentProject)
  }

  // نشر المشروع
  const publishProject = async () => {
    if (!currentProject) return

    try {
      await onPublish?.(currentProject)
      toast.success('تم نشر المشروع بنجاح')
    } catch (error) {
      console.error('Error publishing project:', error)
      toast.error('خطأ في نشر المشروع')
    }
  }

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'desktop': return <Monitor className="h-4 w-4" />
      case 'tablet': return <Tablet className="h-4 w-4" />
      case 'mobile': return <Smartphone className="h-4 w-4" />
      default: return <Monitor className="h-4 w-4" />
    }
  }

  const getDeviceWidth = (device: string) => {
    switch (device) {
      case 'desktop': return '100%'
      case 'tablet': return '768px'
      case 'mobile': return '375px'
      default: return '100%'
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* شريط الأدوات */}
      <div className="border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-semibold arabic-text">
              {currentProject ? currentProject.name : 'بناء الصفحات الذكية'}
            </h2>
            {currentProject && (
              <Badge variant="outline">
                {currentProject.generationMode === 'ai' ? '🤖 مولد بالذكاء الاصطناعي' :
                 currentProject.generationMode === 'template' ? '📋 من قالب' : '✏️ يدوي'}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* أزرار المعاينة */}
            <div className="flex items-center border rounded-lg">
              {(['desktop', 'tablet', 'mobile'] as const).map((device) => (
                <Button
                  key={device}
                  variant={previewDevice === device ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setPreviewDevice(device)}
                  className="rounded-none first:rounded-l-lg last:rounded-r-lg"
                >
                  {getDeviceIcon(device)}
                </Button>
              ))}
            </div>

            {currentProject && (
              <>
                <Button variant="outline" onClick={previewProject}>
                  <Eye className="h-4 w-4 mr-2" />
                  معاينة
                </Button>
                <Button variant="outline" onClick={saveProject}>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ
                </Button>
                <Button onClick={publishProject}>
                  <Upload className="h-4 w-4 mr-2" />
                  نشر
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* الشريط الجانبي */}
        <div className="w-80 border-r bg-muted/30 p-4 space-y-4">
          <div className="space-y-2">
            <Button 
              onClick={() => setShowAIDialog(true)} 
              className="w-full justify-start"
              variant="outline"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              توليد بالذكاء الاصطناعي
            </Button>
            
            <Button 
              onClick={() => setShowTemplateDialog(true)} 
              className="w-full justify-start"
              variant="outline"
            >
              <Layout className="h-4 w-4 mr-2" />
              اختيار قالب
            </Button>
            
            <Button 
              onClick={() => createNewProject('مشروع جديد')} 
              className="w-full justify-start"
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              مشروع فارغ
            </Button>
          </div>

          {currentProject && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">تفاصيل المشروع</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div>
                  <Label>الاسم:</Label>
                  <p className="text-muted-foreground">{currentProject.name}</p>
                </div>
                <div>
                  <Label>المكونات:</Label>
                  <p className="text-muted-foreground">{currentProject.components.length}</p>
                </div>
                <div>
                  <Label>آخر تحديث:</Label>
                  <p className="text-muted-foreground">
                    {new Date(currentProject.updatedAt).toLocaleDateString('ar-MA')}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* منطقة المعاينة */}
        <div className="flex-1 p-4 bg-gray-50 dark:bg-gray-900">
          {currentProject ? (
            <div className="h-full flex items-center justify-center">
              <div 
                className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden"
                style={{ 
                  width: getDeviceWidth(previewDevice),
                  height: '80vh',
                  maxWidth: '100%'
                }}
              >
                <div className="h-full overflow-y-auto">
                  {currentProject.components.length > 0 ? (
                    <div className="space-y-0">
                      {/* هيدر القائمة الرئيسية المحسن */}
                      {includeMainHeader && (
                        <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg p-4 sticky top-0 z-50">
                          <div className="max-w-7xl mx-auto flex items-center justify-between">
                            {/* الشعار والعنوان */}
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                                  <GraduationCap className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                  <div className="text-xl font-bold text-gray-900 dark:text-white">
                                    منصة أزياء التخرج
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    Graduation Toqs Platform
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* القائمة الرئيسية */}
                            <nav className="hidden lg:flex items-center gap-1">
                              {mainMenuItems.slice(0, 6).map((item, index) => {
                                const IconComponent = getIconComponent(item.icon)
                                return (
                                  <a
                                    key={index}
                                    href="#"
                                    className="flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
                                  >
                                    {IconComponent && <IconComponent className="h-4 w-4" />}
                                    <span>{item.title_ar || item.title}</span>
                                  </a>
                                )
                              })}
                            </nav>

                            {/* أزرار التفاعل */}
                            <div className="flex items-center gap-3">
                              <div className="hidden md:flex items-center gap-2">
                                <Button size="sm" variant="ghost" className="text-gray-600 dark:text-gray-400">
                                  تسجيل الدخول
                                </Button>
                                <Button size="sm" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                                  إنشاء حساب
                                </Button>
                              </div>

                              {/* قائمة الهاتف المحمول */}
                              <Button size="sm" variant="ghost" className="lg:hidden">
                                <Grid3X3 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* مكونات الصفحة */}
                      {currentProject.components.map((component, index) => (
                        <div
                          key={component.id}
                          className="border-2 border-dashed border-transparent hover:border-blue-300 transition-colors group relative"
                          style={{
                            height: component.size.height,
                            backgroundColor: component.props.style?.backgroundColor || '#f9fafb',
                            color: component.props.style?.color || '#111827',
                            padding: component.props.style?.padding || '1rem',
                            textAlign: component.props.style?.textAlign || 'right'
                          }}
                        >
                          <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Badge variant="secondary" className="text-xs">
                              {component.type}
                            </Badge>
                          </div>
                          <div className="arabic-text">
                            {component.props.content || `مكون ${component.type}`}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="h-full flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <Layout className="h-12 w-12 mx-auto mb-4" />
                        <p>لا توجد مكونات بعد</p>
                        <p className="text-sm">ابدأ بإضافة مكونات للصفحة</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <Sparkles className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-xl font-semibold mb-2">مرحباً ببناء الصفحات الذكية</h3>
                <p className="text-muted-foreground mb-6">
                  ابدأ بإنشاء صفحة جديدة باستخدام الذكاء الاصطناعي أو اختر قالباً جاهزاً
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={() => setShowAIDialog(true)}>
                    <Wand2 className="h-4 w-4 mr-2" />
                    توليد بالذكاء الاصطناعي
                  </Button>
                  <Button variant="outline" onClick={() => setShowTemplateDialog(true)}>
                    <Layout className="h-4 w-4 mr-2" />
                    اختيار قالب
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* حوار التوليد بالذكاء الاصطناعي */}
      <Dialog open={showAIDialog} onOpenChange={setShowAIDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="arabic-text flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              توليد صفحة بالذكاء الاصطناعي
            </DialogTitle>
            <DialogDescription>
              صف الصفحة التي تريد إنشاءها وسيقوم الذكاء الاصطناعي بتوليدها لك
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* اسم الصفحة */}
            <div className="space-y-2">
              <Label htmlFor="pageName">اسم الصفحة *</Label>
              <Input
                id="pageName"
                value={pageName}
                onChange={(e) => setPageName(e.target.value)}
                placeholder="مثال: صفحة هبوط شركة التقنية"
                className="arabic-text"
              />
            </div>

            {/* وصف الصفحة */}
            <div className="space-y-2">
              <Label htmlFor="aiPrompt">وصف الصفحة *</Label>
              <Textarea
                id="aiPrompt"
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder="مثال: أريد صفحة هبوط لشركة أزياء التخرج تتضمن قسم البطل وعرض المنتجات ونموذج اتصال..."
                rows={4}
                className="arabic-text"
              />
            </div>

            {/* الخيارات المتقدمة */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">الخيارات المتقدمة</Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                >
                  {showAdvancedOptions ? 'إخفاء' : 'إظهار'}
                </Button>
              </div>

              {showAdvancedOptions && (
                <div className="space-y-4 p-4 border rounded-lg bg-muted/30">
                  <Tabs defaultValue="basic" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic">الأساسيات</TabsTrigger>
                      <TabsTrigger value="design">التصميم</TabsTrigger>
                      <TabsTrigger value="ai">الذكاء الاصطناعي</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4">
                      {/* فئة الصفحة */}
                      <div className="space-y-2">
                        <Label>فئة الصفحة</Label>
                        <Select value={pageCategory} onValueChange={setPageCategory}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر فئة الصفحة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="landing">صفحة هبوط</SelectItem>
                            <SelectItem value="business">صفحة أعمال</SelectItem>
                            <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                            <SelectItem value="portfolio">معرض أعمال</SelectItem>
                            <SelectItem value="blog">مدونة</SelectItem>
                            <SelectItem value="education">تعليمية</SelectItem>
                            <SelectItem value="fashion">أزياء</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* الجمهور المستهدف */}
                      <div className="space-y-2">
                        <Label>الجمهور المستهدف</Label>
                        <Input
                          value={targetAudience}
                          onChange={(e) => setTargetAudience(e.target.value)}
                          placeholder="مثال: طلاب الجامعات، الخريجين الجدد"
                        />
                      </div>

                      {/* نوع العمل */}
                      <div className="space-y-2">
                        <Label>نوع العمل</Label>
                        <Input
                          value={businessType}
                          onChange={(e) => setBusinessType(e.target.value)}
                          placeholder="مثال: تأجير أزياء التخرج، خدمات تعليمية"
                        />
                      </div>

                      {/* خيار تضمين الهيدر */}
                      <div className="flex items-start space-x-3 space-x-reverse">
                        <input
                          type="checkbox"
                          id="includeMainHeader"
                          checked={includeMainHeader}
                          onChange={(e) => setIncludeMainHeader(e.target.checked)}
                          className="rounded mt-1"
                        />
                      <div className="flex-1">
                        <Label htmlFor="includeMainHeader" className="text-sm font-medium">
                          تضمين هيدر القائمة الرئيسية المحسن
                        </Label>
                        <p className="text-xs text-muted-foreground mt-1">
                          إضافة هيدر احترافي مع شعار المنصة وعناصر التنقل الرئيسية
                        </p>
                      </div>
                    </div>

                    {/* معاينة الهيدر */}
                    {includeMainHeader && (
                      <div className="space-y-3">
                        <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/30 p-3 rounded border-r-4 border-blue-400">
                          <div className="flex items-center gap-2 mb-2">
                            <GraduationCap className="h-4 w-4 text-blue-600" />
                            <strong>معاينة الهيدر المضمن:</strong>
                          </div>
                          <ul className="space-y-1 text-xs">
                            <li>• شعار المنصة مع تصميم متدرج</li>
                            <li>• عناصر التنقل مع أيقونات تفاعلية</li>
                            <li>• أزرار تسجيل الدخول وإنشاء الحساب</li>
                            <li>• دعم كامل للوضع الليلي والنهاري</li>
                            <li>• تصميم متجاوب لجميع الأجهزة</li>
                          </ul>
                        </div>

                        {/* عرض عناصر القائمة المتاحة */}
                        {mainMenuItems.length > 0 && (
                          <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded">
                            <p className="text-xs font-medium mb-2">عناصر القائمة المتاحة ({mainMenuItems.length}):</p>
                            <div className="flex flex-wrap gap-1">
                              {mainMenuItems.slice(0, 6).map((item, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {item.title_ar || item.title}
                                </Badge>
                              ))}
                              {mainMenuItems.length > 6 && (
                                <Badge variant="outline" className="text-xs">
                                  +{mainMenuItems.length - 6} المزيد
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    </TabsContent>

                    <TabsContent value="design" className="space-y-4">
                      {/* نمط الصفحة */}
                      <div className="space-y-2">
                        <Label>نمط التصميم</Label>
                        <Select value={pageStyle} onValueChange={setPageStyle}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نمط التصميم" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="modern">عصري</SelectItem>
                            <SelectItem value="classic">كلاسيكي</SelectItem>
                            <SelectItem value="minimal">بسيط</SelectItem>
                            <SelectItem value="elegant">أنيق</SelectItem>
                            <SelectItem value="creative">إبداعي</SelectItem>
                            <SelectItem value="professional">احترافي</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* الألوان */}
                      <div className="space-y-2">
                        <Label>الألوان الأساسية</Label>
                        <div className="grid grid-cols-4 gap-2">
                          {[
                            '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B',
                            '#EF4444', '#6366F1', '#EC4899', '#14B8A6'
                          ].map((color) => (
                            <button
                              key={color}
                              type="button"
                              className={`w-12 h-12 rounded-lg border-2 transition-all ${
                                selectedColors.includes(color)
                                  ? 'border-gray-900 dark:border-white scale-110'
                                  : 'border-gray-300 hover:scale-105'
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => {
                                if (selectedColors.includes(color)) {
                                  setSelectedColors(selectedColors.filter(c => c !== color))
                                } else if (selectedColors.length < 3) {
                                  setSelectedColors([...selectedColors, color])
                                }
                              }}
                            />
                          ))}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          اختر حتى 3 ألوان ({selectedColors.length}/3)
                        </p>
                      </div>

                      {/* خيارات المحتوى */}
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <input
                            type="checkbox"
                            id="includeImages"
                            checked={includeImages}
                            onChange={(e) => setIncludeImages(e.target.checked)}
                            className="rounded"
                          />
                          <Label htmlFor="includeImages">تضمين الصور</Label>
                        </div>

                        <div className="flex items-center space-x-3 space-x-reverse">
                          <input
                            type="checkbox"
                            id="includeText"
                            checked={includeText}
                            onChange={(e) => setIncludeText(e.target.checked)}
                            className="rounded"
                          />
                          <Label htmlFor="includeText">تضمين النصوص</Label>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="ai" className="space-y-4">
                      {/* اختيار النموذج */}
                      <div className="space-y-2">
                        <Label>نموذج الذكاء الاصطناعي</Label>
                        <Select value={selectedAIModel} onValueChange={setSelectedAIModel} disabled={isLoadingModels}>
                          <SelectTrigger>
                            <SelectValue placeholder={isLoadingModels ? "جاري التحميل..." : "اختر نموذج الذكاء الاصطناعي"} />
                          </SelectTrigger>
                          <SelectContent>
                            {availableModels.map((provider) => (
                              provider.models?.map((model: string) => (
                                <SelectItem key={`${provider.id}-${model}`} value={`${provider.id}-${model}`}>
                                  {provider.providerName} - {model}
                                </SelectItem>
                              ))
                            ))}
                          </SelectContent>
                        </Select>
                        {isLoadingModels && (
                          <p className="text-xs text-muted-foreground">جاري تحميل النماذج المتاحة...</p>
                        )}
                      </div>

                      {/* شريط التقدم */}
                      {isGenerating && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm">جاري التوليد...</Label>
                            <span className="text-sm text-muted-foreground">{Math.round(generationProgress)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${generationProgress}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">نصائح للحصول على أفضل النتائج:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• كن محدداً في وصف نوع الصفحة (هبوط، منتج، شركة، إلخ)</li>
                <li>• اذكر الأقسام المطلوبة (عن الشركة، المنتجات، الاتصال)</li>
                <li>• حدد الألوان أو النمط المفضل إن أردت</li>
                <li>• اذكر الجمهور المستهدف</li>
                <li>• استخدم خيار "تضمين الهيدر" للحفاظ على تصميم الموقع الموحد</li>
              </ul>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAIDialog(false)}>
              إلغاء
            </Button>
            <Button
              onClick={generateWithAI}
              disabled={isGenerating || !aiPrompt.trim() || !pageName.trim()}
              className="relative overflow-hidden"
            >
              {isGenerating ? (
                <>
                  <div className="flex items-center">
                    <Sparkles className="h-4 w-4 mr-2 animate-pulse" />
                    جاري التوليد... {Math.round(generationProgress)}%
                  </div>
                  <div
                    className="absolute bottom-0 left-0 h-1 bg-white/30 transition-all duration-500"
                    style={{ width: `${generationProgress}%` }}
                  />
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  توليد صفحة احترافية
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار اختيار القالب */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="arabic-text">اختيار قالب</DialogTitle>
            <DialogDescription>
              اختر قالباً جاهزاً لبدء مشروعك
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map((template) => (
              <Card 
                key={template.id} 
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => useTemplate(template)}
              >
                <div className="aspect-video bg-muted rounded-t-lg flex items-center justify-center">
                  <Layout className="h-8 w-8 text-muted-foreground" />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold arabic-text">{template.nameAr}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {template.description}
                  </p>
                  <div className="flex items-center justify-between mt-3">
                    <Badge variant="outline">{template.category}</Badge>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <span>{template.usageCount}</span>
                      <span>استخدام</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {templates.length === 0 && (
            <div className="text-center py-8">
              <Layout className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">لا توجد قوالب متاحة حالياً</p>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTemplateDialog(false)}>
              إلغاء
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
