
'use client'

import {
  useState,
  useEffect
} from 'react'
import {
  PageProject,
  PageTemplate,
  AIGenerationRequest
} from '@/types/page-builder'
import {
  MockDataManager
} from '@/lib/mockData'
import {
  But<PERSON>
} from '@/components/ui/button'

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
import {
  toast
} from 'sonner'






import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
