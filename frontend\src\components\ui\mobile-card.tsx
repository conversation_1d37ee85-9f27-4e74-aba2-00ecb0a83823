
"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface MobileCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: "default" | "outlined" | "elevated"
  size?: "sm" | "md" | "lg"
}

interface MobileCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileCardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

// Mobile Card
const MobileCard = React.forwardRef<HTMLDivElement, MobileCardProps>(
  ({ className, children, variant = "default", size = "md", ...props }, ref) => {
    const variants = {
      default: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
      outlined: "bg-transparent border-2 border-gray-300 dark:border-gray-600",
      elevated: "bg-white dark:bg-gray-800 shadow-lg border-0"
    }

    const sizes = {
      sm: "mobile-spacing-sm",
      md: "mobile-spacing",
      lg: "mobile-spacing-lg"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "mobile-card rounded-lg transition-all duration-300",
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileCard.displayName = "MobileCard"

// Mobile Card Header
const MobileCardHeader = React.forwardRef<HTMLDivElement, MobileCardHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex flex-col space-y-1.5 pb-4 border-b border-gray-200 dark:border-gray-700 mb-4",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileCardHeader.displayName = "MobileCardHeader"

// Mobile Card Content
const MobileCardContent = React.forwardRef<HTMLDivElement, MobileCardContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("space-y-3", className)}
      {...props}
    >
      {children}
    </div>
  )
)
MobileCardContent.displayName = "MobileCardContent"

// Mobile Card Footer
const MobileCardFooter = React.forwardRef<HTMLDivElement, MobileCardFooterProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700 mt-4",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileCardFooter.displayName = "MobileCardFooter"

// Mobile Card Title
interface MobileCardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode
  level?: 1 | 2 | 3 | 4 | 5 | 6
}

const MobileCardTitle = React.forwardRef<HTMLHeadingElement, MobileCardTitleProps>(
  ({ className, children, level = 3, ...props }, ref) => {
    const Component = `h${level}` as keyof JSX.IntrinsicElements

    return (
      <Component
        ref={ref as any}
        className={cn(
          "mobile-text-lg font-semibold text-gray-900 dark:text-white arabic-text",
          className
        )}
        {...props}
      >
        {children}
      </Component>
    )
  }
)
MobileCardTitle.displayName = "MobileCardTitle"

// Mobile Card Description
const MobileCardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, children, ...props }, ref) => (
    <p
      ref={ref}
      className={cn(
        "mobile-text-sm text-gray-600 dark:text-gray-400 arabic-text",
        className
      )}
      {...props}
    >
      {children}
    </p>
  )
)
MobileCardDescription.displayName = "MobileCardDescription"

// Mobile Grid Container
interface MobileGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  cols?: 1 | 2 | 3 | 4
  gap?: "sm" | "md" | "lg"
}

const MobileGrid = React.forwardRef<HTMLDivElement, MobileGridProps>(
  ({ className, children, cols = 1, gap = "md", ...props }, ref) => {
    const gapSizes = {
      sm: "gap-2",
      md: "gap-4",
      lg: "gap-6"
    }

    const colClasses = {
      1: "grid-cols-1",
      2: "grid-cols-1 sm:grid-cols-2",
      3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          colClasses[cols],
          gapSizes[gap],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileGrid.displayName = "MobileGrid"

export {
  MobileCard,
}
