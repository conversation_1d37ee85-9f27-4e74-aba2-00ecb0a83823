{"name": "domelementtype", "version": "2.3.0", "description": "all the types of nodes in htmlparser2's dom", "author": "<PERSON> <<EMAIL>>", "license": "BSD-2-<PERSON><PERSON>", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "element", "types", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint src", "format": "prettier --write **/*.{ts,json,md}", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.6.2", "typescript": "^4.6.3"}}