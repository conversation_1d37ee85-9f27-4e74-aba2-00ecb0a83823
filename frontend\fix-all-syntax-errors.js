const fs = require('fs');
const path = require('path');

// Function to find all TypeScript/TSX files
function findTsxFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (file !== 'node_modules' && file !== '.next' && file !== '.git') {
        results = results.concat(findTsxFiles(filePath));
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Function to fix all syntax errors in a file
function fixAllSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. Fix broken import statements
    // Remove duplicate import lines and malformed imports
    const lines = content.split('\n');
    const fixedLines = [];
    let i = 0;
    
    while (i < lines.length) {
      const line = lines[i];
      
      // Check for broken import patterns
      if (line.match(/^import\s*\{\s*$/) || line.match(/^import\s*\{[^}]*$/) && !line.includes('from')) {
        // This is a broken import, try to reconstruct it
        let importContent = line;
        let j = i + 1;
        
        // Collect all lines until we find the 'from' clause or end
        while (j < lines.length) {
          const nextLine = lines[j];
          importContent += '\n' + nextLine;
          
          if (nextLine.includes('from \'') || nextLine.includes('from "')) {
            break;
          }
          j++;
        }
        
        // Try to fix the import
        const fromMatch = importContent.match(/from\s*['"]([^'"]+)['"]/);
        if (fromMatch) {
          const moduleName = fromMatch[1];
          
          // Extract all import names
          const importNames = [];
          const importMatch = importContent.match(/import\s*\{([^}]*)\}/s);
          if (importMatch) {
            const imports = importMatch[1]
              .split(',')
              .map(imp => imp.trim())
              .filter(imp => imp && !imp.includes('from'));
            importNames.push(...imports);
          }
          
          // Also check for standalone import names on separate lines
          const standaloneImports = importContent.match(/^\s*([A-Z][a-zA-Z0-9_]*)\s*,?\s*$/gm);
          if (standaloneImports) {
            standaloneImports.forEach(imp => {
              const cleanImp = imp.replace(/[,\s]/g, '');
              if (cleanImp && !importNames.includes(cleanImp)) {
                importNames.push(cleanImp);
              }
            });
          }
          
          if (importNames.length > 0) {
            const uniqueImports = [...new Set(importNames)];
            const fixedImport = `import {\n  ${uniqueImports.join(',\n  ')}\n} from '${moduleName}'`;
            fixedLines.push(fixedImport);
            modified = true;
          }
        }
        
        i = j + 1;
      } else if (line.match(/^\s*[A-Z][a-zA-Z0-9_,\s]*\s*}\s*from\s*['"][^'"]+['"]/)) {
        // This is a line that starts with import names but missing 'import {'
        const match = line.match(/^\s*([A-Z][a-zA-Z0-9_,\s]*)\s*}\s*from\s*['"]([^'"]+)['"]/);
        if (match) {
          const imports = match[1].split(',').map(imp => imp.trim()).filter(imp => imp);
          const moduleName = match[2];
          const fixedImport = `import {\n  ${imports.join(',\n  ')}\n} from '${moduleName}'`;
          fixedLines.push(fixedImport);
          modified = true;
        }
        i++;
      } else if (line.match(/^\s*}\s*from\s*['"][^'"]+['"]/)) {
        // Skip standalone } from lines (they should be handled above)
        i++;
        modified = true;
      } else if (line.match(/^\s*[A-Z][a-zA-Z0-9_]*\s*,?\s*$/) && 
                 i > 0 && 
                 (fixedLines[fixedLines.length - 1]?.includes('import {') || 
                  fixedLines[fixedLines.length - 1]?.match(/^\s*[A-Z]/))) {
        // Skip standalone import names that should be part of previous import
        i++;
        modified = true;
      } else {
        fixedLines.push(line);
        i++;
      }
    }
    
    if (modified) {
      content = fixedLines.join('\n');
    }
    
    // 2. Fix JSX structure issues
    // Fix missing JSX element names (< className=... should be <div className=...)
    content = content.replace(/< className=/g, '<div className=');
    content = content.replace(/< htmlFor=/g, '<label htmlFor=');
    
    // Fix template literal issues in className
    content = content.replace(/className=\{`([^`]*)\$\{([^}]*)\}([^`]*)`\}/g, 'className={`$1${$2}$3`}');
    
    // 3. Fix common JSX closing tag issues
    // This is more complex and would need specific handling per file
    
    if (content !== fs.readFileSync(filePath, 'utf8')) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed syntax errors in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Starting comprehensive syntax error fixes...');
console.log(`📁 Scanning directory: ${srcDir}`);

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} TypeScript/TSX files`);

let fixedCount = 0;
let totalCount = tsxFiles.length;

tsxFiles.forEach(file => {
  if (fixAllSyntaxErrors(file)) {
    fixedCount++;
  }
});

console.log('\n🎉 Comprehensive Fix Summary:');
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`📄 Total files: ${totalCount}`);
console.log(`⏭️  Files unchanged: ${totalCount - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n🚀 Run "npm run type-check" to verify fixes!');
} else {
  console.log('\n✨ All syntax errors are already fixed!');
}
