
"use client"

import {
  useState
} from 'react'
import {
  useTranslation
} from '@/hooks/useTranslation'
import {
  Navigation
} from '@/components/Navigation'
import {
  GraduationOutfitPreview
} from '@/components/customize/GraduationOutfitPreview'
import {
  Color
} from '@/components/customize/Color '
import {
  DesignActions
} from '@/components/customize/DesignActions'
import {
  Button
} from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

import {
  Content,
  List,
  Trigger,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
} from '@/components/ui/tabs'
import {
  Content,
  Item,
  Trigger,
  Value,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import {
  Switch
} from '@/components/ui/switch'

import {
  Shirt,
  import { Label } from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
  Crown,
  Ribbon,
  RotateCcw,
  ShoppingCart,
  Crown
} from 'lucide-react'

// أنواع البيانات للتخصيص
interface CustomizationState {
  gown: {
    color: string
    style: string
    size: string
    fabric: string
  }
  cap: {
    color: string
    style: string
    tassel: {
      color: string
      style: string
    }
  }
  stole: {
    enabled: boolean
    color: string
    pattern: string
    text: string
    embroidery: boolean
  }
  accessories: {
    hood: boolean
    sash: boolean
    medal: boolean
  }
}

const initialState: CustomizationState = {
  gown: {
    color: 'black',
    style: 'classic',
    size: 'M',
    fabric: 'premium'
  },
  cap: {
    color: 'black',
    style: 'traditional',
    tassel: {
      color: 'gold',
      style: 'classic'
    }
  },
  stole: {
    enabled: false,
    color: 'gold',
    pattern: 'plain',
    text: '',
    embroidery: false
  },
  accessories: {
    hood: false,
    sash: false,
    medal: false
  }
}

// خيارات التخصيص
const customizationOptions = {
  colors: [
    { name: 'أسود', value: 'black', hex: '#000000', category: 'classic' as const, popularity: 5 },
    { name: 'أزرق داكن', value: 'navy', hex: '#1e3a8a', category: 'classic' as const, popularity: 4 },
    { name: 'بورجوندي', value: 'burgundy', hex: '#7c2d12', category: 'premium' as const, popularity: 3 },
    { name: 'أخضر داكن', value: 'forest', hex: '#166534', category: 'modern' as const, popularity: 2 },
    { name: 'بنفسجي', value: 'purple', hex: '#7c3aed', category: 'modern' as const, popularity: 3, isNew: true },
    { name: 'رمادي', value: 'gray', hex: '#4b5563', category: 'classic' as const, popularity: 3 }
  ],
  tasselColors: [
    { name: 'ذهبي', value: 'gold', hex: '#fbbf24', category: 'classic' as const, popularity: 5 },
    { name: 'فضي', value: 'silver', hex: '#e5e7eb', category: 'premium' as const, popularity: 4 },
    { name: 'أسود', value: 'black', hex: '#000000', category: 'classic' as const, popularity: 4 },
    { name: 'أبيض', value: 'white', hex: '#ffffff', category: 'classic' as const, popularity: 3 },
    { name: 'أزرق', value: 'blue', hex: '#3b82f6', category: 'modern' as const, popularity: 2 },
    { name: 'أحمر', value: 'red', hex: '#ef4444', category: 'modern' as const, popularity: 2, isNew: true }
  ],
  gownStyles: [
    { name: 'كلاسيكي', value: 'classic', description: 'التصميم التقليدي الأنيق' },
    { name: 'عصري', value: 'modern', description: 'تصميم معاصر مع لمسات حديثة' },
    { name: 'فاخر', value: 'luxury', description: 'تصميم راقي مع تفاصيل مميزة' }
  ],
  fabrics: [
    { name: 'قياسي', value: 'standard', price: 0 },
    { name: 'مميز', value: 'premium', price: 50 },
    { name: 'فاخر', value: 'luxury', price: 100 }
  ],
  sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL']
}

export default function CustomizePage() {
  const { } = useTranslation() // ✅ إصلاح: إزالة متغير غير مستخدم
  const [customization, setCustomization] = useState<CustomizationState>(initialState)
  const [activeTab, setActiveTab] = useState('gown')
  const [totalPrice, setTotalPrice] = useState(299.99)

  const updateCustomization = (category: keyof CustomizationState, updates: unknown) => {
    setCustomization(prev => ({
      ...prev,
      [category]: { ...prev[category], ...updates }
    }))
  }

  const resetCustomization = () => {
    setCustomization(initialState)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            🎨 تخصيص زي التخرج
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 arabic-text">
            صمم زي التخرج المثالي الذي يعكس شخصيتك
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Preview Section */}
          <div className="lg:col-span-1 space-y-6">
            <div className="sticky top-24">
              <GraduationOutfitPreview
                configuration={customization}
                className="mb-6"
              />

              {/* Price Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">ملخص السعر</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className="arabic-text">الثوب الأساسي:</span>
                      <span>299 درهم</span>
                    </div>
                    {customization.stole.enabled && (
                      <div className="flex justify-between">
                        <span className="arabic-text">الوشاح:</span>
                        <span>50 درهم</span>
                      </div>
                    )}
                    {customization.accessories.hood && (
                      <div className="flex justify-between">
                        <span className="arabic-text">غطاء الرأس:</span>
                        <span>30 درهم</span>
                      </div>
                    )}
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold arabic-text">الإجمالي:</span>
                      <span className="text-2xl font-bold text-blue-600">{totalPrice} درهم</span>
                    </div>
                  </div>

                  <Button className="w-full mt-4 arabic-text">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    إضافة للسلة
                  </Button>
                </CardContent>
              </Card>

              {/* Design Actions */}
              <DesignActions
                designData={customization}
                designName="تصميم زي التخرج المخصص"
                on ={(name, description) => {}}
                onShare={(platform) => {}}
              />
            </div>
          </div>

          {/* Customization Options */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="arabic-text">خيارات التخصيص</CardTitle>
                  <Button variant="outline" size="sm" onClick={resetCustomization}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    إعادة تعيين
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Select value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="gown" className="arabic-text">
                      <Shirt className="h-4 w-4 mr-2" />
                      الثوب
                    </TabsTrigger>
                    <SelectTrigger value="cap" className="arabic-text">
                      <Crown className="h-4 w-4 mr-2" />
                      القبعة
                    </TabsTrigger>
                    <TabsTrigger value="stole" className="arabic-text">
                      <Ribbon className="h-4 w-4 mr-2" />
                      الوشاح
                    </TabsTrigger>
                    <TabsTrigger value="accessories" className="arabic-text">
                      <div className="h-4 w-4 mr-2" />
                      الإكسسوارات
                    </TabsTrigger>
                  </TabsList>

                  {/* Gown Customization */}
                  <TabsContent value="gown" className="space-y-6 mt-6">
                    <Color title="لون الثوب"
                      colors={customizationOptions.colors}
                      selectedColor={customization.gown.color}
                      onColorChange={(color) => updateCustomization('gown', { color })}
                      showCategories={true}
                      showSearch={true}
                      allowCustom={true}
                    />

                    <div className="space-y-3">
                      <div className="arabic-text">نمط الثوب</div>
                      <div className="grid gap-3">
                        {customizationOptions.gownStyles.map((style) => (
                          <button
                            key={style.value}
                            onClick={() => updateCustomization('gown', { style: style.value })}
                            className={`p-4 rounded-lg border-2 text-left transition-colors ${
                              customization.gown.style === style.value
                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                            }`}
                          >
                            <div className="font-medium arabic-text">{style.name}</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                              {style.description}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="arabic-text">المقاس</div>
                      <Select value={customization.gown.size} 
                        onValueChange={(size) => updateCustomization('gown', { size })}
                      >
                        <TabsTrigger>
                          <SelectValue />
                        </TabsTrigger>
                        <SelectContent>
                          {customizationOptions.sizes.map((size) => (
                            <SelectItem key={size} value={size}>
                              {size}
                            </SelectItem>
                          ))}
                        </TabsContent>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="arabic-text">نوع القماش</div>
                      <div className="grid gap-3">
                        {customizationOptions.fabrics.map((fabric) => (
                          <button
                            key={fabric.value}
                            onClick={() => updateCustomization('gown', { fabric: fabric.value })}
                            className={`p-4 rounded-lg border-2 flex justify-between items-center transition-colors ${
                              customization.gown.fabric === fabric.value
                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                            }`}
                          >
                            <span className="font-medium arabic-text">{fabric.name}</span>
                            {fabric.price > 0 && (
                              <Badge variant="secondary">+{fabric.price} درهم</div>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  {/* Cap Customization */}
                  <TabsContent value="cap" className="space-y-6 mt-6">
                    <Color title="لون القبعة"
                      colors={customizationOptions.colors}
                      selectedColor={customization.cap.color}
                      onColorChange={(color) => updateCustomization('cap', { color })}
                      showCategories={true}
                    />

                    <Color title="لون الشرابة"
                      colors={customizationOptions.tasselColors}
                      selectedColor={customization.cap.tassel.color}
                      onColorChange={(color) => updateCustomization('cap', {
                        tassel: { ...customization.cap.tassel, color }
                      })}
                      showCategories={false}
                    />
                  </TabsContent>

                  {/* Stole Customization */}
                  <TabsContent value="stole" className="space-y-6 mt-6">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="stole-enabled"
                        checked={customization.stole.enabled}
                        onCheckedChange={(enabled) => updateCustomization('stole', { enabled })}
                      />
                      <Label htmlFor="stole-enabled" className="arabic-text">
                        إضافة وشاح التخرج
                      </Label>
                    </div>

                    {customization.stole.enabled && (
                      <>
                        <Color title="لون الوشاح"
                          colors={customizationOptions.tasselColors}
                          selectedColor={customization.stole.color}
                          onColorChange={(color) => updateCustomization('stole', { color })}
                          showCategories={false}
                        />

                        <div className="flex items-center space-x-2">
                          <Switch
                            id="stole-embroidery"
                            checked={customization.stole.embroidery}
                            onCheckedChange={(embroidery) => updateCustomization('stole', { embroidery })}
                          />
                          <Label htmlFor="stole-embroidery" className="arabic-text">
                            تطريز مخصص (+50 درهم)
                          </Label>
                        </div>
                      </>
                    )}
                  </TabsContent>

                  {/* Accessories */}
                  <TabsContent value="accessories" className="space-y-6 mt-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="arabic-text">غطاء الرأس الأكاديمي</div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            للدرجات العليا (+30 درهم)
                          </p>
                        </div>
                        <Switch
                          checked={customization.accessories.hood}
                          onCheckedChange={(hood) => updateCustomization('accessories', { hood })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="arabic-text">حزام الشرف</div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            للمتفوقين (+25 درهم)
                          </p>
                        </div>
                        <Switch
                          checked={customization.accessories.sash}
                          onCheckedChange={(sash) => updateCustomization('accessories', { sash })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="arabic-text">ميدالية التخرج</div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            تذكار مميز (+40 درهم)
                          </p>
                        </div>
                        <Switch
                          checked={customization.accessories.medal}
                          onCheckedChange={(medal) => updateCustomization('accessories', { medal })}
                        />
                      </div>
                    </div>
                  </TabsContent>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
