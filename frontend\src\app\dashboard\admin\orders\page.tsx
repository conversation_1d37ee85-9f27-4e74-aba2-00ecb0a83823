
"use client"

import {
  useState,
  useEffect
} from 'react'
import {
  useAuth
} from '@/contexts/AuthContext'
import {
  Navigation
} from '@/components/Navigation'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Button
} from '@/components/ui/button'

import {
  Content,
  Item,
  Trigger,
  Value,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  useToast
} from '@/components/ui/toast'
import {
  OrderStatus PaymentStatus
} from '@/components/admin/OrderStatus '
import {
  OrderDetails
} from '@/components/admin/OrderDetails '
import Link from 'next/link'
import {
  Search,
  MoreHorizontal,
  Trash2,
  import { Label } from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
} from '@/components/ui/tabs'
  ArrowLeft,
  RefreshCw,
  Clock,
  Truck,
  ArrowLeft
} from 'lucide-react'

// أنواع البيانات
interface Order {
  id: string
  order_number: string
  customer_name: string
  customer_email: string
  customer_phone?: string
  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method?: string
  items: unknown[]
  subtotal: number
  tax: number
  shipping_cost: number
  total: number
  shipping_address: {
    street: string
    city: string
    state: string
    postal_code: string
    country: string
  }
  tracking_number?: string
  notes?: string
  created_at: string
  updated_at: string
  delivery_date?: string
  school_name?: string
}

interface OrderStats {
  total: number
  pending: number
  confirmed: number
  in_production: number
  shipped: number
  delivered: number
  cancelled: number
  total_revenue: number
  pending_payments: number
}

export default function OrdersManagement() {
  const { } = useAuth() // ✅ إصلاح: إزالة متغيرات غير مستخدمة
  const toast = useToast()
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, set edOrders] = useState<Order[]>([])
  const [stats, setStats] = useState<OrderStats | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [status setStatus ] = useState<string>('all')
  const [payment setPayment ] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [loading, setLoading] = useState(true)
  const [selectedOrder, set edOrder] = useState<Order | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)

  // التحقق من صلاحيات الأدمن
  useEffect(() => {
    if (!user || !profile || profile.role !== 'admin') {
      window.location.href = '/dashboard'
      return
    }
  }, [user, profile])

  // جلب الطلبات
  const fetchOrders = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/orders')
      const data = await response.json()
      
      if (response.ok) {
        setOrders(data.orders || [])
        setStats(data.stats || null)
      } else {
        toast.error(data.error || 'فشل في جلب الطلبات')
      }
    } catch (_error) {toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrders()
  }, [])

  // دالة تحديث الطلب
  const handleUpdateOrder = async (orderId: string, updates: unknown) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json' },
        body: JSON.stringify(updates) })

      const data = await response.json()

      if (response.ok) {
        // تحديث قائمة الطلبات
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId ? { ...order, ...updates } : order
          )
        )
        toast.success('تم تحديث الطلب بنجاح')
      } else {
        toast.error(data.error || 'فشل في تحديث الطلب')
      }
    } catch (_error) {toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // دالة حذف الطلب
  const handleDeleteOrder = async (orderId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
      return
    }

    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'DELETE' })

      const data = await response.json()

      if (response.ok) {
        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId))
        toast.success('تم حذف الطلب بنجاح')
      } else {
        toast.error(data.error || 'فشل في حذف الطلب')
      }
    } catch (_error) {toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // دالة عرض تفاصيل الطلب
  const handleViewOrder = (order: Order) => {
    set edOrder(order)
    setShowOrderDetails(true)
  }

  // تطبيق الفلاتر والبحث
  useEffect(() => {
    let filtered = [...orders]

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer_email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلترة حسب الحالة
    if (status !== 'all') {
      filtered = filtered.filter(order => order.status === status )
    }

    // فلترة حسب حالة الدفع
    if (payment !== 'all') {
      filtered = filtered.filter(order => order.payment_status === payment )
    }

    // ترتيب النتائج
    filtered.sort((a, b) => {
      let aValue: unknown = a[sortBy as keyof Order]
      let bValue: unknown = b[sortBy as keyof Order]

      if (sortBy === 'created_at') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1
      } else {
        return aValue > bValue ? 1 : -1
      }
    })

    set edOrders(filtered)
  }, [orders, searchTerm, status payment sortBy, sortOrder])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </Link>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة الطلبات 📦
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة ومتابعة جميع طلبات العملاء
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={fetchOrders}>
                <RefreshCw className="h-4 w-4 mr-2" />
                تحديث
              </Button>
              <Button variant="outline">
                <div className="h-4 w-4 mr-2" />
                تصدير
              </Button>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                      إجمالي الطلبات
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stats.total}
                    </p>
                  </div>
                  <div className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                      الطلبات المعلقة
                    </p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {stats.pending}
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                      الطلبات المكتملة
                    </p>
                    <p className="text-2xl font-bold text-green-600">
                      {stats.delivered}
                    </p>
                  </div>
                  <div className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                      إجمالي المبيعات
                    </p>
                    <p className="text-2xl font-bold text-blue-600">
                      {stats.total_revenue.toFixed(2)} Dhs
                    </p>
                  </div>
                  <div className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* s and Search */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="arabic-text">البحث والفلترة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 arabic-text"
                  />
                </div>
              </div>

              <Select value={status } onValueChange={setStatus }>
                <SelectTrigger className="arabic-text">
                  <SelectValue placeholder="حالة الطلب" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">في الانتظار</SelectItem>
                  <SelectItem value="confirmed">مؤكد</SelectItem>
                  <SelectItem value="in_production">قيد الإنتاج</SelectItem>
                  <SelectItem value="shipped">تم الشحن</SelectItem>
                  <SelectItem value="delivered">تم التسليم</SelectItem>
                  <SelectItem value="cancelled">ملغي</SelectItem>
                </TabsContent>
              </div>

              <Select value={payment } onValueChange={setPayment }>
                <TabsTrigger className="arabic-text">
                  <SelectValue placeholder="حالة الدفع" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع حالات الدفع</SelectItem>
                  <SelectItem value="pending">في الانتظار</SelectItem>
                  <SelectItem value="paid">مدفوع</SelectItem>
                  <SelectItem value="failed">فشل</SelectItem>
                  <SelectItem value="refunded">مسترد</SelectItem>
                </TabsContent>
              </div>

              <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
                const [field, order] = value.split('-')
                setSortBy(field)
                setSortOrder(order as 'asc' | 'desc')
              }}>
                <TabsTrigger className="arabic-text">
                  <SelectValue placeholder="ترتيب حسب" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="created_at-desc">الأحدث أولاً</SelectItem>
                  <SelectItem value="created_at-asc">الأقدم أولاً</SelectItem>
                  <SelectItem value="total-desc">المبلغ (الأعلى أولاً)</SelectItem>
                  <SelectItem value="total-asc">المبلغ (الأقل أولاً)</SelectItem>
                  <SelectItem value="customer_name-asc">اسم العميل (أ-ي)</SelectItem>
                  <SelectItem value="customer_name-desc">اسم العميل (ي-أ)</SelectItem>
                </TabsContent>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="arabic-text">قائمة الطلبات</CardTitle>
              <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                {filteredOrders.length} من {orders.length} طلب
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      رقم الطلب
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      العميل
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      المدرسة
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      حالة الطلب
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      حالة الدفع
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      المبلغ
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      التاريخ
                    </th>
                    <th className="text-right p-4 font-medium text-gray-900 dark:text-white arabic-text">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => (
                    <tr key={order.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="p-4">
                        <div className="font-medium text-blue-600 arabic-text">
                          {order.order_number}
                        </div>
                        <div className="text-sm text-gray-500 arabic-text">
                          {order.items.length} عنصر
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="font-medium text-gray-900 dark:text-white arabic-text">
                          {order.customer_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.customer_email}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900 dark:text-white arabic-text">
                          {order.school_name || 'غير محدد'}
                        </div>
                      </td>
                      <td className="p-4">
                        <OrderStatus status={order.status} />
                      </td>
                      <td className="p-4">
                        <PaymentStatus status={order.payment_status} />
                      </td>
                      <td className="p-4">
                        <div className="font-medium text-gray-900 dark:text-white">
                          {order.total.toFixed(2)} Dhs
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {new Date(order.created_at).toLocaleDateString('ar-SA')}
                        </div>
                      </td>
                      <td className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewOrder(order)}>
                              <div className="h-4 w-4 mr-2" />
                              عرض التفاصيل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleViewOrder(order)}>
                              <div className="h-4 w-4 mr-2" />
                              تحديث الحالة
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Truck className="h-4 w-4 mr-2" />
                              تتبع الشحنة
                            </DropdownMenuItem>
                            <DropdownMenu />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeleteOrder(order.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              حذف
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredOrders.length === 0 && (
                <div className="text-center py-8">
                  <div className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 arabic-text">لا توجد طلبات</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Details */}
        <OrderDetails order={selectedOrder}
          isOpen={showOrderDetails}
          onClose={() => {
            setShowOrderDetails(false)
            set edOrder(null)
          }}
          onUpdate={handleUpdateOrder}
        />

      </main>
    </div>
  )
}
