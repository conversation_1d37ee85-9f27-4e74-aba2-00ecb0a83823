
"use client"

import {
  useState,
  useEffect
} from 'react'
import {
  useAuth
} from '@/contexts/AuthContext'
import {
  Navigation
} from '@/components/Navigation'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  But<PERSON>
} from '@/components/ui/button'

import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'



import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  Eye,
  Star,
  Clock,
  RefreshCw,
  ShoppingCart,
  Eye
} from 'lucide-react'

// أنواع البيانات
interface AnalyticsData {
  overview: {
    totalRevenue: number
    totalOrders: number
    totalCustomers: number
    averageOrderValue: number
    conversionRate: number
    returnRate: number
  }
  trends: {
    revenue: { period: string; value: number; change: number }[]
    orders: { period: string; value: number; change: number }[]
    customers: { period: string; value: number; change: number }[]
  }
  products: {
    id: string
    name: string
    sales: number
    revenue: number
    views: number
    rating: number
    stock: number
  }[]
  demographics: {
    ageGroups: { range: string; percentage: number }[]
    locations: { city: string; percentage: number }[]
    devices: { type: string; percentage: number }[]
  }
  performance: {
    pageViews: number
    uniqueVisitors: number
    bounceRate: number
    avgSessionDuration: number
    topPages: { page: string; views: number }[]
  }
}

export default function AnalyticsPage() {
  const { user, profile } = useAuth()
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [timeRange, setTimeRange] = useState('30d')
  const [loading, setLoading] = useState(true)

  // تحميل البيانات الوهمية
  useEffect(() => {
    const mockData: AnalyticsData = {
      overview: {
        totalRevenue: 125430,
        totalOrders: 342,
        totalCustomers: 1247,
        averageOrderValue: 367,
        conversionRate: 3.2,
        returnRate: 2.1
      },
      trends: {
        revenue: [
          { period: 'يناير', value: 45000, change: 12.5 },
          { period: 'فبراير', value: 52000, change: 15.6 },
          { period: 'مارس', value: 48000, change: -7.7 },
          { period: 'أبريل', value: 58000, change: 20.8 }
        ],
        orders: [
          { period: 'يناير', value: 120, change: 8.3 },
          { period: 'فبراير', value: 135, change: 12.5 },
          { period: 'مارس', value: 128, change: -5.2 },
          { period: 'أبريل', value: 156, change: 21.9 }
        ],
        customers: [
          { period: 'يناير', value: 89, change: 15.2 },
          { period: 'فبراير', value: 102, change: 14.6 },
          { period: 'مارس', value: 95, change: -6.9 },
          { period: 'أبريل', value: 118, change: 24.2 }
        ]
      },
      products: [
        {
          id: '1',
          name: 'زي التخرج الكلاسيكي',
          sales: 156,
          revenue: 46800,
          views: 2340,
          rating: 4.5,
          stock: 23
        },
        {
          id: '2',
          name: 'قبعة التخرج المميزة',
          sales: 89,
          revenue: 8010,
          views: 1560,
          rating: 4.2,
          stock: 45
        },
        {
          id: '3',
          name: 'وشاح التخرج الفاخر',
          sales: 67,
          revenue: 10050,
          views: 890,
          rating: 4.7,
          stock: 12
        }
      ],
      demographics: {
        ageGroups: [
          { range: '18-24', percentage: 45 },
          { range: '25-34', percentage: 32 },
          { range: '35-44', percentage: 18 },
          { range: '45+', percentage: 5 }
        ],
        locations: [
          { city: 'دبي', percentage: 35 },
          { city: 'أبوظبي', percentage: 28 },
          { city: 'الشارقة', percentage: 22 },
          { city: 'عجمان', percentage: 15 }
        ],
        devices: [
          { type: 'الهاتف المحمول', percentage: 68 },
          { type: 'سطح المكتب', percentage: 25 },
          { type: 'الجهاز اللوحي', percentage: 7 }
        ]
      },
      performance: {
        pageViews: 15420,
        uniqueVisitors: 8930,
        bounceRate: 32.5,
        avgSessionDuration: 245,
        topPages: [
          { page: '/catalog', views: 4520 },
          { page: '/', views: 3890 },
          { page: '/customize', views: 2340 },
          { page: '/dashboard', views: 1890 }
        ]
      }
    }

    setTimeout(() => {
      setData(mockData)
      setLoading(false)
    }, 1000)
  }, [timeRange])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                التحليلات والتقارير 📊
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                تحليل شامل لأداء المنصة والمبيعات
              </p>
            </div>
            <div className="flex gap-3">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border rounded-md arabic-text"
              >
                <option value="7d">آخر 7 أيام</option>
                <option value="30d">آخر 30 يوم</option>
                <option value="90d">آخر 3 أشهر</option>
                <option value="1y">آخر سنة</option>
              </select>
              <Button variant="outline" className="arabic-text">
                <Download className="h-4 w-4 mr-2" />
                تصدير التقرير
              </Button>
              <Button className="arabic-text">
                <RefreshCw className="h-4 w-4 mr-2" />
                تحديث البيانات
              </Button>
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي الإيرادات</p>
                  <p className="text-xl font-bold">{formatCurrency(data.overview.totalRevenue)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي الطلبات</p>
                  <p className="text-xl font-bold">{data.overview.totalOrders}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">إجمالي العملاء</p>
                  <p className="text-xl font-bold">{data.overview.totalCustomers}</p>
                </div>
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط قيمة الطلب</p>
                  <p className="text-xl font-bold">{formatCurrency(data.overview.averageOrderValue)}</p>
                </div>
                <Target className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">معدل التحويل</p>
                  <p className="text-xl font-bold">{formatPercentage(data.overview.conversionRate)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-cyan-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">معدل الإرجاع</p>
                  <p className="text-xl font-bold">{formatPercentage(data.overview.returnRate)}</p>
                </div>
                <TrendingDown className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="trends" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="trends" className="arabic-text">
              <TrendingUp className="h-4 w-4 mr-2" />
              الاتجاهات
            </TabsTrigger>
            <TabsTrigger value="products" className="arabic-text">
              <Package className="h-4 w-4 mr-2" />
              المنتجات
            </TabsTrigger>
            <TabsTrigger value="demographics" className="arabic-text">
              <Users className="h-4 w-4 mr-2" />
              الديموغرافيا
            </TabsTrigger>
            <TabsTrigger value="performance" className="arabic-text">
              <BarChart3 className="h-4 w-4 mr-2" />
              الأداء
            </TabsTrigger>
          </TabsList>

          {/* Trends */}
          <TabsContent value="trends" className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              {/* Revenue Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">اتجاه الإيرادات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.trends.revenue.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm arabic-text">{item.period}</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{formatCurrency(item.value)}</span>
                          <div className={`flex items-center gap-1 ${
                            item.change >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.change >= 0 ? (
                              <TrendingUp className="h-3 w-3" />
                            ) : (
                              <TrendingDown className="h-3 w-3" />
                            )}
                            <span className="text-xs">{formatPercentage(Math.abs(item.change))}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Orders Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">اتجاه الطلبات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.trends.orders.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm arabic-text">{item.period}</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{item.value}</span>
                          <div className={`flex items-center gap-1 ${
                            item.change >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.change >= 0 ? (
                              <TrendingUp className="h-3 w-3" />
                            ) : (
                              <TrendingDown className="h-3 w-3" />
                            )}
                            <span className="text-xs">{formatPercentage(Math.abs(item.change))}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Customers Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">اتجاه العملاء الجدد</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.trends.customers.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm arabic-text">{item.period}</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{item.value}</span>
                          <div className={`flex items-center gap-1 ${
                            item.change >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.change >= 0 ? (
                              <TrendingUp className="h-3 w-3" />
                            ) : (
                              <TrendingDown className="h-3 w-3" />
                            )}
                            <span className="text-xs">{formatPercentage(Math.abs(item.change))}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Products */}
          <TabsContent value="products" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">أداء المنتجات</CardTitle>
                <CardDescription className="arabic-text">
                  تحليل مفصل لأداء المنتجات الأكثر مبيعاً
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.products.map((product) => (
                    <div key={product.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium arabic-text">{product.name}</h3>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm">{product.rating}</span>
                          </div>
                          <Badge variant={product.stock > 20 ? "default" : product.stock > 5 ? "secondary" : "destructive"}>
                            {product.stock} متوفر
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600 dark:text-gray-400 arabic-text">المبيعات</p>
                          <p className="font-medium">{product.sales}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400 arabic-text">الإيرادات</p>
                          <p className="font-medium">{formatCurrency(product.revenue)}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400 arabic-text">المشاهدات</p>
                          <p className="font-medium">{product.views}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400 arabic-text">معدل التحويل</p>
                          <p className="font-medium">{formatPercentage((product.sales / product.views) * 100)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Demographics */}
          <TabsContent value="demographics" className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              {/* Age Groups */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الفئات العمرية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.demographics.ageGroups.map((group, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm arabic-text">{group.range} سنة</span>
                          <span className="text-sm font-medium">{formatPercentage(group.percentage)}</span>
                        </div>
                        <Progress value={group.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Locations */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">المواقع الجغرافية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.demographics.locations.map((location, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm arabic-text">{location.city}</span>
                          <span className="text-sm font-medium">{formatPercentage(location.percentage)}</span>
                        </div>
                        <Progress value={location.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Devices */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الأجهزة المستخدمة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.demographics.devices.map((device, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm arabic-text">{device.type}</span>
                          <span className="text-sm font-medium">{formatPercentage(device.percentage)}</span>
                        </div>
                        <Progress value={device.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Performance */}
          <TabsContent value="performance" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Website Performance */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">أداء الموقع</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Eye className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{data.performance.pageViews.toLocaleString()}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">مشاهدات الصفحة</p>
                    </div>
                    
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{data.performance.uniqueVisitors.toLocaleString()}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">زوار فريدون</p>
                    </div>
                    
                    <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                      <MousePointer className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{formatPercentage(data.performance.bounceRate)}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">معدل الارتداد</p>
                    </div>
                    
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <Clock className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{formatDuration(data.performance.avgSessionDuration)}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط الجلسة</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Top Pages */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الصفحات الأكثر زيارة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.performance.topPages.map((page, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                          </div>
                          <span className="font-medium">{page.page}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{page.views.toLocaleString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
