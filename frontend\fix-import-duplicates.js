const fs = require('fs');
const path = require('path');

// Function to find all TypeScript/TSX files
function findTsxFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (file !== 'node_modules' && file !== '.next' && file !== '.git') {
        results = results.concat(findTsxFiles(filePath));
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Function to fix duplicate import statements
function fixDuplicateImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix pattern: "import {\n  import {" 
    if (content.includes('import {\n  import {')) {
      content = content.replace(/import\s*\{\s*\n\s*import\s*\{/g, 'import {');
      modified = true;
    }
    
    // Fix pattern: "import {\nimport {" 
    if (content.includes('import {\nimport {')) {
      content = content.replace(/import\s*\{\s*\nimport\s*\{/g, 'import {');
      modified = true;
    }
    
    // Fix pattern where there are multiple "import {" on consecutive lines
    content = content.replace(/import\s*\{\s*\n\s*import\s*\{/g, 'import {');
    
    // Remove duplicate imports from same module
    const lines = content.split('\n');
    const processedLines = [];
    const importMap = new Map();
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Check if this is an import line
      const importMatch = line.match(/^import\s*\{\s*([^}]*)\s*\}\s*from\s*['"]([^'"]+)['"]/);
      if (importMatch) {
        const imports = importMatch[1];
        const module = importMatch[2];
        
        if (importMap.has(module)) {
          // Merge with existing import
          const existingImports = importMap.get(module);
          const newImports = imports.split(',').map(imp => imp.trim()).filter(imp => imp);
          const allImports = [...existingImports, ...newImports];
          const uniqueImports = [...new Set(allImports)];
          importMap.set(module, uniqueImports);
          modified = true;
        } else {
          const importList = imports.split(',').map(imp => imp.trim()).filter(imp => imp);
          importMap.set(module, importList);
          processedLines.push(line);
        }
      } else {
        processedLines.push(line);
      }
    }
    
    // Rebuild content with merged imports
    if (modified) {
      let newContent = processedLines.join('\n');
      
      // Replace the original imports with merged ones
      for (const [module, imports] of importMap) {
        if (imports.length > 0) {
          const uniqueImports = [...new Set(imports)];
          const importStatement = `import {\n  ${uniqueImports.join(',\n  ')}\n} from '${module}'`;
          
          // Find and replace the first occurrence of this module's import
          const regex = new RegExp(`import\\s*\\{[^}]*\\}\\s*from\\s*['"]${module.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
          let replaced = false;
          newContent = newContent.replace(regex, (match) => {
            if (!replaced) {
              replaced = true;
              return importStatement;
            }
            return ''; // Remove duplicate
          });
        }
      }
      
      content = newContent;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed duplicate imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Starting duplicate import fixes...');

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} files`);

let fixedCount = 0;

tsxFiles.forEach(file => {
  if (fixDuplicateImports(file)) {
    fixedCount++;
  }
});

console.log(`\n✅ Fixed ${fixedCount} files`);
console.log('🚀 Run build to test fixes');
