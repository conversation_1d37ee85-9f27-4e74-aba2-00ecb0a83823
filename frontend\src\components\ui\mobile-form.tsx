
"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'





interface MobileFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
}

interface MobileFormGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileFormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  children: React.ReactNode
  required?: boolean
}

interface MobileFormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
}

interface MobileFormTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
}

interface MobileFormSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  children: React.ReactNode
  error?: string
}

interface MobileFormErrorProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

// Mobile Form
const MobileForm = React.forwardRef<HTMLFormElement, MobileFormProps>(
  ({ className, children, ...props }, ref) => (
    <form
      ref={ref}
      className={cn("mobile-form space-y-6", className)}
      {...props}
    >
      {children}
    </form>
  )
)
MobileForm.displayName = "MobileForm"

// Mobile Form Group
const MobileFormGroup = React.forwardRef<HTMLDivElement, MobileFormGroupProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("mobile-form-group", className)}
      {...props}
    >
      {children}
    </div>
  )
)
MobileFormGroup.displayName = "MobileFormGroup"

// Mobile Form Label
const MobileFormLabel = React.forwardRef<HTMLLabelElement, MobileFormLabelProps>(
  ({ className, children, required, ...props }, ref) => (
    <label
      ref={ref}
      className={cn("mobile-form-label", className)}
      {...props}
    >
      {children}
      {required && <span className="text-red-500 mr-1">*</span>}
    </label>
  )
)
MobileFormLabel.displayName = "MobileFormLabel"

// Mobile Form Input
const MobileFormInput = React.forwardRef<HTMLInputElement, MobileFormInputProps>(
  ({ className, error, ...props }, ref) => (
    <input
      ref={ref}
      className={cn(
        "mobile-form-input",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
        className
      )}
      {...props}
    />
  )
)
MobileFormInput.displayName = "MobileFormInput"

// Mobile Form Textarea
const MobileFormTextarea = React.forwardRef<HTMLTextAreaElement, MobileFormTextareaProps>(
  ({ className, error, ...props }, ref) => (
    <textarea
      ref={ref}
      className={cn(
        "mobile-form-input min-h-[120px] resize-y",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
        className
      )}
      {...props}
    />
  )
)
MobileFormTextarea.displayName = "MobileFormTextarea"

// Mobile Form Select
const MobileFormSelect = React.forwardRef<HTMLSelectElement, MobileFormSelectProps>(
  ({ className, children, error, ...props }, ref) => (
    <select
      ref={ref}
      className={cn(
        "mobile-form-input appearance-none bg-white dark:bg-gray-800",
        "bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzZCNzI4MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-no-repeat bg-right-3 bg-center pr-10",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
        className
      )}
      {...props}
    >
      {children}
    </select>
  )
)
MobileFormSelect.displayName = "MobileFormSelect"

// Mobile Form Error
const MobileFormError = React.forwardRef<HTMLDivElement, MobileFormErrorProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "text-sm text-red-600 dark:text-red-400 mt-1",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileFormError.displayName = "MobileFormError"

// Mobile Form Button
interface MobileFormButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: "primary" | "secondary" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
  loading?: boolean
}

const MobileFormButton = React.forwardRef<HTMLButtonElement, MobileFormButtonProps>(
  ({ className, children, variant = "primary", size = "md", loading, disabled, ...props }, ref) => {
    const variants = {
      primary: "mobile-btn-primary",
      secondary: "mobile-btn-secondary",
      outline: "border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400",
      ghost: "bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800"
    }

    const sizes = {
      sm: "px-4 py-2 text-sm",
      md: "px-6 py-3 text-base",
      lg: "px-8 py-4 text-lg"
    }

    return (
      <button
        ref={ref}
        className={cn(
          "mobile-btn w-full sm:w-auto",
          variants[variant],
          sizes[size],
          (disabled || loading) && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="mobile-spinner w-4 h-4 mr-2 border-2 border-current border-t-transparent"></div>
        )}
        {children}
      </button>
    )
  }
)
MobileFormButton.displayName = "MobileFormButton"

// Mobile Form Row (for side-by-side fields)
interface MobileFormRowProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  cols?: 1 | 2 | 3
}

const MobileFormRow = React.forwardRef<HTMLDivElement, MobileFormRowProps>(
  ({ className, children, cols = 2, ...props }, ref) => {
    const colClasses = {
      1: "grid-cols-1",
      2: "grid-cols-1 sm:grid-cols-2",
      3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid gap-4",
          colClasses[cols],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileFormRow.displayName = "MobileFormRow"

export {
  MobileForm,
}
