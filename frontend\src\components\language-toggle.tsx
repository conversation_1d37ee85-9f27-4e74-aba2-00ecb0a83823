
"use client"

import * as React from "react"

import { useTranslation } from "@/hooks/useTranslation"
import { Locale, localeNames, localeFlags } from "@/lib/i18n"

import { Button } from "@/components/ui/button"
import {
  Globe,
  ChevronDown
} from 'lucide-react'

export function LanguageToggle() {
  const { locale, changeLocale } = useTranslation()

  const getCurrentLanguage = () => {
    return {
      flag: localeFlags[locale],
      name: localeNames[locale],
      code: locale.toUpperCase()
    }
  }

  const currentLang = getCurrentLanguage ()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group"
        >
          <div className="flex items-center gap-2">
            <span className="text-lg group-hover:scale-110 transition-transform duration-300">
              {currentLang.flag}
            </span>
            <span className="hidden sm:inline-block text-sm font-medium">
              {currentLang.code}
            </span>
          </div>
          <div className="h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300" />
          <span className="sr-only">تغيير اللغة / Change language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-48 p-2"
        sideOffset={8}
      >
        <DropdownMenu className="text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1">
          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}
        </DropdownMenu >
        <DropdownMenu />
        {Object.entries(localeNames).map(([code, name]) => (
          <DropdownMenuItem
            key={code}
            onClick={() => changeLocale(code as Locale)}
            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${
              locale === code
                ? "bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300"
                : "hover:bg-gray-100 dark:hover:bg-gray-700"
            }`}
          >
            <span className="text-lg">{localeFlags[code as Locale]}</span>
            <div className="flex-1">
              <div className="font-medium text-sm">{name}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}
              </div>
            </div>
            {locale === code && (
              <div className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
