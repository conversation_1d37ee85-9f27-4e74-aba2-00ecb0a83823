
"use client"

import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { MobileCard, MobileCardContent, MobileGrid } from '@/components/ui/mobile-card'
import { Truck, ShoppingCart } from 'lucide-react'
  Users,
  School,
  ShoppingCart,
  DollarSign,
  TrendingUp,
  Truck
} from 'lucide-react'

interface AdminStats {
  total_users: number
  total_schools: number
  total_orders: number
  total_revenue: number
  monthly_growth: number
  active_deliveries: number
}

interface StatCard {
  title: string
  value: string | number
  icon: React.ReactNode
  color: string
  href?: string
  clickable: boolean
  description?: string
}

interface AdminStatsCardsProps {
  stats: AdminStats
}

export function AdminStatsCards({ stats }: AdminStatsCardsProps) {
  const statCards: StatCard[] = [
    {
      title: 'إجمالي المستخدمين',
      value: stats.total_users.toLocaleString(),
      icon: <Users className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />,
      color: 'blue',
      href: '/dashboard/admin/users',
      clickable: true,
      description: 'انقر للإدارة'
    },
    {
      title: 'المدارس المسجلة',
      value: stats.total_schools,
      icon: <School className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />,
      color: 'green',
      clickable: false,
      description: 'انقر للإدارة'
    },
    {
      title: 'إجمالي الطلبات',
      value: stats.total_orders.toLocaleString(),
      icon: <ShoppingCart className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />,
      color: 'purple',
      href: '/dashboard/admin/products',
      clickable: true,
      description: 'انقر لإدارة المنتجات'
    },
    {
      title: 'إجمالي الإيرادات',
      value: `${(stats.total_revenue / 1000000).toFixed(1)}M Dhs`,
      icon: <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />,
      color: 'yellow',
      clickable: false
    },
    {
      title: 'النمو الشهري',
      value: `+${stats.monthly_growth}%`,
      icon: <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />,
      color: 'green',
      clickable: false
    },
    {
      title: 'عمليات التوصيل',
      value: stats.active_deliveries,
      icon: <Truck className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600" />,
      color: 'orange',
      clickable: false,
      description: 'انقر للإدارة'
    }
  ]

  return (
    <MobileGrid cols={3} gap="md" className="mb-6 sm:mb-8">
      {statCards.map((card, index) => {
        const CardComponent = (
          <MobileCard
            key={index}
            variant="elevated"
            size="sm"
            className={`${
              card.clickable
                ? 'hover:shadow-md transition-shadow cursor-pointer touch-target'
                : card.description
                  ? 'hover:shadow-md transition-shadow cursor-pointer opacity-75 touch-target'
                  : ''
            }`}
          >
            <MobileCardContent>
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="mobile-text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    {card.title}
                  </p>
                  <p className="mobile-text-lg sm:text-2xl font-bold text-gray-900 dark:text-white">
                    {card.value}
                  </p>
                  {card.description && (
                    <p className={`mobile-text-sm mt-1 ${
                      card.clickable ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {card.description}
                    </p>
                  )}
                </div>
                <div className="ml-2 sm:ml-4">
                  {card.icon}
                </div>
              </div>
            </MobileCardContent>
          </MobileCard>
        )

        return card.clickable && card.href ? (
          <Link key={index} href={card.href}>
            {CardComponent}
          </Link>
        ) : (
          CardComponent
        )
      })}
    </MobileGrid>
  )
}
