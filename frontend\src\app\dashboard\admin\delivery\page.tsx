
'use client'

import { use<PERSON>tate, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import {  Content, Item, Trigger, Value } from '@/components/ui/select'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {  Content, Description, Header, Title, Trigger } from '@/components/ui/dialog'
import {  Content, List, Trigger } from '@/components/ui/tabs'
import {
  import {
import {
  Clock,
  Truck,
  Clock
} from 'lucide-react'
  s
import Link from 'next/link'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsContent } from '@/components/ui/tabs'





// أنواع البيانات
interface DeliveryCompany {
  id: string
  name: string
  contact_person: string
  phone: string
  email: string
  address: string
  city: string
  coverage_areas: string[]
  base_rate: number
  per_km_rate: number
  rating: number
  total_deliveries: number
  active_drivers: number
  is_active: boolean
  created_at: string
}

interface DeliveryDriver {
  id: string
  company_id: string
  company_name: string
  name: string
  phone: string
  email: string
  license_number: string
  vehicle_type: string
  vehicle_plate: string
  rating: number
  total_deliveries: number
  current_status: 'available' | 'busy' | 'offline'
  current_location?: {
    lat: number
    lng: number
    address: string
  }
  is_active: boolean
  created_at: string
}

interface DeliveryOrder {
  id: string
  order_number: string
  driver_id: string
  driver_name: string
  company_name: string
  customer_name: string
  pickup_address: string
  delivery_address: string
  distance: number
  estimated_time: number
  delivery_fee: number
  status: 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'
  created_at: string
  delivered_at?: string
}

// بيانات وهمية
const mockDeliveryCompanies: DeliveryCompany[] = [
  {
    id: '1',
    name: 'شركة التوصيل السريع',
    contact_person: 'أحمد محمد',
    phone: '+212-6-12345678',
    email: '<EMAIL>',
    address: 'شارع محمد الخامس، الدار البيضاء',
    city: 'الدار البيضاء',
    coverage_areas: ['الدار البيضاء', 'الرباط', 'سلا'],
    base_rate: 25,
    per_km_rate: 3,
    rating: 4.8,
    total_deliveries: 1250,
    active_drivers: 15,
    is_active: true,
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'خدمات التوصيل المغربية',
    contact_person: 'فاطمة الزهراء',
    phone: '+212-6-87654321',
    email: '<EMAIL>',
    address: 'شارع الحسن الثاني، فاس',
    city: 'فاس',
    coverage_areas: ['فاس', 'مكناس', 'إفران'],
    base_rate: 20,
    per_km_rate: 2.5,
    rating: 4.6,
    total_deliveries: 890,
    active_drivers: 12,
    is_active: true,
    created_at: '2024-01-20T14:30:00Z'
  }
]

const mockDeliveryDrivers: DeliveryDriver[] = [
  {
    id: '1',
    company_id: '1',
    company_name: 'شركة التوصيل السريع',
    name: 'يوسف العلوي',
    phone: '+212-6-11111111',
    email: '<EMAIL>',
    license_number: 'DL123456',
    vehicle_type: 'دراجة نارية',
    vehicle_plate: 'A-12345-20',
    rating: 4.9,
    total_deliveries: 156,
    current_status: 'available',
    current_location: {
      lat: 33.5731,
      lng: -7.5898,
      address: 'وسط مدينة الدار البيضاء'
    },
    is_active: true,
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    company_id: '1',
    company_name: 'شركة التوصيل السريع',
    name: 'عبد الرحمن بنعلي',
    phone: '+212-6-22222222',
    email: '<EMAIL>',
    license_number: 'DL789012',
    vehicle_type: 'سيارة صغيرة',
    vehicle_plate: 'B-67890-20',
    rating: 4.7,
    total_deliveries: 203,
    current_status: 'busy',
    is_active: true,
    created_at: '2024-01-18T09:15:00Z'
  }
]

const mockDeliveryOrders: DeliveryOrder[] = [
  {
    id: '1',
    order_number: 'GT-**********',
    driver_id: '1',
    driver_name: 'يوسف العلوي',
    company_name: 'شركة التوصيل السريع',
    customer_name: 'خالد المنصوري',
    pickup_address: 'مستودع أزياء التخرج، الدار البيضاء',
    delivery_address: 'جامعة زايد، دبي الأكاديمي',
    distance: 12.5,
    estimated_time: 45,
    delivery_fee: 62.5,
    status: 'in_transit',
    created_at: '2024-01-22T10:30:00Z'
  },
  {
    id: '2',
    order_number: 'GT-**********',
    driver_id: '2',
    driver_name: 'عبد الرحمن بنعلي',
    company_name: 'شركة التوصيل السريع',
    customer_name: 'فاطمة الزهراني',
    pickup_address: 'مستودع أزياء التخرج، الدار البيضاء',
    delivery_address: 'الجامعة الأمريكية في الشارقة',
    distance: 8.3,
    estimated_time: 30,
    delivery_fee: 45.9,
    status: 'delivered',
    created_at: '2024-01-21T14:15:00Z',
    delivered_at: '2024-01-21T15:00:00Z'
  }
]

export default function DeliveryManagementPage() {
  const [activeTab, setActiveTab] = useState('companies')
  const [companies, setCompanies] = useState<DeliveryCompany[]>(mockDeliveryCompanies)
  const [drivers, setDrivers] = useState<DeliveryDriver[]>(mockDeliveryDrivers)
  const [orders, setOrders] = useState<DeliveryOrder[]>(mockDeliveryOrders)
  const [searchTerm, setSearchTerm] = useState('')
  const [status setStatus ] = useState('all')
  const [isLoading, setIsLoading] = useState(false)

  // إحصائيات التوصيل
  const deliveryStats = {
    total_companies: companies.length,
    active_companies: companies.filter(c => c.is_active).length,
    total_drivers: drivers.length,
    active_drivers: drivers.filter(d => d.current_status === 'available').length,
    total_orders: orders.length,
    delivered_orders: orders.filter(o => o.status === 'delivered').length,
    in_transit_orders: orders.filter(o => o.status === 'in_transit').length,
    total_revenue: orders.reduce((sum, order) => sum + order.delivery_fee, 0)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'busy': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'offline': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'assigned': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'picked_up': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'in_transit': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      case 'delivered': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return 'متاح'
      case 'busy': return 'مشغول'
      case 'offline': return 'غير متصل'
      case 'assigned': return 'مُعيّن'
      case 'picked_up': return 'تم الاستلام'
      case 'in_transit': return 'في الطريق'
      case 'delivered': return 'تم التسليم'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/dashboard/admin" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة للوحة التحكم
          </Link>
          
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
                إدارة التوصيل 🚚
              </h1>
              <p className="text-gray-600 dark:text-gray-400 arabic-text">
                إدارة شركات التوصيل والموصلين والطلبات
              </p>
            </div>
            
            <div className="flex gap-3 mt-4 md:mt-0">
              <Button onClick={() => setIsLoading(true)} variant="outline">
                <div className="h-4 w-4 mr-2" />
                تحديث
              </Button>
              <Button>
                <div className="h-4 w-4 mr-2" />
                إضافة شركة توصيل
              </Button>
            </div>
          </div>
        </div>

        {/* إحصائيات التوصيل */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    شركات التوصيل
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {deliveryStats.active_companies}/{deliveryStats.total_companies}
                  </p>
                </div>
                <Truck className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    الموصلين المتاحين
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {deliveryStats.active_drivers}/{deliveryStats.total_drivers}
                  </p>
                </div>
                < s className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    طلبات في الطريق
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {deliveryStats.in_transit_orders}
                  </p>
                </div>
                <Route className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إيرادات التوصيل
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {deliveryStats.total_revenue.toFixed(2)} Dhs
                  </p>
                </div>
                <div className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* التبويبات الرئيسية */}
        <Select value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="companies" className="arabic-text">
              <Truck className="h-4 w-4 mr-2" />
              شركات التوصيل
            </TabsTrigger>
            <SelectTrigger value="zones" className="arabic-text">
              <div className="h-4 w-4 mr-2" />
              المناطق والشحن
            </TabsTrigger>
            <TabsTrigger value="drivers" className="arabic-text">
              < s className="h-4 w-4 mr-2" />
              الموصلين
            </TabsTrigger>
            <TabsTrigger value="orders" className="arabic-text">
              <div className="h-4 w-4 mr-2" />
              طلبات التوصيل
            </TabsTrigger>
          </TabsList>

          {/* تبويب شركات التوصيل */}
          <TabsContent value="companies" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">شركات التوصيل</CardTitle>
              </CardHeader>
              <CardContent>
                {/* البحث والفلترة */}
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input placeholder="البحث في شركات التوصيل..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 arabic-text"
                      />
                    </div>
                  </div>
                  <Select value={status } onValueChange={setStatus }>
                    <TabsTrigger className="w-48">
                      <SelectValue placeholder="جميع الحالات" />
                    </TabsTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="active">نشط</SelectItem>
                      <SelectItem value="inactive">غير نشط</SelectItem>
                    </TabsContent>
                  </div>
                </div>

                {/* جدول شركات التوصيل */}
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="arabic-text">اسم الشركة</TableHead>
                        <TableHead className="arabic-text">الشخص المسؤول</TableHead>
                        <TableHead className="arabic-text">المدينة</TableHead>
                        <TableHead className="arabic-text">الموصلين النشطين</TableHead>
                        <TableHead className="arabic-text">التقييم</TableHead>
                        <TableHead className="arabic-text">الحالة</TableHead>
                        <TableHead className="arabic-text">الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {companies.map((company) => (
                        <TableRow key={company.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium arabic-text">{company.name}</div>
                              <div className="text-sm text-gray-500">{company.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium arabic-text">{company.contact_person}</div>
                              <div className="text-sm text-gray-500">{company.phone}</div>
                            </div>
                          </TableCell>
                          <TableCell className="arabic-text">{company.city}</TableCell>
                          <TableCell>
                            <div className="text-center">
                              <span className="font-bold text-green-600">{company.active_drivers}</span>
                              <span className="text-gray-500">/{company.total_deliveries}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <div className="h-4 w-4 text-yellow-500 fill-current" />
                              <span>{company.rating}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className={company.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                              {company.is_active ? 'نشط' : 'غير نشط'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب المناطق والشحن */}
          <TabsContent value="zones" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-6">
              {/* إدارة المناطق */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <div className="h-5 w-5" />
                    مناطق التوصيل
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {[
                      { name: 'بني ملال', cost: 50, time: '24 ساعة', active: true },
                      { name: 'خنيفرة', cost: 60, time: '48 ساعة', active: true },
                      { name: 'الفقيه بن صالح', cost: 55, time: '24 ساعة', active: true },
                      { name: 'أزيلال', cost: 70, time: '48 ساعة', active: false }
                    ].map((zone, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium arabic-text">{zone.name}</span>
                            < variant={zone.active ? "default" : "secondary"}>
                              {zone.active ? 'مفعل' : 'معطل'}
                            </div>
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            التكلفة: {zone.cost} Dhs • الوقت: {zone.time}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <div className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <div className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full">
                    <div className="h-4 w-4 mr-2" />
                    إضافة منطقة جديدة
                  </Button>
                </CardContent>
              </Card>

              {/* إعدادات الشحن */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <div className="h-5 w-5" />
                    إعدادات الشحن
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="default_shipping" className="arabic-text">تكلفة الشحن الافتراضية (Dhs)</Label>
                      <Input id="default_shipping" type="number" defaultValue="50" />
                    </div>
                    <div>
                      <Label htmlFor="free_shipping_threshold" className="arabic-text">الحد الأدنى للشحن المجاني (Dhs)</Label>
                      <Input id="free_shipping_threshold" type="number" defaultValue="500" />
                    </div>
                    <div>
                      <Label htmlFor="express_shipping" className="arabic-text">تكلفة الشحن السريع (Dhs)</Label>
                      <Input id="express_shipping" type="number" defaultValue="100" />
                    </div>
                    <div>
                      <Label htmlFor="max_weight" className="arabic-text">الوزن الأقصى للطرد (كيلو)</Label>
                      <Input id="max_weight" type="number" defaultValue="10" />
                    </div>
                    <div>
                      <Label htmlFor="processing_time" className="arabic-text">وقت المعالجة (ساعات)</Label>
                      <Input id="processing_time" type="number" defaultValue="24" />
                    </div>
                  </div>
                  <Button className="w-full">
                    <div className="h-4 w-4 mr-2" />
                    حفظ الإعدادات
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* إحصائيات الشحن */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إحصائيات الشحن</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">156</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">طلبات قيد التوصيل</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">892</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">طلبات مكتملة</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-yellow-600">24</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط وقت التوصيل (ساعة)</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-purple-600">52.5</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">متوسط تكلفة الشحن (Dhs)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب الموصلين */}
          <TabsContent value="drivers" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الموصلين</CardTitle>
              </CardHeader>
              <CardContent>
                {/* البحث والفلترة */}
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input placeholder="البحث في الموصلين..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 arabic-text"
                      />
                    </div>
                  </div>
                  <Select value={status } onValueChange={setStatus }>
                    <TabsTrigger className="w-48">
                      <SelectValue placeholder="جميع الحالات" />
                    </TabsTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="available">متاح</SelectItem>
                      <SelectItem value="busy">مشغول</SelectItem>
                      <SelectItem value="offline">غير متصل</SelectItem>
                    </TabsContent>
                  </div>
                </div>

                {/* جدول الموصلين */}
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="arabic-text">الموصل</TableHead>
                        <TableHead className="arabic-text">الشركة</TableHead>
                        <TableHead className="arabic-text">نوع المركبة</TableHead>
                        <TableHead className="arabic-text">التقييم</TableHead>
                        <TableHead className="arabic-text">عدد التوصيلات</TableHead>
                        <TableHead className="arabic-text">الحالة</TableHead>
                        <TableHead className="arabic-text">الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {drivers.map((driver) => (
                        <TableRow key={driver.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium arabic-text">{driver.name}</div>
                              <div className="text-sm text-gray-500">{driver.phone}</div>
                            </div>
                          </TableCell>
                          <TableCell className="arabic-text">{driver.company_name}</TableCell>
                          <TableCell>
                            <div>
                              <div className="arabic-text">{driver.vehicle_type}</div>
                              <div className="text-sm text-gray-500">{driver.vehicle_plate}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <div className="h-4 w-4 text-yellow-500 fill-current" />
                              <span>{driver.rating}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-center">{driver.total_deliveries}</TableCell>
                          <TableCell>
                            <div className={getStatusColor(driver.current_status)}>
                              {getStatusText(driver.current_status)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب طلبات التوصيل */}
          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">طلبات التوصيل</CardTitle>
              </CardHeader>
              <CardContent>
                {/* البحث والفلترة */}
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input placeholder="البحث في طلبات التوصيل..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 arabic-text"
                      />
                    </div>
                  </div>
                  <Select value={status } onValueChange={setStatus }>
                    < Trigger className="w-48">
                      <SelectValue placeholder="جميع الحالات" />
                    </TabsTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="assigned">مُعيّن</SelectItem>
                      <SelectItem value="picked_up">تم الاستلام</SelectItem>
                      <SelectItem value="in_transit">في الطريق</SelectItem>
                      <SelectItem value="delivered">تم التسليم</SelectItem>
                      <SelectItem value="cancelled">ملغي</SelectItem>
                    </TabsContent>
                  </div>
                </div>

                {/* جدول طلبات التوصيل */}
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="arabic-text">رقم الطلب</TableHead>
                        <TableHead className="arabic-text">العميل</TableHead>
                        <TableHead className="arabic-text">الموصل</TableHead>
                        <TableHead className="arabic-text">المسافة</TableHead>
                        <TableHead className="arabic-text">رسوم التوصيل</TableHead>
                        <TableHead className="arabic-text">الحالة</TableHead>
                        <TableHead className="arabic-text">الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orders.map((order) => (
                        <TableRow key={order.id}>
                          <TableCell>
                            <div className="font-medium">{order.order_number}</div>
                          </TableCell>
                          <TableCell className="arabic-text">{order.customer_name}</TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium arabic-text">{order.driver_name}</div>
                              <div className="text-sm text-gray-500 arabic-text">{order.company_name}</div>
                            </div>
                          </TableCell>
                          <TableCell>{order.distance} كم</TableCell>
                          <TableCell>{order.delivery_fee} Dhs</TableCell>
                          <TableCell>
                            <div className={getStatusColor(order.status)}>
                              {getStatusText(order.status)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <div className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </div>
    </div>
  )
}
