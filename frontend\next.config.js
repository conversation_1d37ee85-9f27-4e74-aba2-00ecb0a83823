/** @type {import('next').NextConfig} */
const nextConfig = {
  // App Router is enabled by default in Next.js 13.4+
  // No need for experimental.appDir anymore

  // ✅ إصلاح حرج: إعادة تفعيل فحوصات البناء
  eslint: {
    ignoreDuringBuilds: false, // ✅ تفعيل ESLint أثناء البناء
  },
  typescript: {
    ignoreBuildErrors: false,  // ✅ تفعيل فحص أخطاء TypeScript
  },

  // ✅ إضافة Security Headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
          }
        ]
      }
    ];
  },

  // ✅ تحسينات الأداء
  experimental: {
    optimizeCss: true,
    // ✅ Turbopack configuration
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
      resolveAlias: {
        '@': './src',
      },
    },
  },

  // ✅ إزالة console.log في الإنتاج
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // ✅ تحسين Bundle - Only for non-Turbopack builds
  ...(process.env.NODE_ENV !== 'development' && {
    webpack: (config, { isServer }) => {
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
        };
      }

      // Bundle analyzer للتطوير
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
          })
        );
      }

      return config;
    },
  }),
}

module.exports = nextConfig
