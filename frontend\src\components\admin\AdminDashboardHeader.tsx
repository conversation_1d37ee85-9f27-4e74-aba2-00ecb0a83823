
"use client"

import {
  AdminQuickNav
} from './AdminQuickNav'


import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  Bell,
  Crown
} from 'lucide-react'

interface AdminDashboardHeaderProps {
  alertsCount?: number
}

export function AdminDashboardHeader({ alertsCount = 0 }: AdminDashboardHeaderProps) {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2">
            <Crown className="h-8 w-8 text-yellow-500" />
            لوحة تحكم الإدارة
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            إدارة شاملة للمنصة والمستخدمين والطلبات
          </p>
          {alertsCount > 0 && (
            <div className="flex items-center gap-2 mt-2">
              <Bell className="h-4 w-4 text-amber-500" />
              <Badge variant="destructive" className="text-xs">
                {alertsCount} تنبيه جديد
              </Badge>
            </div>
          )}
        </div>
        
        <AdminQuickNav />
      </div>
    </div>
  )
}
