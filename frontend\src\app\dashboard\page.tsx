
"use client"

import { useEffect } from 'react'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { Card, CardContent } from '@/components/ui/card'
import {
  Loader2
} from 'lucide-react'

function DashboardContent() {
  const { profile } = useAuth()

  useEffect(() => {
    if (profile) {
      // إعادة توجيه المستخدمين إلى لوحات التحكم المخصصة لهم
      switch (profile.role) {
        case UserRole.ADMIN:
          window.location.href = '/dashboard/admin'
          break
        case UserRole.SCHOOL:
          window.location.href = '/dashboard/school'
          break
        case UserRole.DELIVERY:
          window.location.href = '/dashboard/delivery'
          break
        case UserRole.STUDENT:
          window.location.href = '/dashboard/student'
          break
        default:
          window.location.href = '/dashboard/student'
          break
      }
    }
  }, [profile])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
          <p className="text-gray-600 dark:text-gray-300 text-center">
            جاري إعادة التوجيه إلى لوحة التحكم المناسبة...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}
