(()=>{var e={};e.id=9077,e.ids=[9077],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47134:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>b});var t={};r.r(t),r.d(t,{DELETE:()=>x,GET:()=>c,POST:()=>p,PUT:()=>d});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(38561);async function c(){try{return i.NextResponse.json({success:!0,providers:[],message:"تم جلب المزودين بنجاح",useLocalStorage:!0})}catch(e){return i.NextResponse.json({success:!1,error:"فشل في جلب المزودين",providers:[]},{status:500})}}async function p(e){try{let{provider:s,providerName:r,baseUrl:t,apiKey:n,models:o,description:a,status:c="active"}=await e.json();if(!s||!r||!t||!n||!o||0===o.length)return i.NextResponse.json({success:!1,error:"جميع البيانات مطلوبة (المزود، الاسم، الرابط، مفتاح API، النماذج)"},{status:400});let p={id:u.C.generateId(),provider:s,providerName:r,baseUrl:t,apiKey:n,models:o,description:a||"",status:c,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},d=u.C.addAIProvider(p);return i.NextResponse.json({success:!0,provider:d,message:`تم إضافة مزود ${r} بنجاح`})}catch(e){return i.NextResponse.json({success:!1,error:"فشل في إضافة المزود"},{status:500})}}async function d(e){try{let{id:s,provider:r,providerName:t,baseUrl:n,apiKey:o,models:a,description:c,status:p}=await e.json();if(!s)return i.NextResponse.json({success:!1,error:"معرف المزود مطلوب"},{status:400});let d={id:s,provider:r,providerName:t,baseUrl:n,apiKey:o,models:a,description:c||"",status:p,updatedAt:new Date().toISOString()},x=u.C.updateAIProvider(s,d);if(!x)return i.NextResponse.json({success:!1,error:"المزود غير موجود"},{status:404});return i.NextResponse.json({success:!0,provider:x,message:`تم تحديث مزود ${t} بنجاح`})}catch(e){return i.NextResponse.json({success:!1,error:"فشل في تحديث المزود"},{status:500})}}async function x(e){try{let{searchParams:s}=new URL(e.url),r=s.get("id");if(!r)return i.NextResponse.json({success:!1,error:"معرف المزود مطلوب"},{status:400});if(!u.C.deleteAIProvider(r))return i.NextResponse.json({success:!1,error:"المزود غير موجود"},{status:404});return i.NextResponse.json({success:!0,message:"تم حذف المزود بنجاح"})}catch(e){return i.NextResponse.json({success:!1,error:"فشل في حذف المزود"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/ai-providers/route",pathname:"/api/ai-providers",filename:"route",bundlePath:"app/api/ai-providers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-providers\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:b,serverHooks:g}=m;function v(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:b})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,580],()=>r(47134));module.exports=t})();