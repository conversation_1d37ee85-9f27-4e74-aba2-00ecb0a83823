
"use client"

import {
  useState,
  useEffect
} from 'react'
import {
  useAuth
} from '@/contexts/AuthContext'
import {
  Navigation
} from '@/components/Navigation'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Button
} from '@/components/ui/button'

import {
  Separator
} from '@/components/ui/separator'

import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
import {
  AlertCircle,
  Upload,
  Plus,
  Save,
  Eye,
  Star,
  Bell
} from 'lucide-react'
  Star,
  Clock,
  Truck,
  ArrowRight,
  Star
} from 'lucide-react'

// أنواع البيانات
interface OrderDetails {
  id: string
  orderNumber: string
  status: 'confirmed' | 'processing' | 'shipped' | 'delivered'
  createdAt: string
  estimatedDelivery: string
  items: Array<{
    id: string
    name: string
    quantity: number
    price: number
    image: string
  }>
  shipping: {
    method: string
    address: string
    cost: number
  }
  payment: {
    method: string
    amount: number
    status: 'paid' | 'pending' | 'failed'
  }
  customer: {
    name: string
    email: string
    phone: string
  }
}

export default function OrderConfirmationPage() {
  const { user, profile } = useAuth()
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)

  // بيانات وهمية للطلب
  useEffect(() => {
    const mockOrder: OrderDetails = {
      id: 'ORD-2024-001',
      orderNumber: 'GT-240120-001',
      status: 'confirmed',
      createdAt: new Date().toISOString(),
      estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      items: [
        {
          id: '1',
          name: 'زي التخرج الكلاسيكي',
          quantity: 1,
          price: 299.99,
          image: '/api/placeholder/100/100'
        },
        {
          id: '2',
          name: 'قبعة التخرج المميزة',
          quantity: 1,
          price: 89.99,
          image: '/api/placeholder/100/100'
        }
      ],
      shipping: {
        method: 'التوصيل العادي (3-5 أيام)',
        address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة',
        cost: 25
      },
      payment: {
        method: 'بطاقة ائتمان',
        amount: 435.73,
        status: 'paid'
      },
      customer: {
        name: profile?.full_name || 'العميل',
        email: profile?.email || '<EMAIL>',
        phone: profile?.phone || '+971501234567'
      }
    }

    setTimeout(() => {
      setOrderDetails(mockOrder)
      setLoading(false)
    }, 1000)
  }, [profile])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'processing': return 'bg-blue-100 text-blue-800'
      case 'shipped': return 'bg-purple-100 text-purple-800'
      case 'delivered': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed': return 'مؤكد'
      case 'processing': return 'قيد المعالجة'
      case 'shipped': return 'تم الشحن'
      case 'delivered': return 'تم التسليم'
      default: return 'غير معروف'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!orderDetails) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <Package className="h-24 w-24 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
              لم يتم العثور على الطلب
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 arabic-text">
              تأكد من رقم الطلب أو تواصل مع خدمة العملاء
            </p>
            <Button asChild>
              <a href="/dashboard/student" className="arabic-text">
                العودة للوحة التحكم
              </a>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            تم تأكيد طلبك بنجاح! 🎉
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            شكراً لك على ثقتك بنا. سنقوم بمعالجة طلبك في أقرب وقت ممكن
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="arabic-text">تفاصيل الطلب</CardTitle>
                    <CardDescription className="arabic-text">
                      رقم الطلب: {orderDetails.orderNumber}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(orderDetails.status)}>
                    {getStatusText(orderDetails.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">تاريخ الطلب</p>
                      <p className="font-medium">
                        {new Date(orderDetails.createdAt).toLocaleDateString('en-US')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Truck className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">التوصيل المتوقع</p>
                      <p className="font-medium">
                        {new Date(orderDetails.estimatedDelivery).toLocaleDateString('en-US')}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="space-y-4">
                  <h3 className="font-medium arabic-text">المنتجات المطلوبة</h3>
                  {orderDetails.items.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex-shrink-0">
                        <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"></div>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium arabic-text">{item.name}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                          الكمية: {item.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{item.price} درهم</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Shipping Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 arabic-text">
                  <MapPin className="h-5 w-5" />
                  معلومات الشحن
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">طريقة التوصيل</p>
                    <p className="font-medium arabic-text">{orderDetails.shipping.method}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">عنوان التوصيل</p>
                    <p className="font-medium arabic-text">{orderDetails.shipping.address}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">تكلفة الشحن</p>
                    <p className="font-medium">{orderDetails.shipping.cost} درهم</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 arabic-text">
                  <CreditCard className="h-5 w-5" />
                  معلومات الدفع
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400 arabic-text">طريقة الدفع:</span>
                    <span className="font-medium arabic-text">{orderDetails.payment.method}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400 arabic-text">حالة الدفع:</span>
                    <Badge className={orderDetails.payment.status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                      {orderDetails.payment.status === 'paid' ? 'مدفوع' : 'في الانتظار'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400 arabic-text">المبلغ المدفوع:</span>
                    <span className="font-bold text-lg">{orderDetails.payment.amount} درهم</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Order Tracking */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">تتبع الطلب</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium arabic-text">تم تأكيد الطلب</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {new Date(orderDetails.createdAt).toLocaleString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                      <Clock className="h-4 w-4 text-gray-400" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-400 arabic-text">قيد المعالجة</p>
                      <p className="text-sm text-gray-400 arabic-text">في الانتظار</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                      <Truck className="h-4 w-4 text-gray-400" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-400 arabic-text">تم الشحن</p>
                      <p className="text-sm text-gray-400 arabic-text">في الانتظار</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                      <Package className="h-4 w-4 text-gray-400" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-400 arabic-text">تم التسليم</p>
                      <p className="text-sm text-gray-400 arabic-text">في الانتظار</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customer Support */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">تحتاج مساعدة؟</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium arabic-text">اتصل بنا</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">+971 4 123 4567</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium arabic-text">راسلنا</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></p>
                  </div>
                </div>

                <Button variant="outline" className="w-full arabic-text">
                  مركز المساعدة
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full arabic-text">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الفاتورة
                </Button>
                
                <Button variant="outline" className="w-full arabic-text">
                  <Share2 className="h-4 w-4 mr-2" />
                  مشاركة الطلب
                </Button>
                
                <Button className="w-full arabic-text" asChild>
                  <a href="/dashboard/student">
                    لوحة التحكم
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Next Steps */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="arabic-text">الخطوات التالية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Mail className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-medium mb-2 arabic-text">تأكيد بالبريد الإلكتروني</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  ستصلك رسالة تأكيد على {orderDetails.customer.email}
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Package className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-medium mb-2 arabic-text">تحضير الطلب</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  سنبدأ في تحضير طلبك خلال 24 ساعة
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Truck className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-medium mb-2 arabic-text">التوصيل</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  سيصل طلبك خلال {orderDetails.shipping.method}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
