
"use client"

import {
  useState,
  useEffect,
  useRef
} from 'react'
import {
  useTranslation
} from '@/hooks/useTranslation'
import {
  useProducts,
  type Product
} from '@/hooks/useProducts'
import {
  Card,
  CardContent
} from '@/components/ui/card'
import {
  Button
} from '@/components/ui/button'


import {
  Label
} from '@/components/ui/label'
import {
  Input
} from '@/components/ui/input'
import {
  Textarea
} from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Badge
} from '@/components/ui/badge'
import {
  Progress
} from '@/components/ui/progress'
  ShoppingCart,
  ArrowRight,
  ShoppingCart
} from 'lucide-react'

interface ProductCardProps {
  product: Product
  delay: number
}

function ProductCard({ product, delay }: ProductCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 overflow-hidden">
        <div className="relative">
          {/* Product Image */}
          <div className="aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
              <div className="text-6xl">🎓</div>
            </div>
            
            {/* s */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {product.isNew && (
                <div className="bg-green-500 text-white">جديد</div>
              )}
              {product.isFeatured && (
                <div className="bg-yellow-500 text-white">
                  <div className="w-3 h-3 mr-1" />
                  مميز
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="secondary"
                className="w-10 h-10 p-0 rounded-full bg-white/90 hover:bg-white"
                onClick={() => setIsLiked(!isLiked)}
              >
                <div className={`w-4 h-4 ${isLiked ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="w-10 h-10 p-0 rounded-full bg-white/90 hover:bg-white"
              >
                <div className="w-4 h-4 text-gray-600" />
              </Button>
            </div>

            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button className="bg-white text-gray-900 hover:bg-gray-100">
                <ShoppingCart className="w-4 h-4 mr-2" />
                أضف للسلة
              </Button>
            </div>
          </div>

          {/* Product */}
          <CardContent className="p-4">
            <div className="mb-2">
              <Badge variant="outline" className="text-xs">
                {product.category}
              </div>
            </div>
            
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2 arabic-text line-clamp-2">
              {product.name}
            </h3>
            
            {/* Rating */}
            <div className="flex items-center gap-1 mb-3">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  < key={i}
                    className={`w-4 h-4 ${
                      i < product.rating
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-500">({product.reviews})</span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-gray-900 dark:text-white">
                {product.price}
              </span>
              {product.originalPrice && (
                <span className="text-sm text-gray-500 line-through">
                  {product.originalPrice}
                </span>
              )}
            </div>
          </CardContent>
        </div>
      </Card>
    </div>
  )
}

export function ProductsPreview() {
  const { } = useTranslation() // ✅ إصلاح: إزالة متغير غير مستخدم
  const { featuredProducts: products, loading } = useProducts()

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <ShoppingCart className="w-4 h-4" />
            منتجاتنا المميزة
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            {t('home.products.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text mb-8">
            {t('home.products.subtitle')}
          </p>
          
          {/* Category */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Button variant="default" className="arabic-text">
              {t('home.products.featured')}
            </Button>
            <Button variant="outline" className="arabic-text">
              {t('home.products.newArrivals')}
            </Button>
            <Button variant="outline" className="arabic-text">
              {t('home.products.popular')}
            </Button>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {products.map((product, index) => (
            <ProductCard
              key={product.id}
              product={product}
              delay={index * 200}
            />
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button 
            size="lg" 
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 arabic-text group"
            asChild
          >
            <a href="/catalog" className="flex items-center gap-2">
              {t('home.products.viewAll')}
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
        </div>
      </div>
    </section>
  )
}
