
"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'


interface MobileTableProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileTableHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileTableBodyProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileTableRowProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface MobileTableCellProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  label?: string
}

// Mobile Table Container
const MobileTable = React.forwardRef<HTMLDivElement, MobileTableProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("mobile-table-container", className)}
      {...props}
    >
      <div className="mobile-table">
        {children}
      </div>
    </div>
  )
)
MobileTable.displayName = "MobileTable"

// Mobile Table Header
const MobileTableHeader = React.forwardRef<HTMLDivElement, MobileTableHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "hidden sm:grid grid-cols-auto gap-4 p-4 bg-gray-50 dark:bg-gray-800 font-semibold text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileTableHeader.displayName = "MobileTableHeader"

// Mobile Table Body
const MobileTableBody = React.forwardRef<HTMLDivElement, MobileTableBodyProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("divide-y divide-gray-200 dark:divide-gray-700", className)}
      {...props}
    >
      {children}
    </div>
  )
)
MobileTableBody.displayName = "MobileTableBody"

// Mobile Table Row
const MobileTableRow = React.forwardRef<HTMLDivElement, MobileTableRowProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "sm:grid sm:grid-cols-auto sm:gap-4 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",
        "flex flex-col gap-2 sm:gap-0", // Mobile: stack vertically, Desktop: grid
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileTableRow.displayName = "MobileTableRow"

// Mobile Table Cell
const MobileTableCell = React.forwardRef<HTMLDivElement, MobileTableCellProps>(
  ({ className, children, label, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "text-sm text-gray-900 dark:text-gray-100",
        "sm:block", // Desktop: normal display
        "flex justify-between items-center", // Mobile: flex with label
        className
      )}
      {...props}
    >
      {/* Mobile Label */}
      {label && (
        <span className="font-medium text-gray-600 dark:text-gray-400 sm:hidden">
          {label}:
        </span>
      )}
      
      {/* Content */}
      <div className="text-right sm:text-right">
        {children}
      </div>
    </div>
  )
)
MobileTableCell.displayName = "MobileTableCell"

// Responsive Table Component
interface ResponsiveTableProps {
  headers: string[]
  data: Array<Record<string, React.ReactNode>>
  className?: string
}

const ResponsiveTable = React.forwardRef<HTMLDivElement, ResponsiveTableProps>(
  ({ headers, data, className }, ref) => (
    <MobileTable ref={ref} className={className}>
      {/* Desktop Header */}
      <MobileTableHeader>
        {headers.map((header, index) => (
          <div key={index} className="text-right">
            {header}
          </div>
        ))}
      </MobileTableHeader>

      {/* Table Body */}
      <MobileTableBody>
        {data.map((row, rowIndex) => (
          <MobileTableRow key={rowIndex}>
            {headers.map((header, cellIndex) => (
              <MobileTableCell
                key={cellIndex}
                label={header}
              >
                {row[header] || '-'}
              </MobileTableCell>
            ))}
          </MobileTableRow>
        ))}
      </MobileTableBody>
    </MobileTable>
  )
)
ResponsiveTable.displayName = "ResponsiveTable"

export {
  MobileTable,
}
