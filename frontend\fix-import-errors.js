const fs = require('fs');
const path = require('path');

// Function to find all TypeScript/TSX files
function findTsxFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (file !== 'node_modules' && file !== '.next' && file !== '.git') {
        results = results.concat(findTsxFiles(filePath));
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Function to fix import statements
function fixImportStatements(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to match broken import statements from lucide-react
    // Look for lines that start with identifiers followed by } from 'lucide-react'
    const brokenImportPattern = /^(\s*)([A-Z][a-zA-Z0-9_,\s]*)\s*}\s*from\s*['"]lucide-react['"]/gm;
    
    // Find all matches
    const matches = [...content.matchAll(brokenImportPattern)];
    
    if (matches.length > 0) {
      console.log(`🔧 Fixing import statements in: ${filePath}`);
      
      // Process matches in reverse order to maintain line positions
      for (let i = matches.length - 1; i >= 0; i--) {
        const match = matches[i];
        const fullMatch = match[0];
        const indentation = match[1];
        const imports = match[2];
        
        // Find the start of this broken import by looking backwards
        const beforeMatch = content.substring(0, match.index);
        const lines = beforeMatch.split('\n');
        
        // Look for the previous import statement or beginning of broken import
        let startIndex = match.index;
        let searchIndex = match.index - 1;
        
        // Search backwards to find where this import statement actually starts
        while (searchIndex >= 0) {
          const char = content[searchIndex];
          if (char === '\n') {
            const lineStart = searchIndex + 1;
            const lineContent = content.substring(lineStart, match.index).trim();
            
            // If we find a line that looks like it could be part of an import
            if (lineContent && !lineContent.startsWith('import') && !lineContent.includes('from')) {
              // This might be part of the broken import
              startIndex = lineStart;
              searchIndex--;
              continue;
            } else if (lineContent.startsWith('import')) {
              // Found the start of the import statement
              startIndex = lineStart;
              break;
            } else {
              break;
            }
          }
          searchIndex--;
        }
        
        // Extract the broken import section
        const brokenSection = content.substring(startIndex, match.index + fullMatch.length);
        
        // Try to reconstruct the proper import
        let fixedImport = '';
        
        // Check if there's already an import { at the beginning
        if (brokenSection.includes('import {')) {
          // Just fix the formatting
          const importMatch = brokenSection.match(/import\s*\{\s*([\s\S]*?)\s*\}\s*from\s*['"]lucide-react['"]/);
          if (importMatch) {
            const allImports = importMatch[1].replace(/\s+/g, ' ').trim();
            const importList = allImports.split(',').map(imp => imp.trim()).filter(imp => imp);
            const uniqueImports = [...new Set(importList)];
            
            fixedImport = `import {\n  ${uniqueImports.join(',\n  ')}\n} from 'lucide-react'`;
          }
        } else {
          // Need to add import { at the beginning
          const allImports = imports.replace(/\s+/g, ' ').trim();
          const importList = allImports.split(',').map(imp => imp.trim()).filter(imp => imp);
          const uniqueImports = [...new Set(importList)];
          
          fixedImport = `import {\n  ${uniqueImports.join(',\n  ')}\n} from 'lucide-react'`;
        }
        
        // Replace the broken section with the fixed import
        content = content.substring(0, startIndex) + fixedImport + content.substring(match.index + fullMatch.length);
        modified = true;
      }
    }
    
    // Also fix any duplicate lucide-react imports
    const importLines = content.split('\n');
    const lucideImports = [];
    const otherLines = [];
    
    for (let i = 0; i < importLines.length; i++) {
      const line = importLines[i];
      if (line.includes("from 'lucide-react'") || line.includes('from "lucide-react"')) {
        // Extract imports from this line
        const match = line.match(/import\s*\{\s*(.*?)\s*\}\s*from\s*['"]lucide-react['"]/);
        if (match) {
          const imports = match[1].split(',').map(imp => imp.trim()).filter(imp => imp);
          lucideImports.push(...imports);
        }
      } else if (!line.trim().match(/^[A-Z][a-zA-Z0-9_,\s]*$/)) {
        // Not a standalone import identifier line
        otherLines.push(line);
      }
    }
    
    if (lucideImports.length > 0) {
      // Remove duplicates and create a single import statement
      const uniqueImports = [...new Set(lucideImports)];
      const consolidatedImport = `import {\n  ${uniqueImports.join(',\n  ')}\n} from 'lucide-react'`;
      
      // Find where to insert the consolidated import (after other imports)
      let insertIndex = 0;
      for (let i = 0; i < otherLines.length; i++) {
        if (otherLines[i].startsWith('import ') || otherLines[i].startsWith('"use client"') || otherLines[i].startsWith("'use client'")) {
          insertIndex = i + 1;
        } else if (otherLines[i].trim() === '') {
          continue;
        } else {
          break;
        }
      }
      
      // Insert the consolidated import
      otherLines.splice(insertIndex, 0, consolidatedImport);
      content = otherLines.join('\n');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed import statements in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
const srcDir = path.join(process.cwd(), 'src');
console.log('🔧 Starting import statement fixes...');
console.log(`📁 Scanning directory: ${srcDir}`);

const tsxFiles = findTsxFiles(srcDir);
console.log(`📄 Found ${tsxFiles.length} TypeScript/TSX files`);

let fixedCount = 0;
let totalCount = tsxFiles.length;

tsxFiles.forEach(file => {
  if (fixImportStatements(file)) {
    fixedCount++;
  }
});

console.log('\n🎉 Import Fix Summary:');
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`📄 Total files: ${totalCount}`);
console.log(`⏭️  Files unchanged: ${totalCount - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n🚀 Run "npm run type-check" to verify fixes!');
} else {
  console.log('\n✨ All import statements are already correct!');
}
