"use strict";(()=>{var e={};e.id=3031,e.ids=[3031],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79144:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var r={};s.r(r),s.d(r,{DELETE:()=>l,GET:()=>u,PATCH:()=>c,PUT:()=>p});var i=s(96559),a=s(48088),o=s(37719),n=s(32190),d=s(38561);async function u(e,{params:t}){try{let{searchParams:s}=new URL(e.url),r="true"===s.get("include_usage"),i="true"===s.get("include_activities"),a=d.CN.getAIModels().find(e=>e.id===t.id);if(!a)return n.NextResponse.json({error:"النموذج غير موجود"},{status:404});let o={model:a};return r&&(o.usage=a.usage,o.usageStats={dailyAverage:a.usage.dailyUsage.length>0?a.usage.dailyUsage.reduce((e,t)=>e+t.requests,0)/a.usage.dailyUsage.length:0,monthlyTotal:a.usage.monthlyUsage.reduce((e,t)=>e+t.requests,0),costPerRequest:a.usage.totalRequests>0?a.usage.totalCost/a.usage.totalRequests:0,tokensPerRequest:a.usage.totalRequests>0?a.usage.totalTokens/a.usage.totalRequests:0}),i&&(o.activities=d.CN.getModelActivities().filter(e=>e.modelId===t.id).sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,50)),n.NextResponse.json(o)}catch(e){return n.NextResponse.json({error:"خطأ في جلب النموذج"},{status:500})}}async function p(e,{params:t}){try{let{name:s,description:r,apiKey:i,apiEndpoint:a,baseUrl:o,isActive:u,settings:p,selectedModels:l,subModels:c}=await e.json(),g=d.CN.getAIModels(),m=g.findIndex(e=>e.id===t.id);if(-1===m)return n.NextResponse.json({error:"النموذج غير موجود"},{status:404});let v=g[m];if(s&&s!==v.name&&g.find(e=>e.name.toLowerCase()===s.toLowerCase()&&e.provider===v.provider&&e.id!==t.id))return n.NextResponse.json({error:"نموذج بنفس الاسم ومقدم الخدمة موجود بالفعل"},{status:400});let h={...v,...s&&{name:s},...void 0!==r&&{description:r},...void 0!==i&&{apiKey:i},...void 0!==a&&{apiEndpoint:a},...void 0!==o&&{baseUrl:o},...void 0!==u&&{isActive:u},...p&&{settings:{...v.settings,...p}},...l&&{selectedModels:l},...c&&{subModels:c},updatedAt:new Date().toISOString()};void 0!==u&&(h.status=u?"active":"inactive"),g[m]=h,d.CN.saveAIModels(g);let x=d.CN.getModelActivities(),N=[];return s&&s!==v.name&&N.push(`الاسم: ${s}`),void 0!==r&&r!==v.description&&N.push("الوصف"),void 0!==i&&N.push("مفتاح API"),void 0!==a&&N.push("نقطة النهاية"),void 0!==o&&N.push("Base URL"),void 0!==u&&u!==v.isActive&&N.push(u?"تفعيل":"إلغاء تفعيل"),p&&N.push("الإعدادات"),l&&N.push(`النماذج المحددة: ${l.length}`),c&&N.push(`النماذج الفرعية: ${c.length}`),x.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:`تم تحديث النموذج: ${N.join(", ")}`,timestamp:new Date().toISOString(),success:!0}),d.CN.saveModelActivities(x),n.NextResponse.json({message:"تم تحديث النموذج بنجاح",model:h})}catch(e){return n.NextResponse.json({error:"خطأ في تحديث النموذج"},{status:500})}}async function l(e,{params:t}){try{let e=d.CN.getAIModels(),s=e.findIndex(e=>e.id===t.id);if(-1===s)return n.NextResponse.json({error:"النموذج غير موجود"},{status:404});let r=e[s];if(r.usage.totalRequests>0&&r.usage.lastUsed&&new Date(r.usage.lastUsed).getTime()>Date.now()-6048e5)return n.NextResponse.json({error:"لا يمكن حذف النموذج لوجود استخدام حديث. يرجى إلغاء تفعيله بدلاً من ذلك.",suggestion:"deactivate"},{status:400});e.splice(s,1),d.CN.saveAIModels(e);let i=d.CN.getModelActivities();return i.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:`تم حذف النموذج: ${r.name}`,timestamp:new Date().toISOString(),success:!0}),d.CN.saveModelActivities(i),n.NextResponse.json({message:"تم حذف النموذج بنجاح",deletedModel:{id:r.id,name:r.name,provider:r.provider}})}catch(e){return n.NextResponse.json({error:"خطأ في حذف النموذج"},{status:500})}}async function c(e,{params:t}){try{let{action:s,data:r}=await e.json();if(!s)return n.NextResponse.json({error:"الإجراء مطلوب"},{status:400});let i=d.CN.getAIModels(),a=i.findIndex(e=>e.id===t.id);if(-1===a)return n.NextResponse.json({error:"النموذج غير موجود"},{status:404});let o=i[a],u=d.CN.getModelActivities();switch(s){case"test_connection":let p=Math.random()>.1,l=Math.floor(3e3*Math.random())+500;o.lastTestedAt=new Date().toISOString(),o.testResult={success:p,responseTime:l,error:p?void 0:"فشل في الاتصال بالخدمة"},p?o.status="active":o.status="error",u.push({id:d.CN.generateId(),modelId:t.id,type:"test",description:`اختبار الاتصال: ${p?"نجح":"فشل"}`,timestamp:new Date().toISOString(),duration:l,success:p,errorMessage:p?void 0:"فشل في الاتصال"});break;case"reset_usage":o.usage={totalRequests:0,totalTokens:0,totalCost:0,dailyUsage:[],monthlyUsage:[],averageResponseTime:0,successRate:0},u.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:"تم إعادة تعيين إحصائيات الاستخدام",timestamp:new Date().toISOString(),success:!0});break;case"add_submodel":if(!r||!r.name)return n.NextResponse.json({error:"بيانات النموذج الفرعي مطلوبة"},{status:400});let c={id:d.CN.generateId(),name:r.name,modelId:t.id,description:r.description||"",version:r.version||"1.0",capabilities:r.capabilities||[],pricing:r.pricing||{inputTokens:0,outputTokens:0,currency:"USD",unit:"1K tokens"},limits:r.limits||{maxTokens:4096,requestsPerMinute:60,requestsPerDay:1e3,contextWindow:4096},isActive:!0,isDefault:!1,tags:r.tags||[],releaseDate:new Date().toISOString()};o.subModels.push(c),u.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:`تم إضافة نموذج فرعي: ${r.name}`,timestamp:new Date().toISOString(),success:!0});break;default:return n.NextResponse.json({error:"إجراء غير مدعوم"},{status:400})}return o.updatedAt=new Date().toISOString(),i[a]=o,d.CN.saveAIModels(i),d.CN.saveModelActivities(u),n.NextResponse.json({message:"تم تنفيذ الإجراء بنجاح",model:o,action:s})}catch(e){return n.NextResponse.json({error:"خطأ في تنفيذ الإجراء"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ai-models/[id]/route",pathname:"/api/ai-models/[id]",filename:"route",bundlePath:"app/api/ai-models/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:h}=g;function x(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,8554],()=>s(79144));module.exports=r})();