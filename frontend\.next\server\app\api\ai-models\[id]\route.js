(()=>{var e={};e.id=3031,e.ids=[3031],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79144:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var r={};s.r(r),s.d(r,{DELETE:()=>c,GET:()=>u,PATCH:()=>p,PUT:()=>l});var i=s(96559),n=s(48088),o=s(37719),a=s(32190),d=s(38561);async function u(e,{params:t}){try{let{searchParams:s}=new URL(e.url),r="true"===s.get("include_usage"),i="true"===s.get("include_activities"),n=d.C.getAIModels().find(e=>e.id===t.id);if(!n)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let o={model:n};return r&&(o.usage=n.usage,o.usageStats={dailyAverage:n.usage.dailyUsage.length>0?n.usage.dailyUsage.reduce((e,t)=>e+t.requests,0)/n.usage.dailyUsage.length:0,monthlyTotal:n.usage.monthlyUsage.reduce((e,t)=>e+t.requests,0),costPerRequest:n.usage.totalRequests>0?n.usage.totalCost/n.usage.totalRequests:0,tokensPerRequest:n.usage.totalRequests>0?n.usage.totalTokens/n.usage.totalRequests:0}),i&&(o.activities=d.C.getModelActivities().filter(e=>e.modelId===t.id).sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,50)),a.NextResponse.json(o)}catch(e){return a.NextResponse.json({error:"خطأ في جلب النموذج"},{status:500})}}async function l(e,{params:t}){try{let{name:s,description:r,apiKey:i,apiEndpoint:n,baseUrl:o,isActive:u,settings:l,selectedModels:c,subModels:p}=await e.json(),g=d.C.getAIModels(),m=g.findIndex(e=>e.id===t.id);if(-1===m)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let x=g[m];if(s&&s!==x.name&&g.find(e=>e.name.toLowerCase()===s.toLowerCase()&&e.provider===x.provider&&e.id!==t.id))return a.NextResponse.json({error:"نموذج بنفس الاسم ومقدم الخدمة موجود بالفعل"},{status:400});let v={...x,...s&&{name:s},...void 0!==r&&{description:r},...void 0!==i&&{apiKey:i},...void 0!==n&&{apiEndpoint:n},...void 0!==o&&{baseUrl:o},...void 0!==u&&{isActive:u},...l&&{settings:{...x.settings,...l}},...c&&{selectedModels:c},...p&&{subModels:p},updatedAt:new Date().toISOString()};void 0!==u&&(v.status=u?"active":"inactive"),g[m]=v,d.C.saveAIModels(g);let h=d.C.getModelActivities(),f=[];return s&&s!==x.name&&f.push(`الاسم: ${s}`),void 0!==r&&r!==x.description&&f.push("الوصف"),void 0!==i&&f.push("مفتاح API"),void 0!==n&&f.push("نقطة النهاية"),void 0!==o&&f.push("Base URL"),void 0!==u&&u!==x.isActive&&f.push(u?"تفعيل":"إلغاء تفعيل"),l&&f.push("الإعدادات"),c&&f.push(`النماذج المحددة: ${c.length}`),p&&f.push(`النماذج الفرعية: ${p.length}`),h.push({id:d.C.generateId(),modelId:t.id,type:"config_change",description:`تم تحديث النموذج: ${f.join(", ")}`,timestamp:new Date().toISOString(),success:!0}),d.C.saveModelActivities(h),a.NextResponse.json({message:"تم تحديث النموذج بنجاح",model:v})}catch(e){return a.NextResponse.json({error:"خطأ في تحديث النموذج"},{status:500})}}async function c(e,{params:t}){try{let e=d.C.getAIModels(),s=e.findIndex(e=>e.id===t.id);if(-1===s)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let r=e[s];if(r.usage.totalRequests>0&&r.usage.lastUsed&&new Date(r.usage.lastUsed).getTime()>Date.now()-6048e5)return a.NextResponse.json({error:"لا يمكن حذف النموذج لوجود استخدام حديث. يرجى إلغاء تفعيله بدلاً من ذلك.",suggestion:"deactivate"},{status:400});e.splice(s,1),d.C.saveAIModels(e);let i=d.C.getModelActivities();return i.push({id:d.C.generateId(),modelId:t.id,type:"config_change",description:`تم حذف النموذج: ${r.name}`,timestamp:new Date().toISOString(),success:!0}),d.C.saveModelActivities(i),a.NextResponse.json({message:"تم حذف النموذج بنجاح",deletedModel:{id:r.id,name:r.name,provider:r.provider}})}catch(e){return a.NextResponse.json({error:"خطأ في حذف النموذج"},{status:500})}}async function p(e,{params:t}){try{let{action:s,data:r}=await e.json();if(!s)return a.NextResponse.json({error:"الإجراء مطلوب"},{status:400});let i=d.C.getAIModels(),n=i.findIndex(e=>e.id===t.id);if(-1===n)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let o=i[n],u=d.C.getModelActivities();switch(s){case"test_connection":let l=Math.random()>.1,c=Math.floor(3e3*Math.random())+500;o.lastTestedAt=new Date().toISOString(),o.testResult={success:l,responseTime:c,error:l?void 0:"فشل في الاتصال بالخدمة"},l?o.status="active":o.status="error",u.push({id:d.C.generateId(),modelId:t.id,type:"test",description:`اختبار الاتصال: ${l?"نجح":"فشل"}`,timestamp:new Date().toISOString(),duration:c,success:l,errorMessage:l?void 0:"فشل في الاتصال"});break;case"reset_usage":o.usage={totalRequests:0,totalTokens:0,totalCost:0,dailyUsage:[],monthlyUsage:[],averageResponseTime:0,successRate:0},u.push({id:d.C.generateId(),modelId:t.id,type:"config_change",description:"تم إعادة تعيين إحصائيات الاستخدام",timestamp:new Date().toISOString(),success:!0});break;case"add_submodel":if(!r||!r.name)return a.NextResponse.json({error:"بيانات النموذج الفرعي مطلوبة"},{status:400});let p={id:d.C.generateId(),name:r.name,modelId:t.id,description:r.description||"",version:r.version||"1.0",capabilities:r.capabilities||[],pricing:r.pricing||{inputTokens:0,outputTokens:0,currency:"USD",unit:"1K tokens"},limits:r.limits||{maxTokens:4096,requestsPerMinute:60,requestsPerDay:1e3,contextWindow:4096},isActive:!0,isDefault:!1,tags:r.tags||[],releaseDate:new Date().toISOString()};o.subModels.push(p),u.push({id:d.C.generateId(),modelId:t.id,type:"config_change",description:`تم إضافة نموذج فرعي: ${r.name}`,timestamp:new Date().toISOString(),success:!0});break;default:return a.NextResponse.json({error:"إجراء غير مدعوم"},{status:400})}return o.updatedAt=new Date().toISOString(),i[n]=o,d.C.saveAIModels(i),d.C.saveModelActivities(u),a.NextResponse.json({message:"تم تنفيذ الإجراء بنجاح",model:o,action:s})}catch(e){return a.NextResponse.json({error:"خطأ في تنفيذ الإجراء"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai-models/[id]/route",pathname:"/api/ai-models/[id]",filename:"route",bundlePath:"app/api/ai-models/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:v}=g;function h(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(79144));module.exports=r})();