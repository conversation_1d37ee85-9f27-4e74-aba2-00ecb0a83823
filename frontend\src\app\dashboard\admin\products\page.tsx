
"use client"

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import {  Content, Item, Trigger, Value } from '@/components/ui/select'
import {  Content, Description, Header, Title, Trigger } from '@/components/ui/dialog'
import {  Content, List, Trigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { ProductForm } from '@/components/admin/ProductForm'
import { Product } from '@/components/admin/ Product '
import { Confirm } from '@/components/ui/confirm-dialog'
import { ToastContainer, useToast } from '@/components/ui/toast'
import Link from 'next/link'
import {
  Search,
  Trash2,
  MoreHorizontal,
  ArrowLeft,
  import {
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
  Brain,
  FileText,
  Tag,
  List,
  Wand2,
  Brain
} from 'lucide-react'

// أنواع البيانات
interface Product {
  id: string
  name: string
  description: string
  category: 'gown' | 'cap' | 'tassel' | 'stole' | 'hood'
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: string[]
  stock_quantity: number
  is_available: boolean
  is_published: boolean
  created_at: string
  updated_at: string
  rating?: number
  reviews_count?: number
}

interface Category {
  id: string
  name_ar: string
  name_en?: string
  name_fr?: string
  slug: string
  description?: string
  icon?: string
  is_active: boolean
  order_index: number
  created_at: string
  updated_at: string
}

const categoryNames = {
  gown: 'ثوب التخرج',
  cap: 'قبعة التخرج',
  tassel: 'الشرابة',
  stole: 'الوشاح',
  hood: 'القلنسوة'
}

export default function ProductsManagement() {
  const { } = useAuth() // ✅ إصلاح: إزالة متغيرات غير مستخدمة
  const toast = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, set edProducts] = useState<Product[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [category setCategory ] = useState<string>('all')
  const [availability setAvailability ] = useState<string>('all')
  const [published setPublished ] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [loading, setLoading] = useState(true)
  const [showAddProduct, setShowAddProduct] = useState(false)
  const [editingProduct, set ingProduct] = useState<Product | null>(null)
  const [showProgresssetShow ] = useState(false)
  const [deleteConfirm, setDeleteConfirm] = useState<{
    open: boolean
    productId: string
    productName: string
  }>({ open: false, productId: '', productName: '' })

  // State للفئات
  const [categories, setCategories] = useState<Category[]>([])
  const [showAddCategory, setShowAddCategory] = useState(false)
  const [editingCategory, set ingCategory] = useState<Category | null>(null)
  const [categoryFormData, setCategoryFormData] = useState({
    name_ar: '',
    slug: '',
    description: '',
    icon: '',
    is_active: true,
    order_index: 0
  })
  const [activeTab, setActiveTab] = useState('products')

  // جلب المنتجات من API
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts]) // ✅ إصلاح: إضافة fetchProducts للـ dependencies

  // ✅ إصلاح: استخدام useCallback لـ fetchProducts
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/products')

      if (!response.ok) {
        throw new Error('فشل في جلب المنتجات')
      }

      const data = await response.json()
      setProducts(data.products || [])
      setFilteredProducts(data.products || [])
    } catch (_error) {
      toast.error('فشل في جلب المنتجات')
    } finally {
      setLoading(false)
    }
  }, []) // ✅ إصلاح: dependencies فارغة لأن API endpoint ثابت

  // تطبيق الفلاتر والبحث
  useEffect(() => {
    let filtered = [...products]

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلتر الفئة
    if (category !== 'all') {
      filtered = filtered.filter(product => product.category === category )
    }

    // فلتر التوفر
    if (availability !== 'all') {
      filtered = filtered.filter(product =>
        availability === 'available' ? product.is_available : !product.is_available
      )
    }

    // فلتر النشر
    if (published !== 'all') {
      filtered = filtered.filter(product =>
        published === 'published' ? product.is_published : !product.is_published
      )
    }

    // الترتيب
    filtered.sort((a, b) => {
      let aValue: unknown = a[sortBy as keyof Product]
      let bValue: unknown = b[sortBy as keyof Product]

      if (sortBy === 'price' || sortBy === 'stock_quantity' || sortBy === 'rating') {
        aValue = Number(aValue) || 0
        bValue = Number(bValue) || 0
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    set edProducts(filtered)
  }, [products, searchTerm, category availability published sortBy, sortOrder])

  // فتح حوار تأكيد الحذف
  const openDeleteConfirm = (productId: string, productName: string) => {
    setDeleteConfirm({
      open: true,
      productId,
      productName
    })
  }

  // تنفيذ حذف المنتج
  const handleDeleteProduct = async () => {
    try {const response = await fetch(`/api/products/${deleteConfirm.productId}`, {
        method: 'DELETE'
      })if (!response.ok) {
        const errorData = await response.json()throw new Error(errorData.error || 'فشل في حذف المنتج')
      }

      const successData = await response.json()// إغلاق نافذة التأكيد
      setDeleteConfirm({ open: false, productId: '', productName: '' })

      // تحديث قائمة المنتجات
      await fetchProducts()
      toast.success('تم حذف المنتج بنجاح!')
    } catch (_error) {toast.error(error instanceof Error ? error.message : 'فشل في حذف المنتج')
      // إغلاق نافذة التأكيد حتى في حالة الخطأ
      setDeleteConfirm({ open: false, productId: '', productName: '' })
    }
  }

  const handleToggleAvailability = async (productId: string) => {
    try {
      const product = products.find(p => p.id === productId)
      if (!product) return

      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_available: !product.is_available
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث حالة المنتج')
      }

      // تحديث قائمة المنتجات
      await fetchProducts()
      toast.success(`تم ${!product.is_available ? 'تفعيل' : 'إلغاء تفعيل'} المنتج بنجاح`)
    } catch (_error) {toast.error(error instanceof Error ? error.message : 'فشل في تحديث حالة المنتج')
    }
  }

  const handleTogglePublished = async (productId: string) => {
    try {
      const product = products.find(p => p.id === productId)
      if (!product) return

      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_published: !product.is_published
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث حالة النشر')
      }

      // تحديث قائمة المنتجات
      await fetchProducts()
      toast.success(`تم ${!product.is_published ? 'نشر' : 'إلغاء نشر'} المنتج بنجاح`)
    } catch (_error) {toast.error(error instanceof Error ? error.message : 'فشل في تحديث حالة النشر')
    }
  }

  const handleAddProduct = async (productData: unknown) => {
    try {
      setLoading(true)

      // معالجة الصور
      let imageUrls: string[] = []
      if (productData.images && productData.images.length > 0) {
        imageUrls = productData.images.map((img: unknown) => {
          // إذا كانت الصورة مرفوعة بالفعل، استخدم الرابط المحفوظ
          if (img.uploaded && img.fallbackUrl) {
            return img.fallbackUrl
          }
          // إذا لم تكن مرفوعة، استخدم المعاينة كنسخة احتياطية
          return img.preview
        }).filter(Boolean)
      }

      // إضافة المنتج
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...productData,
          images: imageUrls
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إضافة المنتج')
      }

      const data = await response.json()

      // تحديث قائمة المنتجات
      await fetchProducts()

      setShowAddProduct(false)
      toast.success('تم إضافة المنتج بنجاح!')
    } catch (_error) {toast.error(error instanceof Error ? error.message : 'فشل في إضافة المنتج')
    } finally {
      setLoading(false)
    }
  }

  // فتح حوار التعديل
  const open = (product: Product) => {
    set ingProduct(product)
    setShow (true)
  }

  // تحديث المنتج
  const handleUpdateProduct = async (productData: unknown) => {
    try {
      setLoading(true)

      // معالجة الصور
      let imageUrls: string[] = []
      if (productData.images && productData.images.length > 0) {
        imageUrls = productData.images.map((img: unknown) => {
          if (img.uploaded && img.fallbackUrl) {
            return img.fallbackUrl
          }
          return img.preview
        }).filter(Boolean)
      }

      const response = await fetch(`/api/products/${productData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...productData,
          images: imageUrls
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث المنتج')
      }

      await fetchProducts()
      toast.success('تم تحديث المنتج بنجاح!')
    } catch (_error) {toast.error(error instanceof Error ? error.message : 'فشل في تحديث المنتج')
    } finally {
      setLoading(false)
    }
  }

  // جلب الفئات
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || [])
      }
    } catch (_error) {}
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  // إضافة/تحديث فئة
  const handleFileUploadCategory = async () => {
    try {
      const url = editingCategory ? `/api/categories/${editingCategory.id}` : '/api/categories'
      const method = editingCategory ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(categoryFormData)
      })

      if (response.ok) {
        await fetchCategories()
        resetCategoryForm()
        toast.success(editingCategory ? 'تم تحديث الفئة بنجاح!' : 'تم إضافة الفئة بنجاح!')
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'فشل في حفظ الفئة')
      }
    } catch (_error) {toast.error('فشل في حفظ الفئة')
    }
  }

  // إعادة تعيين نموذج الفئة
  const resetCategoryForm = () => {
    setCategoryFormData({
      name_ar: '',
      slug: '',
      description: '',
      icon: '',
      is_active: true,
      order_index: 0
    })
    set ingCategory(null)
    setShowAddCategory(false)
  }

  // تحضير تعديل فئة
  const prepare Category = (category: unknown) => {
    setCategoryFormData({
      name_ar: category.name_ar,
      slug: category.slug,
      description: category.description || '',
      icon: category.icon || '',
      is_active: category.is_active,
      order_index: category.order_index
    })
    set ingCategory(category)
    setShowAddCategory(true)
  }

  // حذف فئة
  const handleDeleteCategory = async (categoryId: string) => {
    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchCategories()
        toast.success('تم حذف الفئة بنجاح!')
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'فشل في حذف الفئة')
      }
    } catch (_error) {toast.error('فشل في حذف الفئة')
    }
  }

  // تشخيص للتطويرif (!user || profile?.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 arabic-text">غير مصرح لك بالوصول</h1>
          <p className="text-gray-600 mt-2 arabic-text">هذه الصفحة مخصصة للمديرين فقط</p>
          <p className="text-sm text-gray-500 mt-2"> : {user ? 'موجود' : 'غير موجود'}</p>
          <p className="text-sm text-gray-500">Role: {profile?.role || 'غير محدد'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </Link>
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة المنتجات والفئات 📦
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إضافة وتعديل وإدارة منتجات وفئات المنصة
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <div className="h-4 w-4 mr-2" />
                تصدير
              </Button>
              {activeTab === 'products' && (
                <Button size="sm" onClick={() => setShowAddProduct(true)}>
                  <div className="h-4 w-4 mr-2" />
                  إضافة منتج جديد
                </Button>
              )}
              {activeTab === 'categories' && (
                <Button size="sm" onClick={() => { resetCategoryForm(); setShowAddCategory(true); }}>
                  <div className="h-4 w-4 mr-2" />
                  إضافة فئة جديدة
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* AI Enhancement Stats */}
        <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-blue-600 rounded-lg">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white arabic-text">
                    ميزات الذكاء الاصطناعي المتاحة
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 arabic-text">
                    استخدم الذكاء الاصطناعي لتحسين منتجاتك تلقائياً
                  </p>
                </div>
              </div>
              <div className="flex gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">6</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">ميزات متاحة</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">نشط</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 arabic-text">حالة النظام</div>
                </div>
              </div>
            </div>
            <div className="mt-4 flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <FileText className="h-3 w-3 mr-1" />
                توليد الأوصاف
              </div>
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <Tag className="h-3 w-3 mr-1" />
                توليد العناوين
              </div>
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <List className="h-3 w-3 mr-1" />
                توليد الميزات
              </div>
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <div className="h-3 w-3 mr-1" />
                توليد المواصفات
              </div>
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <Wand2 className="h-3 w-3 mr-1" />
                اقتراح الفئات
              </div>
              <Badge variant="outline" className="bg-white dark:bg-gray-800">
                <Search className="h-3 w-3 mr-1" />
                تحسين SEO
              </div>
            </div>
          </CardContent>
        </Card>

        {/* */}
        <Select value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="products" className="arabic-text">المنتجات</TabsTrigger>
            <SelectTrigger value="categories" className="arabic-text">الفئات</TabsTrigger>
          </TabsList>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-6">

        {/* Stats Cards */}
        <div className="product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي المنتجات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {products.length}
                  </p>
                </div>
                <div className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    المنتجات المتاحة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {products.filter(p => p.is_available).length}
                  </p>
                </div>
                <div className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    المنتجات المنشورة
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {products.filter(p => p.is_published).length}
                  </p>
                </div>
                <div className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    مخزون منخفض
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {products.filter(p => p.stock_quantity < 20).length}
                  </p>
                </div>
                <div className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    متوسط التقييم
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {(products.reduce((acc, p) => acc + (p.rating || 0), 0) / products.length).toFixed(1)}
                  </p>
                </div>
                <div className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* s and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {/* البحث */}
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input placeholder="البحث في المنتجات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 arabic-text"
                />
              </div>

              {/* فلتر الفئة */}
              <Select value={category } onValueChange={setCategory }>
                <TabsTrigger>
                  <SelectValue placeholder="جميع الفئات" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {Object.entries(categoryNames).map(([key, name]) => (
                    <SelectItem key={key} value={key}>{name}</SelectItem>
                  ))}
                </TabsContent>
              </div>

              {/* فلتر التوفر */}
              <Select value={availability } onValueChange={setAvailability }>
                <TabsTrigger>
                  <SelectValue placeholder="جميع المنتجات" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المنتجات</SelectItem>
                  <SelectItem value="available">متاح</SelectItem>
                  <SelectItem value="unavailable">غير متاح</SelectItem>
                </TabsContent>
              </div>

              {/* فلتر النشر */}
              <Select value={published } onValueChange={setPublished }>
                <TabsTrigger>
                  <SelectValue placeholder="حالة النشر" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المنتجات</SelectItem>
                  <SelectItem value="published">منشور</SelectItem>
                  <SelectItem value="unpublished">غير منشور</SelectItem>
                </TabsContent>
              </div>

              {/* ترتيب حسب */}
              <Select value={sortBy} onValueChange={setSortBy}>
                < Trigger>
                  <SelectValue placeholder="ترتيب حسب" />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="created_at">تاريخ الإنشاء</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                  <SelectItem value="price">السعر</SelectItem>
                  <SelectItem value="stock_quantity">المخزون</SelectItem>
                  <SelectItem value="rating">التقييم</SelectItem>
                </TabsContent>
              </div>

              {/* اتجاه الترتيب */}
              <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}>
                < Trigger>
                  <SelectValue />
                </TabsTrigger>
                <SelectContent>
                  <SelectItem value="desc">تنازلي</SelectItem>
                  <SelectItem value="asc">تصاعدي</SelectItem>
                </TabsContent>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Table */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">قائمة المنتجات ({filteredProducts.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2 arabic-text">جاري التحميل...</p>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-8">
                <div className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 arabic-text">لا توجد منتجات</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">المنتج</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">الفئة</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">السعر</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">المخزون</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">التقييم</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">الحالة</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">النشر</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProducts.map((product) => (
                      <tr key={product.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-4">
                            <div className="relative">
                              <img
                                src={product.images[0] || '/api/placeholder/80/80'}
                                alt={product.name}
                                className="w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-700"
                              / alt="">
                              {product.images.length > 1 && (
                                <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                  {product.images.length}
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-gray-900 dark:text-white arabic-text line-clamp-1">
                                {product.name}
                              </p>
                              <p className="text-sm text-gray-500 arabic-text line-clamp-2 mt-1">
                                {product.description}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge variant="outline" className="arabic-text">
                            {categoryNames[product.category]}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className="price font-medium text-gray-900 dark:text-white">
                              {product.price} Dhs
                            </p>
                            {product.rental_price && (
                              <p className="price text-gray-500">
                                إيجار: {product.rental_price} Dhs
                              </p>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            <span className={`font-medium ${
                              product.stock_quantity < 20
                                ? 'text-red-600'
                                : product.stock_quantity < 50
                                  ? 'text-orange-600'
                                  : 'text-green-600'
                            }`}>
                              {product.stock_quantity}
                            </span>
                            {product.stock_quantity < 20 && (
                              <div className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="rating flex items-center gap-1">
                            <div className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="number text-sm font-medium">
                              {product.rating?.toFixed(1) || 'N/A'}
                            </span>
                            <span className="number text-xs text-gray-500">
                              ({product.reviews_count || 0})
                            </span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          < variant={product.is_available ? "default" : "secondary"}
                            className="arabic-text"
                          >
                            {product.is_available ? 'متاح' : 'غير متاح'}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          < variant={product.is_published ? "default" : "destructive"}
                            className="arabic-text"
                          >
                            {product.is_published ? 'منشور' : 'غير منشور'}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <TooltipProvider>
                            <div className="actions flex items-center gap-2">
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button size="sm" variant="outline" asChild>
                                    <a href={`/product/${product.id}`} target="_blank">
                                      <div className="h-4 w-4" />
                                    </a>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>عرض المنتج</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => open (product)}
                                  >
                                    <div className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>تعديل المنتج</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleToggleAvailability(product.id)}
                                  >
                                    {product.is_available ? '🔒' : '🔓'}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{product.is_available ? 'إلغاء التوفر' : 'تفعيل التوفر'}</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleTogglePublished(product.id)}
                                  >
                                    {product.is_published ? '👁️' : '🙈'}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{product.is_published ? 'إلغاء النشر' : 'نشر المنتج'}</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => openDeleteConfirm(product.id, product.name)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>حذف المنتج</p>
                                </TooltipContent>
                              </Tooltip>
                            </div>
                          </TooltipProvider>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            {/* Categories Management */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إدارة الفئات</CardTitle>
                <div className="arabic-text">
                  إضافة وتعديل وإدارة فئات المنتجات
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="arabic-text">الاسم</TableHead>
                      <TableHead className="arabic-text">الرابط المختصر</TableHead>
                      <TableHead className="arabic-text">الحالة</TableHead>
                      <TableHead className="arabic-text">الترتيب</TableHead>
                      <TableHead className="arabic-text">تاريخ الإنشاء</TableHead>
                      <TableHead className="arabic-text">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.map((category) => (
                      <TableRow key={category.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {category.icon && <span className="text-lg">{category.icon}</span>}
                            <div>
                              <div className="font-medium arabic-text">{category.name_ar}</div>
                              {category.description && (
                                <div className="text-sm text-gray-500 arabic-text">{category.description}</div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">
                            {category.slug}
                          </code>
                        </TableCell>
                        <TableCell>
                          < variant={category.is_active ? "default" : "secondary"}>
                            {category.is_active ? 'نشط' : 'غير نشط'}
                          </div>
                        </TableCell>
                        <TableCell>{category.order_index}</TableCell>
                        <TableCell>
                          {new Date(category.created_at).toLocaleDateString('en-US')}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => prepare Category(category)}>
                                <div className="h-4 w-4 mr-2" />
                                تعديل
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteCategory(category.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                حذف
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </main>

      {/* Add/ Category */}
      <Dialog open={showAddCategory} onOpenChange={setShowAddCategory}>
        < Content className="max-w-2xl">
          < Header>
            < Title className="arabic-text">
              {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
            </ Title>
            < Description className="arabic-text">
              {editingCategory ? 'تعديل بيانات الفئة' : 'أدخل بيانات الفئة الجديدة'}
            </ Description>
          </ Header>
          <div className="space-y-4">
            <div>
              <Label htmlFor="category-name" className="arabic-text">اسم الفئة (مطلوب)</Label>
              <Input id="category-name"
                value={categoryFormData.name_ar}
                onChange={(e) => setCategoryFormData(prev => ({ ...prev, name_ar: e.target.value }))}
                placeholder="أدخل اسم الفئة"
                className="arabic-text"
              />
            </div>
            <div>
              <Label htmlFor="category-slug" className="arabic-text">الرابط المختصر (مطلوب)</Label>
              <Input id="category-slug"
                value={categoryFormData.slug}
                onChange={(e) => setCategoryFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="category-slug"
              />
            </div>
            <div>
              <Label htmlFor="category-description" className="arabic-text">الوصف</Label>
              <Input id="category-description"
                value={categoryFormData.description}
                onChange={(e) => setCategoryFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف الفئة"
                className="arabic-text"
              />
            </div>
            <div>
              <Label htmlFor="category-icon" className="arabic-text">الأيقونة</Label>
              <Input id="category-icon"
                value={categoryFormData.icon}
                onChange={(e) => setCategoryFormData(prev => ({ ...prev, icon: e.target.value }))}
                placeholder="🏷️"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="category-active"
                checked={categoryFormData.is_active}
                onChange={(e) => setCategoryFormData(prev => ({ ...prev, is_active: e.target.checked }))}
              />
              <Label htmlFor="category-active" className="arabic-text">فئة نشطة</Label>
            </div>
            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={resetCategoryForm}>
                إلغاء
              </Button>
              <Button onClick={handleFileUploadCategory}>
                {editingCategory ? 'تحديث' : 'إضافة'}
              </Button>
            </div>
          </div>
        </TabsContent>
      </div>

      {/* Add Product */}
      <Dialog open={showAddProduct} onOpenChange={setShowAddProduct}>
        < Content className="max-w-4xl max-h-[90vh] overflow-y-auto">
          < Header>
            < Title className="arabic-text">إضافة منتج جديد</ Title>
            < Description className="arabic-text">
              أدخل تفاصيل المنتج الجديد
            </ Description>
          </ Header>
          <ProductForm
            onSubmit={handleAddProduct}
            onCancel={() => setShowAddProduct(false)}
          />
        </TabsContent>
      </div>

      {/* Product */}
      < Product product={editingProduct}
        open={show }
        onOpenChange={setShow }
        on ={handleUpdateProduct}
      />

      {/* Delete Confirmation */}
      <Confirm open={deleteConfirm.open}
        onOpenChange={(open) => setDeleteConfirm(prev => ({ ...prev, open }))}
        title="تأكيد حذف المنتج"
        description={`هل أنت متأكد من حذف المنتج "${deleteConfirm.productName}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
        confirmText="حذف"
        cancelText="إلغاء"
        variant="destructive"
        onConfirm={handleDeleteProduct}
      />

      {/* Toast Container */}
      <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} />
    </div>
  )
}
