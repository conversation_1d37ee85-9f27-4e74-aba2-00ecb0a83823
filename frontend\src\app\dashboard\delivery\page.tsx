
"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import {  Content, List, Trigger } from '@/components/ui/tabs'

import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Clock, Truck } from 'lucide-react'

  Truck, Clock, Navigation as NavigationIcon, Route, Search } from 'lucide-react'

// أنواع البيانات
interface DeliveryOrder {
  id: string
  customer_name: string
  customer_phone: string
  address: string
  items: string[]
  total_value: number
  status: 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'failed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  pickup_time?: string
  delivery_time?: string
  estimated_delivery: string
  distance: number
  notes?: string
}

interface DeliveryStats {
  total_deliveries: number
  completed_today: number
  pending_deliveries: number
  total_earnings: number
  average_rating: number
  total_distance: number
}

export default function DeliveryDashboard() {
  const { } = useAuth() // ✅ إصلاح: إزالة متغيرات غير مستخدمة
  const [activeTab, setActiveTab] = useState('overview')
  const [orders, setOrders] = useState<DeliveryOrder[]>([])
  const [stats, setStats] = useState<DeliveryStats>({
    total_deliveries: 0,
    completed_today: 0,
    pending_deliveries: 0,
    total_earnings: 0,
    average_rating: 0,
    total_distance: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockOrders: DeliveryOrder[] = [
      {
        id: 'DEL001',
        customer_name: 'أحمد محمد علي',
        customer_phone: '+971501234567',
        address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة',
        items: ['زي التخرج الكلاسيكي', 'قبعة التخرج'],
        total_value: 389.99,
        status: 'assigned',
        priority: 'high',
        estimated_delivery: '2024-01-20T14:00:00Z',
        distance: 12.5,
        notes: 'يرجى الاتصال قبل الوصول'
      },
      {
        id: 'DEL002',
        customer_name: 'فاطمة أحمد حسن',
        customer_phone: '+971507654321',
        address: 'منطقة الجميرا، دبي، الإمارات العربية المتحدة',
        items: ['زي التخرج المميز'],
        total_value: 299.99,
        status: 'in_transit',
        priority: 'medium',
        pickup_time: '2024-01-20T10:30:00Z',
        estimated_delivery: '2024-01-20T12:00:00Z',
        distance: 8.2
      },
      {
        id: 'DEL003',
        customer_name: 'محمد عبدالله سالم',
        customer_phone: '+971509876543',
        address: 'شارع الوحدة، أبوظبي، الإمارات العربية المتحدة',
        items: ['زي التخرج الفاخر', 'إكسسوارات التخرج'],
        total_value: 567.99,
        status: 'delivered',
        priority: 'low',
        pickup_time: '2024-01-20T08:00:00Z',
        delivery_time: '2024-01-20T10:15:00Z',
        estimated_delivery: '2024-01-20T10:30:00Z',
        distance: 45.3
      }
    ]

    const mockStats: DeliveryStats = {
      total_deliveries: 156,
      completed_today: 8,
      pending_deliveries: mockOrders.filter(o => o.status !== 'delivered' && o.status !== 'failed').length,
      total_earnings: 2450.75,
      average_rating: 4.8,
      total_distance: 1247.6
    }

    setOrders(mockOrders)
    setStats(mockStats)
    setLoading(false)
  }, [])

  const getStatusColor = (status: DeliveryOrder['status']) => {
    switch (status) {
      case 'assigned': return 'bg-blue-100 text-blue-800'
      case 'picked_up': return 'bg-yellow-100 text-yellow-800'
      case 'in_transit': return 'bg-purple-100 text-purple-800'
      case 'delivered': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: DeliveryOrder['status']) => {
    switch (status) {
      case 'assigned': return 'مُعيَّن'
      case 'picked_up': return 'تم الاستلام'
      case 'in_transit': return 'في الطريق'
      case 'delivered': return 'تم التسليم'
      case 'failed': return 'فشل التسليم'
      default: return 'غير معروف'
    }
  }

  const getPriorityColor = (priority: DeliveryOrder['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityText = (priority: DeliveryOrder['priority']) => {
    switch (priority) {
      case 'urgent': return 'عاجل'
      case 'high': return 'عالي'
      case 'medium': return 'متوسط'
      case 'low': return 'منخفض'
      default: return 'غير محدد'
    }
  }

  const filteredOrders = orders.filter(order =>
    order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.address.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-yellow-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-yellow-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                لوحة تحكم التوصيل 🚚
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة طلبات التوصيل والمسارات
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <NavigationIcon className="h-4 w-4 mr-2" />
                فتح الخريطة
              </Button>
              <Button size="sm">
                <div className="h-4 w-4 mr-2" />
                طلب جديد
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي التوصيلات
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_deliveries}
                  </p>
                </div>
                <div className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    مكتمل اليوم
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.completed_today}
                  </p>
                </div>
                <div className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    في الانتظار
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.pending_deliveries}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    الأرباح اليوم
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_earnings} Dhs
                  </p>
                </div>
                <div className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    التقييم
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.average_rating} ⭐
                  </p>
                </div>
                <div className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    المسافة الكلية
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total_distance} كم
                  </p>
                </div>
                <Route className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Select value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="arabic-text">
              <Truck className="h-4 w-4 mr-2" />
              نظرة عامة
            </TabsTrigger>
            <SelectTrigger value="orders" className="arabic-text">
              <div className="h-4 w-4 mr-2" />
              الطلبات
            </TabsTrigger>
            <TabsTrigger value="routes" className="arabic-text">
              <Route className="h-4 w-4 mr-2" />
              المسارات
            </TabsTrigger>
            <TabsTrigger value="earnings" className="arabic-text">
              <div className="h-4 w-4 mr-2" />
              الأرباح
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Today's Schedule */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">جدول اليوم</CardTitle>
                <div className="arabic-text">
                  الطلبات المجدولة لليوم الحالي
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orders.filter(o => o.status !== 'delivered' && o.status !== 'failed').map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                          <div className="h-6 w-6 text-orange-600" />
                        </div>
                        <div>
                          <p className="font-medium arabic-text">{order.customer_name}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {order.address.substring(0, 50)}...
                          </p>
                          <div className="flex gap-2 mt-1">
                            < className={getStatusColor(order.status)}>
                              {getStatusText(order.status)}
                            </div>
                            < className={getPriorityColor(order.priority)}>
                              {getPriorityText(order.priority)}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{order.total_value} Dhs</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {order.distance} كم
                        </p>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <div className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <NavigationIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <div className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">أداء التوصيل الأسبوعي</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <div className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للأداء</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">توزيع الطلبات حسب المنطقة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <div className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">خريطة التوزيع</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-6 mt-6">
            {/* Search and s */}
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 arabic-text"
                  />
                </div>
              </div>
              <Button variant="outline">
                <div className="h-4 w-4 mr-2" />
                فلترة
              </Button>
            </div>

            {/* Orders List */}
            <div className="grid gap-6">
              {filteredOrders.map((order) => (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="arabic-text">طلب #{order.id}</CardTitle>
                        <div className="arabic-text">
                          {order.customer_name} - {order.customer_phone}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        < className={getStatusColor(order.status)}>
                          {getStatusText(order.status)}
                        </div>
                        < className={getPriorityColor(order.priority)}>
                          {getPriorityText(order.priority)}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium arabic-text mb-1">العنوان:</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                          {order.address}
                        </p>
                      </div>

                      <div>
                        <p className="text-sm font-medium arabic-text mb-2">العناصر:</p>
                        <div className="flex flex-wrap gap-2">
                          {order.items.map((item, index) => (
                            <Badge key={index} variant="outline" className="arabic-text">
                              {item}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium arabic-text">القيمة:</p>
                          <p className="text-lg font-bold text-green-600">{order.total_value} Dhs</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium arabic-text">المسافة:</p>
                          <p className="text-lg font-bold text-blue-600">{order.distance} كم</p>
                        </div>
                      </div>

                      {order.notes && (
                        <div>
                          <p className="text-sm font-medium arabic-text mb-1">ملاحظات:</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {order.notes}
                          </p>
                        </div>
                      )}

                      <div className="flex gap-2 pt-3 border-t">
                        <Button variant="outline" size="sm">
                          <div className="h-4 w-4 mr-2" />
                          اتصال
                        </Button>
                        <Button variant="outline" size="sm">
                          <NavigationIcon className="h-4 w-4 mr-2" />
                          التنقل
                        </Button>
                        <Button variant="outline" size="sm">
                          <div className="h-4 w-4 mr-2" />
                          التفاصيل
                        </Button>
                        {order.status === 'assigned' && (
                          <Button size="sm">
                            <div className="h-4 w-4 mr-2" />
                            بدء التوصيل
                          </Button>
                        )}
                        {order.status === 'in_transit' && (
                          <Button size="sm">
                            <div className="h-4 w-4 mr-2" />
                            تأكيد التسليم
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </div>
      </main>
    </div>
  )
}
