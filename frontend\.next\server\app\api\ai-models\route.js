(()=>{var e={};e.id=6139,e.ids=[6139],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38561:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m Expected ',', got 'interface'\n    ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\lib\\mockData.ts\x1b[0m:7:1]\n \x1b[2m 4\x1b[0m │ \n \x1b[2m 5\x1b[0m │ // بيانات وهمية للتطوير والاختبار\n \x1b[2m 6\x1b[0m │ \n \x1b[2m 7\x1b[0m │ export interface MockPage {\n    \xb7 \x1b[35;1m       ─────────\x1b[0m\n \x1b[2m 8\x1b[0m │   id: string\n \x1b[2m 9\x1b[0m │   slug: string\n \x1b[2m10\x1b[0m │   is_published: boolean\n    ╰────\n\n\nCaused by:\n    Syntax Error")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64607:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>u,POST:()=>l,PUT:()=>c});var n=s(96559),i=s(48088),o=s(37719),a=s(32190),d=s(38561);async function u(e){try{let{searchParams:t}=new URL(e.url),s=t.get("provider"),r=t.get("type"),n=t.get("status"),i="true"===t.get("include_inactive"),o=parseInt(t.get("page")||"1"),u=parseInt(t.get("limit")||"10"),l=d.C.getAIModels();s&&(l=l.filter(e=>e.provider===s)),r&&(l=l.filter(e=>e.type===r)),n&&(l=l.filter(e=>e.status===n)),i||(l=l.filter(e=>e.isActive));let c=(o-1)*u,p=l.slice(c,c+u),g={total:l.length,active:l.filter(e=>e.isActive).length,inactive:l.filter(e=>!e.isActive).length,byProvider:l.reduce((e,t)=>(e[t.provider]=(e[t.provider]||0)+1,e),{}),byType:l.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{})};return a.NextResponse.json({models:p,total:l.length,page:o,limit:u,totalPages:Math.ceil(l.length/u),stats:g})}catch(e){return a.NextResponse.json({error:"خطأ في جلب نماذج الذكاء الاصطناعي"},{status:500})}}async function l(e){try{let{name:t,provider:s,type:r,description:n,apiKey:i,apiEndpoint:o,baseUrl:u,settings:l,selectedModels:c,subModels:p}=await e.json();if(!s)return a.NextResponse.json({error:"مقدم الخدمة مطلوب"},{status:400});if(!u)return a.NextResponse.json({error:"Base URL مطلوب"},{status:400});if(!c||0===c.length)return a.NextResponse.json({error:"يجب تحديد نموذج واحد على الأقل"},{status:400});let g=d.C.getAIModels();if(g.find(e=>e.provider===s))return a.NextResponse.json({error:`مقدم الخدمة ${s} موجود بالفعل. يمكنك تحرير الإعدادات الموجودة أو حذف المقدم الحالي أولاً.`},{status:409});let m=t||({openai:"OpenAI",anthropic:"Anthropic Claude",google:"Google Gemini",meta:"Meta LLaMA",stability:"Stability AI",cohere:"Cohere",huggingface:"Hugging Face",deepseek:"DeepSeek"})[s]||s,x=r||function(e){let t=e.some(e=>e.includes("dall-e")||e.includes("stable-diffusion")||e.includes("vision")),s=e.some(e=>e.includes("whisper")||e.includes("tts")),r=e.some(e=>e.includes("embed")),n=e.some(e=>e.includes("code")||e.includes("coder"));return e.some(e=>e.includes("vision")||e.includes("multimodal"))?"multimodal":t?"image":s?"audio":r?"embedding":n?"code":"text"}(c||[]),v={id:d.C.generateId(),name:m,provider:s,type:x,description:n||`نماذج ${m} للذكاء الاصطناعي`,subModels:p||[],selectedModels:c||[],apiKey:i,baseUrl:u,isActive:!0,status:"inactive",settings:l||{temperature:.7,maxTokens:2048,topP:1,frequencyPenalty:0,presencePenalty:0},usage:{totalRequests:0,totalTokens:0,totalCost:0,dailyUsage:[],monthlyUsage:[],averageResponseTime:0,successRate:0},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};g.push(v),d.C.saveAIModels(g);let f=d.C.getModelActivities();return f.push({id:d.C.generateId(),modelId:v.id,type:"config_change",description:`تم إضافة مقدم خدمة جديد: ${m}`,timestamp:new Date().toISOString(),success:!0}),d.C.saveModelActivities(f),a.NextResponse.json({message:"تم إضافة النموذج بنجاح",model:v},{status:201})}catch(e){return a.NextResponse.json({error:"خطأ في إنشاء النموذج"},{status:500})}}async function c(e){try{let{action:t,modelIds:s,settings:r}=await e.json();if(!t||!s||!Array.isArray(s))return a.NextResponse.json({error:"الإجراء ومعرفات النماذج مطلوبة"},{status:400});let n=d.C.getAIModels(),i=d.C.getModelActivities(),o=0;for(let e of s){let s=n.findIndex(t=>t.id===e);if(-1===s)continue;let a=n[s],u="";switch(t){case"activate":a.isActive=!0,a.status="active",u=`تم تفعيل النموذج: ${a.name}`;break;case"deactivate":a.isActive=!1,a.status="inactive",u=`تم إلغاء تفعيل النموذج: ${a.name}`;break;case"update_settings":r&&(a.settings={...a.settings,...r},u=`تم تحديث إعدادات النموذج: ${a.name}`);break;default:continue}a.updatedAt=new Date().toISOString(),n[s]=a,o++,i.push({id:d.C.generateId(),modelId:a.id,type:"config_change",description:u,timestamp:new Date().toISOString(),success:!0})}return d.C.saveAIModels(n),d.C.saveModelActivities(i),a.NextResponse.json({message:`تم تحديث ${o} نموذج بنجاح`,updatedCount:o})}catch(e){return a.NextResponse.json({error:"خطأ في تحديث النماذج"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),s=t.get("ids")?.split(",")||[];if(0===s.length)return a.NextResponse.json({error:"معرفات النماذج مطلوبة"},{status:400});let r=d.C.getAIModels(),n=d.C.getModelActivities(),i=0,o=r.filter(e=>!s.includes(e.id)||(n.push({id:d.C.generateId(),modelId:e.id,type:"config_change",description:`تم حذف النموذج: ${e.name}`,timestamp:new Date().toISOString(),success:!0}),i++,!1));return d.C.saveAIModels(o),d.C.saveModelActivities(n),a.NextResponse.json({message:`تم حذف ${i} نموذج بنجاح`,deletedCount:i})}catch(e){return a.NextResponse.json({error:"خطأ في حذف النماذج"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai-models/route",pathname:"/api/ai-models",filename:"route",bundlePath:"app/api/ai-models/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:v}=g;function f(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(64607));module.exports=r})();